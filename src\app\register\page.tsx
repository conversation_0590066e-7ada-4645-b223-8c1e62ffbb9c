import { redirect } from "next/navigation";
import { createClient } from "@/utils/supabase/server";
import { RegisterForm } from "@/components/auth/register-form";
import { StructuredData } from "@/components/seo/structured-data";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Kayıt Ol | Sportiva - Spor Salonu Yönetim Platformu",
  description:
    "Sportiva'ya ücretsiz kayıt olun. Spor salonunuzu dijitalleştirin, üyeliklerinizi yönetin ve işletmenizi büyütün.",
  keywords: [
    "sportiva kayıt",
    "spor salonu kayıt",
    "ücretsiz kayıt",
    "fitness kayıt",
    "spor salonu yönetimi",
    "dijital spor salonu",
    "üyelik yönetimi",
  ],
  openGraph: {
    title: "Kayıt Ol | Sportiva",
    description:
      "Sportiva'ya ücretsiz kayıt olun ve spor salonu işletmenizi dijitalleştirin.",
    type: "website",
    locale: "tr_TR",
    siteName: "Sportiva",
  },
  twitter: {
    card: "summary",
    title: "Kayıt Ol | Sportiva",
    description:
      "Sportiva'ya ücretsiz kayıt olun ve spor salonu işletmenizi dijitalleştirin.",
  },
  robots: {
    index: true,
    follow: true,
  },
  alternates: {
    canonical: "/register",
  },
};

export default async function RegisterPage() {
  const supabase = await createClient();

  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (user) {
    redirect("/dashboard");
  }

  return (
    <>
      <StructuredData type="RegisterAction" />
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 via-white to-gray-100 dark:from-gray-900 dark:via-gray-800 dark:to-black">
        <div className="max-w-md w-full space-y-8 p-8">
          <RegisterForm />
        </div>
      </div>
    </>
  );
}

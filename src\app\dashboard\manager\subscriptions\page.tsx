"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/components/auth/auth-provider";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Calendar,
  CreditCard,
  Crown,
  Star,
  Zap,
  ArrowRight,
  ExternalLink,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Loader2,
} from "lucide-react";
import { cn } from "@/lib/utils";
import {
  getCurrentManagerSubscription,
  getManagerSubscriptionHistory,
} from "@/app/actions/subscription-actions";

interface CurrentSubscription {
  tier: string;
  duration: string;
  packageName: string;
  maxGyms: number | null;
  maxMembers: number | null;
  features: Record<string, any>;
  subscriptionEndDate: string;
  subscriptionStartDate: string;
  status: string;
  priceAmount: number;
}

interface SubscriptionHistory {
  tier: string;
  duration: string;
  packageName: string;
  priceAmount: number;
  subscriptionStartDate: string;
  subscriptionEndDate: string;
  status: string;
  isActive: boolean;
}

export default function SubscriptionsPage() {
  const { authUser } = useAuth();
  const router = useRouter();
  const [currentSubscription, setCurrentSubscription] =
    useState<CurrentSubscription | null>(null);
  const [subscriptionHistory, setSubscriptionHistory] = useState<
    SubscriptionHistory[]
  >([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadSubscriptionData = async () => {
      if (!authUser) {
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        // Mevcut abonelik bilgilerini yükle
        const currentResponse = await getCurrentManagerSubscription(
          authUser.id
        );
        if (currentResponse.success && currentResponse.data) {
          setCurrentSubscription(currentResponse.data);
        }

        // Abonelik geçmişini yükle
        const historyResponse = await getManagerSubscriptionHistory(
          authUser.id
        );
        if (historyResponse.success && historyResponse.data) {
          setSubscriptionHistory(historyResponse.data);
        }
      } catch (err) {
        console.error("Error loading subscription data:", err);
        setError("Abonelik bilgileri yüklenirken bir hata oluştu.");
      } finally {
        setIsLoading(false);
      }
    };

    loadSubscriptionData();
  }, [authUser]);

  const getTierIcon = (tier: string) => {
    switch (tier) {
      case "starter":
        return <Star className="h-5 w-5" />;
      case "professional":
        return <Zap className="h-5 w-5" />;
      case "enterprise":
        return <Crown className="h-5 w-5" />;
      default:
        return <Star className="h-5 w-5" />;
    }
  };

  const getTierColor = (tier: string) => {
    switch (tier) {
      case "starter":
        return "bg-blue-500";
      case "professional":
        return "bg-green-500";
      case "enterprise":
        return "bg-purple-500";
      default:
        return "bg-gray-500";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "active":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "expired":
        return <XCircle className="h-4 w-4 text-red-500" />;
      case "cancelled":
        return <XCircle className="h-4 w-4 text-red-500" />;
      case "pending_payment":
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "active":
        return "Aktif";
      case "expired":
        return "Süresi Dolmuş";
      case "cancelled":
        return "İptal Edilmiş";
      case "pending_payment":
        return "Ödeme Bekliyor";
      case "failed":
        return "Başarısız";
      default:
        return status;
    }
  };

  const getDurationText = (duration: string) => {
    switch (duration) {
      case "monthly":
        return "Aylık";
      case "quarterly":
        return "3 Aylık";
      case "yearly":
        return "Yıllık";
      default:
        return duration;
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return "Belirtilmemiş";
    return new Date(dateString).toLocaleDateString("tr-TR", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const isSubscriptionExpiringSoon = (endDate: string) => {
    if (!endDate) return false;
    const end = new Date(endDate);
    const now = new Date();
    const daysUntilExpiry = Math.ceil(
      (end.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)
    );
    return daysUntilExpiry <= 30 && daysUntilExpiry > 0;
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="flex items-center gap-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Abonelik bilgileri yükleniyor...</span>
        </div>
      </div>
    );
  }

  if (!authUser) {
    return (
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          Abonelik bilgilerinizi görüntülemek için giriş yapmanız gerekiyor.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Platform Aboneliğim
          </h1>
          <p className="text-muted-foreground">
            Mevcut abonelik durumunuz ve geçmiş abonelik bilgileriniz
          </p>
        </div>
        <Button onClick={() => router.push("/pricing")} className="gap-2">
          <ExternalLink className="h-4 w-4" />
          Paket Değiştir
        </Button>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Current Subscription */}
      {currentSubscription ? (
        <Card className="border-2">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div
                  className={cn(
                    "h-10 w-10 rounded-full flex items-center justify-center text-white",
                    getTierColor(currentSubscription.tier)
                  )}
                >
                  {getTierIcon(currentSubscription.tier)}
                </div>
                <div>
                  <CardTitle className="text-xl">
                    {currentSubscription.packageName}
                  </CardTitle>
                  <CardDescription>
                    {getDurationText(currentSubscription.duration)} Abonelik
                  </CardDescription>
                </div>
              </div>
              <div className="flex items-center gap-2">
                {getStatusIcon(currentSubscription.status)}
                <Badge
                  variant={
                    currentSubscription.status === "active"
                      ? "default"
                      : "secondary"
                  }
                >
                  {getStatusText(currentSubscription.status)}
                </Badge>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Subscription Details */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <p className="text-sm font-medium text-muted-foreground">
                  Başlangıç Tarihi
                </p>
                <p className="text-sm">
                  {formatDate(currentSubscription.subscriptionStartDate)}
                </p>
              </div>
              <div className="space-y-2">
                <p className="text-sm font-medium text-muted-foreground">
                  Bitiş Tarihi
                </p>
                <p className="text-sm">
                  {formatDate(currentSubscription.subscriptionEndDate)}
                </p>
              </div>
              <div className="space-y-2">
                <p className="text-sm font-medium text-muted-foreground">
                  Aylık Ücret
                </p>
                <p className="text-sm font-semibold">
                  ₺{currentSubscription.priceAmount}
                </p>
              </div>
            </div>

            {/* Package Limits */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t">
              <div className="flex items-center gap-2">
                <CreditCard className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">
                  <span className="font-medium">Salon Limiti:</span>{" "}
                  {currentSubscription.maxGyms
                    ? `${currentSubscription.maxGyms} salon`
                    : "Sınırsız"}
                </span>
              </div>
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">
                  <span className="font-medium">Üye Limiti:</span>{" "}
                  {currentSubscription.maxMembers
                    ? `${currentSubscription.maxMembers} üye`
                    : "Sınırsız"}
                </span>
              </div>
            </div>

            {/* Expiry Warning */}
            {isSubscriptionExpiringSoon(
              currentSubscription.subscriptionEndDate
            ) && (
              <Alert className="border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-950">
                <AlertTriangle className="h-4 w-4 text-yellow-600" />
                <AlertDescription className="text-yellow-800 dark:text-yellow-200">
                  Aboneliğinizin süresi yakında dolacak. Kesintisiz hizmet için
                  paketinizi yenilemeyi unutmayın.
                </AlertDescription>
              </Alert>
            )}

            {/* Action Buttons */}
            <div className="flex gap-2 pt-4">
              <Button
                onClick={() => router.push("/pricing")}
                variant="outline"
                className="gap-2"
              >
                <ArrowRight className="h-4 w-4" />
                Paket Yükselt
              </Button>
              <Button
                onClick={() => router.push("/dashboard/manager")}
                variant="outline"
              >
                Yönetici Paneli
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Henüz aktif bir platform aboneliğiniz bulunmuyor. Salon yönetimi
            için bir paket seçmeniz gerekiyor.
          </AlertDescription>
        </Alert>
      )}

      {/* Subscription History */}
      {subscriptionHistory.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Abonelik Geçmişi
            </CardTitle>
            <CardDescription>
              Geçmiş platform abonelik kayıtlarınız
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {subscriptionHistory.map((subscription, index) => (
                <div
                  key={index}
                  className={cn(
                    "flex items-center justify-between p-4 rounded-lg border",
                    subscription.isActive
                      ? "bg-green-50 border-green-200 dark:bg-green-950 dark:border-green-800"
                      : "bg-gray-50 border-gray-200 dark:bg-gray-900 dark:border-gray-700"
                  )}
                >
                  <div className="flex items-center gap-3">
                    <div
                      className={cn(
                        "h-8 w-8 rounded-full flex items-center justify-center text-white text-sm",
                        getTierColor(subscription.tier)
                      )}
                    >
                      {getTierIcon(subscription.tier)}
                    </div>
                    <div>
                      <p className="font-medium">{subscription.packageName}</p>
                      <p className="text-sm text-muted-foreground">
                        {getDurationText(subscription.duration)} •{" "}
                        {formatDate(subscription.subscriptionStartDate)} -{" "}
                        {formatDate(subscription.subscriptionEndDate)}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="text-right">
                      <p className="font-semibold">
                        ₺{subscription.priceAmount}
                      </p>
                      <div className="flex items-center gap-1">
                        {getStatusIcon(subscription.status)}
                        <span className="text-sm text-muted-foreground">
                          {getStatusText(subscription.status)}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Hızlı İşlemler</CardTitle>
          <CardDescription>
            Platform aboneliğinizle ilgili işlemler
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Button
              onClick={() => router.push("/pricing")}
              variant="outline"
              className="h-auto p-4 justify-start gap-3"
            >
              <div className="h-8 w-8 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                <ArrowRight className="h-4 w-4 text-blue-600 dark:text-blue-400" />
              </div>
              <div className="text-left">
                <p className="font-medium">Paket Değiştir</p>
                <p className="text-sm text-muted-foreground">
                  Farklı bir pakete geçiş yapın
                </p>
              </div>
            </Button>

            <Button
              onClick={() => router.push("/dashboard/manager")}
              variant="outline"
              className="h-auto p-4 justify-start gap-3"
            >
              <div className="h-8 w-8 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center">
                <CreditCard className="h-4 w-4 text-green-600 dark:text-green-400" />
              </div>
              <div className="text-left">
                <p className="font-medium">Yönetici Paneli</p>
                <p className="text-sm text-muted-foreground">
                  Salon yönetim paneline git
                </p>
              </div>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

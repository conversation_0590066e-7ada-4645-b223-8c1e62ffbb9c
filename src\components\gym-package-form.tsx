"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Loader2 } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import {
  createGymPackage,
  updateGymPackage,
} from "@/app/actions/package-actions";
import { Tables } from "@/lib/supabase/types";

// Form validasyon şeması
const packageFormSchema = z.object({
  name: z
    .string()
    .min(1, "Paket adı gereklidir")
    .max(100, "Paket adı en fazla 100 karakter olabilir"),
  package_type: z.enum(["monthly", "quarterly", "yearly", "daily", "trial"], {
    errorMap: () => ({ message: "Geçerli bir paket türü seçiniz" }),
  }),
  duration_days: z.coerce.number().int().positive().optional(),
  price_amount: z.coerce.number().positive("Fiyat 0'dan büyük olmalıdır"),
  description: z
    .string()
    .max(500, "Açıklama en fazla 500 karakter olabilir")
    .optional(),
  is_active: z.boolean(),
});

type PackageFormValues = z.infer<typeof packageFormSchema>;

interface GymPackageFormProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  gymId: string;
  onSuccess?: () => void;
  editPackage?: Tables<"gym_packages"> | null;
}

export function GymPackageForm({
  open,
  onOpenChange,
  gymId,
  onSuccess,
  editPackage,
}: GymPackageFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const form = useForm<PackageFormValues>({
    resolver: zodResolver(packageFormSchema),
    defaultValues: {
      name: editPackage?.name || "",
      package_type: editPackage?.package_type || "monthly",
      duration_days: editPackage?.duration_days || undefined,
      price_amount: editPackage?.price_amount || 0,
      description: editPackage?.description || "",
      is_active: editPackage?.is_active ?? true,
    },
  });

  const packageType = form.watch("package_type");

  // editPackage değiştiğinde form değerlerini güncelle
  useEffect(() => {
    if (editPackage) {
      form.reset({
        name: editPackage.name,
        package_type: editPackage.package_type,
        duration_days: editPackage.duration_days || undefined,
        price_amount: editPackage.price_amount,
        description: editPackage.description || "",
        is_active: editPackage.is_active,
      });
    } else {
      form.reset({
        name: "",
        package_type: "monthly",
        duration_days: undefined,
        price_amount: 0,
        description: "",
        is_active: true,
      });
    }
  }, [editPackage, form]);

  const onSubmit = async (values: PackageFormValues) => {
    setIsLoading(true);
    try {
      // Paket türüne göre duration_days ayarla
      let packageData = {
        name: values.name,
        package_type: values.package_type,
        price_amount: values.price_amount,
        description: values.description || null,
        is_active: values.is_active,
        duration_days: null as number | null,
      };

      // Paket türüne göre süre ayarla
      if (["monthly", "quarterly", "yearly"].includes(values.package_type)) {
        if (values.package_type === "monthly") packageData.duration_days = 30;
        else if (values.package_type === "quarterly")
          packageData.duration_days = 90;
        else if (values.package_type === "yearly")
          packageData.duration_days = 365;

        // Kullanıcı özel süre girdiyse onu kullan
        if (values.duration_days !== undefined) {
          packageData.duration_days = values.duration_days;
        }
      } else if (["daily", "trial"].includes(values.package_type)) {
        // Süreyi ayarla
        if (values.duration_days !== undefined) {
          packageData.duration_days = values.duration_days;
        }
      }

      let response;
      if (editPackage) {
        // Düzenleme modu
        response = await updateGymPackage(editPackage.id, packageData);
      } else {
        // Yeni paket oluşturma modu
        response = await createGymPackage({
          gym_id: gymId,
          ...packageData,
        });
      }

      if (response.success) {
        toast({
          title: "Başarılı",
          description: editPackage
            ? "Paket başarıyla güncellendi."
            : "Paket başarıyla oluşturuldu.",
        });
        form.reset();
        onOpenChange(false);
        onSuccess?.();
      } else {
        toast({
          title: "Hata",
          description:
            response.error ||
            (editPackage
              ? "Paket güncellenirken bir hata oluştu."
              : "Paket oluşturulurken bir hata oluştu."),
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Paket işlemi hatası:", error);
      toast({
        title: "Hata",
        description: "Beklenmeyen bir hata oluştu.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>
            {editPackage ? "Paket Düzenle" : "Yeni Paket Ekle"}
          </DialogTitle>
          <DialogDescription>
            {editPackage
              ? "Mevcut paketi düzenleyin."
              : "Salonunuz için yeni bir üyelik paketi oluşturun."}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Paket Adı</FormLabel>
                  <FormControl>
                    <Input placeholder="Örn: Aylık Üyelik" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="package_type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Paket Türü</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Paket türü seçin" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="monthly">Aylık</SelectItem>
                      <SelectItem value="quarterly">3 Aylık</SelectItem>
                      <SelectItem value="yearly">Yıllık</SelectItem>
                      <SelectItem value="daily">Günlük</SelectItem>
                      <SelectItem value="trial">Deneme</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="price_amount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Fiyat (₺)</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      step="0.01"
                      placeholder="0.00"
                      {...field}
                      value={field.value || ""}
                      onChange={(e) => {
                        const value = e.target.value;
                        field.onChange(value === "" ? 0 : Number(value));
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {["monthly", "quarterly", "yearly", "daily", "trial"].includes(
              packageType
            ) && (
              <FormField
                control={form.control}
                name="duration_days"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Süre (Gün)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="Gün sayısı"
                        {...field}
                        value={field.value || ""}
                        onChange={(e) => {
                          const value = e.target.value;
                          field.onChange(
                            value === "" ? undefined : Number(value)
                          );
                        }}
                      />
                    </FormControl>
                    <FormDescription>
                      {packageType === "monthly" && "Varsayılan: 30 gün"}
                      {packageType === "quarterly" && "Varsayılan: 90 gün"}
                      {packageType === "yearly" && "Varsayılan: 365 gün"}
                      {["daily", "trial"].includes(packageType) &&
                        "Paket geçerlilik süresi"}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Açıklama (Opsiyonel)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Paket hakkında açıklama..."
                      className="resize-none"
                      {...field}
                      value={field.value || ""}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="is_active"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                  <div className="space-y-0.5">
                    <FormLabel>Aktif</FormLabel>
                    <FormDescription>
                      Paket satışa açık olsun mu?
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isLoading}
              >
                İptal
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {editPackage ? "Paketi Güncelle" : "Paket Oluştur"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

import { Metadata } from "next";
import { PricingClient } from "@/components/pricing/pricing-client";

export const metadata: Metadata = {
  title: "Fiyatlandırma - Sportiva | Spor Salonu Yönetim Sistemi",
  description: "Sportiva spor salonu yönetim sistemi fiyatlandırma paketleri. Starter, Professional ve Enterprise paketleri ile salonunuzu dijitalleştirin. Aylık, 3 aylık ve yıllık ödeme seçenekleri.",
  keywords: [
    "spor salonu yönetim sistemi fiyat",
    "gym management system pricing",
    "salon yönetimi paket fiyatları",
    "fitness center software pricing",
    "spor salonu yazılımı fiyat",
    "gym software packages",
    "salon dijitalleştirme fiyat",
    "fitness management system cost"
  ],
  openGraph: {
    title: "Fiyatlandırma - Sportiva",
    description: "Salonunuz için en uygun paketi seçin. Starter, Professional ve Enterprise paketleri ile salonunuzu dijitalleştirin.",
    type: "website",
    locale: "tr_TR",
  },
  alternates: {
    canonical: "/pricing"
  }
};

export default function PricingPage() {
  return <PricingClient />;
}

"use server";

import { createClient } from "@/utils/supabase/server";
import { createAction, validateFormData } from "@/lib/actions/core";
import { ApiResponse, ErrorMessages } from "@/lib/actions/types";
import { getSupabaseAdmin } from "@/utils/supabase/admin";
import {
  passwordResetSchema,
  passwordUpdateSchema,
  userUpdateSchema,
} from "@/lib/schemas/schemas";
import { logSuccess, logError } from "@/lib/audit-logger";

/**
 * Kullanıcı profilini getirir
 * @param userId Kullanıcı ID'si
 * @returns Kullanıcı profili veya hata
 */
export async function getUserProfile(userId: string) {
  return await createAction(async (_, supabase) => {
    const { data, error } = await supabase
      .from("users")
      .select("*")
      .eq("id", userId)
      .maybeSingle();

    if (error && error.code === "PGRST116") {
      throw new Error(`Kullanıcı profili getirilirken hata: ${error.message}`);
    }
    return data;
  });
}

/**
 * <PERSON><PERSON><PERSON>cı profilini günceller
 * Bu fonksiyon hem FormData hem de doğrudan obje parametresi alabilir
 */
export async function updateUserProfile(
  userIdOrFormData: string | FormData,
  userData?: {
    name: string;
    surname: string;
    profile_picture_url?: string | null;
    height_cm?: number | null;
    weight_kg?: number | null;
    gender?: string | null;
    fitness_goal?: string | null;
    phone?: string | null;
    age?: number | null;
  }
): Promise<ApiResponse> {
  // FormData olarak gönderilmişse
  if (userIdOrFormData instanceof FormData) {
    const formData = userIdOrFormData;
    const { data, error } = await validateFormData(formData, userUpdateSchema);

    if (error || !data) {
      return { success: false, error: error || "Geçersiz form verisi" };
    }

    const { userId, name, surname, phone, age, gender } = data;

    return await createAction(
      async (_, supabase, authUserId) => {
        // Yetki kontrolü
        if (authUserId !== userId) {
          throw new Error(ErrorMessages.FORBIDDEN);
        }

        // Profil güncelleme
        const { data: userProfile, error: updateError } = await supabase
          .from("users")
          .update({
            name,
            surname,
            phone: phone || null,
            age: age || null,
            gender: gender || null,
            updated_at: new Date().toISOString(),
          })
          .eq("id", userId)
          .select()
          .maybeSingle();

        if (updateError) {
          throw new Error(`Profil güncellenirken hata: ${updateError.message}`);
        }

        return userProfile;
      },
      { requireAuth: true, revalidatePaths: ["/dashboard", "/settings"] }
    );
  }
  // Doğrudan obje olarak gönderilmişse
  else if (typeof userIdOrFormData === "string" && userData) {
    const userId = userIdOrFormData;

    return await createAction(
      async (_, supabase, authUserId) => {
        // Yetki kontrolü
        if (authUserId !== userId) {
          throw new Error(ErrorMessages.FORBIDDEN);
        }

        // Profil güncelleme
        const { data: userProfile, error: updateError } = await supabase
          .from("users")
          .update({
            name: userData.name,
            surname: userData.surname,
            profile_picture_url: userData.profile_picture_url || null,
            height_cm: userData.height_cm || null,
            weight_kg: userData.weight_kg || null,
            gender: userData.gender || null,
            fitness_goal: userData.fitness_goal || null,
            phone: userData.phone || null,
            age: userData.age || null,
            updated_at: new Date().toISOString(),
          })
          .eq("id", userId)
          .select()
          .maybeSingle();

        if (updateError) {
          throw new Error(`Profil güncellenirken hata: ${updateError.message}`);
        }

        return userProfile;
      },
      { requireAuth: true, revalidatePaths: ["/dashboard", "/settings"] }
    );
  } else {
    return {
      success: false,
      error:
        "Geçersiz parametreler. Bir FormData veya userId ve userData objesi sağlamalısınız.",
    };
  }
}

/**
 * Profil resmini günceller
 */
export async function updateProfilePicture(
  formData: FormData
): Promise<ApiResponse> {
  const userId = formData.get("userId") as string;
  const profilePicture = formData.get("profilePicture") as File;

  if (!userId || !profilePicture) {
    return { success: false, error: "Kullanıcı ID'si veya profil resmi eksik" };
  }

  return await createAction(
    async (_, supabase, authUserId) => {
      // Yetki kontrolü
      if (authUserId !== userId) {
        throw new Error(ErrorMessages.FORBIDDEN);
      }

      try {
        // Resmi WebP formatına dönüştür
        const imageArrayBuffer = await profilePicture.arrayBuffer();
        const imageData = new Uint8Array(imageArrayBuffer);

        // Resmi işlemek için Sharp kullanımı
        const sharp = (await import("sharp")).default;
        const webpBuffer = await sharp(imageData)
          .resize(400, 400, { fit: "cover" }) // Resmi 400x400 boyutuna ölçeklendir
          .webp({ quality: 80 }) // WebP formatına dönüştür, %80 kalite
          .toBuffer();

        // Dosya adı oluştur
        const filePath = `${userId}.webp`;

        // WebP formatındaki dosyayı yükle
        const { data: storageData, error: storageError } =
          await supabase.storage
            .from("profile-pictures")
            .upload(filePath, webpBuffer, {
              cacheControl: "3600",
              upsert: true,
              contentType: "image/webp",
            });

        if (storageError) {
          throw new Error(`Resim yüklenirken hata: ${storageError.message}`);
        }

        // Genel URL'i al
        const { data: urlData } = await supabase.storage
          .from("profile-pictures")
          .getPublicUrl(filePath);

        const publicUrl = urlData.publicUrl;

        // Profil bilgisini güncelle
        const { data: userProfile, error: updateError } = await supabase
          .from("users")
          .update({
            profile_picture_url: publicUrl,
            updated_at: new Date().toISOString(),
          })
          .eq("id", userId)
          .select()
          .maybeSingle();

        if (updateError) {
          throw new Error(
            `Profil resmi güncellenirken hata: ${updateError.message}`
          );
        }

        return userProfile;
      } catch (error: any) {
        throw new Error(`Resim işlenirken hata: ${error.message}`);
      }
    },
    { requireAuth: true, revalidatePaths: ["/dashboard", "/settings"] }
  );
}

/**
 * Şifre sıfırlama bağlantısı gönder
 */
export async function sendPasswordResetLink(
  formData: FormData
): Promise<ApiResponse> {
  const { data, error } = await validateFormData(formData, passwordResetSchema);

  if (error || !data) {
    return { success: false, error: error || "Geçersiz form verisi" };
  }

  const { email } = data;

  return await createAction(async (_, supabase) => {
    const { error: resetError } = await supabase.auth.resetPasswordForEmail(
      email,
      { redirectTo: `${process.env.NEXT_PUBLIC_APP_URL}/auth/update-password` }
    );

    if (resetError) {
      throw new Error(`Şifre sıfırlama hatası: ${resetError.message}`);
    }

    return { email };
  });
}

/**
 * Şifreyi günceller
 */
export async function updatePassword(formData: FormData): Promise<ApiResponse> {
  const { data, error } = await validateFormData(
    formData,
    passwordUpdateSchema
  );

  if (error || !data) {
    return { success: false, error: error || "Geçersiz form verisi" };
  }

  const { userId, currentPassword, newPassword } = data;

  return await createAction(
    async (_, supabase, authUserId) => {
      // Yetki kontrolü
      if (authUserId !== userId) {
        throw new Error(ErrorMessages.FORBIDDEN);
      }

      // Mevcut şifre ile giriş kontrolü
      const { error: signInError } = await supabase.auth.signInWithPassword({
        email: "", // Bu kısmı getUserById ile almak gerekebilir
        password: currentPassword,
      });

      if (signInError) {
        throw new Error("Mevcut şifre yanlış");
      }

      // Şifre güncelleme
      const { error: updateError } = await supabase.auth.updateUser({
        password: newPassword,
      });

      if (updateError) {
        throw new Error(`Şifre güncellenirken hata: ${updateError.message}`);
      }

      return { updated: true };
    },
    { requireAuth: true }
  );
}

/**
 * Kullanıcı hesabını siler
 * @param userId Kullanıcı ID'si
 * @returns Başarı durumu veya hata
 */
export async function deleteUserAccount(userId: string) {
  try {
    const supabase = await createClient();

    // Önce kullanıcıyı public.users tablosundan sil
    const { error: deleteError } = await supabase
      .from("users")
      .delete()
      .eq("id", userId);

    if (deleteError) {
      console.error("Kullanıcı profili silinirken hata oluştu:", deleteError);
      return { error: deleteError.message };
    }

    // Sonra kullanıcıyı çıkış yaptır
    // Not: admin.deleteUser() fonksiyonu için service_role key gereklidir
    const { error } = await supabase.auth.signOut();

    if (error) {
      console.error("Kullanıcı çıkış yaparken hata oluştu:", error);
      return { error: error.message };
    }

    return { success: true };
  } catch (error: any) {
    console.error("Kullanıcı hesabı işlemi sırasında beklenmeyen hata:", error);
    return { error: "İşlem sırasında bir hata oluştu. Lütfen tekrar deneyin." };
  }
}

/**
 * Yönetici oluşturma fonksiyonu
 * @param userId Kullanıcı ID'si
 * @param packageType Abonelik paket türü
 * @returns Yönetici bilgileri veya hata
 */
export async function createManager(
  userId: string,
  packageType: "monthly" | "quarterly" | "yearly",
  tier: "starter" | "professional" | "enterprise" = "professional"
) {
  try {
    const adminClient = getSupabaseAdmin();

    // Kullanıcının zaten manager kaydı olup olmadığını kontrol et
    const { data: existingManager } = await adminClient
      .from("managers")
      .select("user_id")
      .eq("user_id", userId)
      .maybeSingle();

    if (existingManager) {
      return { error: "Bu kullanıcı zaten yönetici kaydına sahip." };
    }

    // Kullanıcının varlığını kontrol et
    const { data: userData, error: userError } = await adminClient
      .from("users")
      .select("id, name, surname, email")
      .eq("id", userId)
      .maybeSingle();

    if (userError || !userData) {
      return { error: "Kullanıcı bulunamadı." };
    }

    // Abonelik bitiş tarihini hesapla
    const startDate = new Date();
    const endDate = new Date(startDate);

    switch (packageType) {
      case "monthly":
        endDate.setMonth(endDate.getMonth() + 1);
        break;
      case "quarterly":
        endDate.setMonth(endDate.getMonth() + 3);
        break;
      case "yearly":
        endDate.setFullYear(endDate.getFullYear() + 1);
        break;
    }

    // Platform paketini al
    const { data: platformPackage, error: packageError } = await adminClient
      .from("platform_packages")
      .select("*")
      .eq("tier", tier)
      .eq("duration", packageType)
      .eq("is_active", true)
      .maybeSingle();

    if (packageError || !platformPackage) {
      console.error("Platform paketi bulunamadı:", {
        tier,
        packageType,
        packageError,
        platformPackage,
      });
      return {
        error: `Seçilen paket bulunamadı. Tier: ${tier}, Duration: ${packageType}. Error: ${
          packageError?.message || "Package not found"
        }`,
      };
    }

    // Manager kaydı oluştur (RLS bypass için admin client kullan)
    const { data: managerData, error: managerError } = await adminClient
      .from("managers")
      .insert({
        user_id: userId,
        platform_package_type: packageType,
        tier: tier,
        subscription_start_date: startDate.toISOString(),
        subscription_end_date: endDate.toISOString(),
        status: "active",
        last_payment_transaction_id: null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (managerError) {
      console.error("Manager kaydı oluşturulurken hata:", managerError);
      return { error: managerError.message };
    }

    // Kullanıcının is_manager flag'ini true yap
    const { error: updateUserError } = await adminClient
      .from("users")
      .update({
        is_manager: true,
        updated_at: new Date().toISOString(),
      })
      .eq("id", userId);

    if (updateUserError) {
      console.error(
        "Kullanıcı manager flag'i güncellenirken hata:",
        updateUserError
      );
      // Bu kritik değil, devam edebiliriz
    }

    // Audit log
    await logSuccess(
      "create_manager",
      "user",
      `Yönetici aboneliği oluşturuldu: ${userData.name} ${userData.surname} (${tier} ${packageType})`,
      {
        resourceId: userId,
        newValues: {
          package_type: packageType,
          tier: tier,
          subscription_start: startDate.toISOString(),
          subscription_end: endDate.toISOString(),
          status: "active",
          price_amount: platformPackage.price_amount,
        },
        metadata: {
          user_email: userData.email,
          package_name: platformPackage.name,
          package_duration:
            packageType === "monthly"
              ? "1 ay"
              : packageType === "quarterly"
              ? "3 ay"
              : "1 yıl",
          package_features: platformPackage.features,
        },
      }
    );

    return {
      success: true,
      data: managerData,
      subscription_end_date: endDate.toISOString(),
    };
  } catch (error: any) {
    console.error("Yönetici oluşturulurken hata:", error);

    // Hata audit log
    await logError(
      "create_manager",
      "user",
      `Yönetici aboneliği oluşturulurken hata: Kullanıcı ID ${userId}`,
      error.message,
      {
        resourceId: userId,
        metadata: {
          package_type: packageType,
          error_type: "unexpected_error",
        },
      }
    );

    return { error: error.message };
  }
}

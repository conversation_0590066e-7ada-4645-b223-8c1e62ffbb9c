/**
 * Type definitions for server actions
 */

/**
 * Standard API response structure
 */
export type ApiResponse<T = any> = {
  success: boolean
  data?: T
  error?: string
  message?: string
}

/**
 * Action options for createAction
 */
export type ActionOptions = {
  revalidatePaths?: string[]
  requireAuth?: boolean
}

/**
 * Standard error messages
 */
export const ErrorMessages = {
  UNAUTHORIZED: "Bu işlem için yetkiniz yok.",
  NOT_FOUND: "İstediğiniz kayıt bulunamadı.",
  VALIDATION_ERROR: "Geçersiz veri formatı. Lütfen tüm alanları kontrol edin.",
  FORBIDDEN: "Bu işlemi gerçekleştirmeye yetkiniz yok.",
  SERVER_ERROR: "Sunucu hatası. Lütfen daha sonra tekrar deneyin.",
  AUTH_REQUIRED: "Bu işlem için giriş yapmanız gerekiyor."
} 
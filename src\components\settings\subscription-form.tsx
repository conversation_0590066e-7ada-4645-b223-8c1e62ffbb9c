"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  <PERSON>ertCircle,
  CheckCircle,
  <PERSON><PERSON><PERSON>,
  Credit<PERSON>ard,
  Check,
} from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { cn } from "@/lib/utils";

import { createManager } from "@/app/actions/user-actions";

interface SubscriptionFormProps {
  userId: string;
  isManager: boolean;
}

type SubscriptionPlan = "monthly" | "quarterly" | "yearly";
type SubscriptionTier = "starter" | "professional" | "enterprise";
type SubscriptionStep = "select" | "payment" | "gym-setup";

export function SubscriptionForm({ userId, isManager }: SubscriptionFormProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [currentStep, setCurrentStep] = useState<SubscriptionStep>("select");
  const [selectedPlan, setSelectedPlan] = useState<SubscriptionPlan | null>(
    null
  );
  const [selectedTier, setSelectedTier] =
    useState<SubscriptionTier>("professional");
  const [paymentSuccess, setPaymentSuccess] = useState(false);

  // Gym setup form state
  const [gymFormData, setGymFormData] = useState({
    name: "",
    description: "",
    phone: "",
    email: "",
    address: "",
    city: "",
    district: "",
    gym_type: "fitness",
  });

  if (isManager) {
    return (
      <div className="space-y-6">
        <Alert className="bg-green-50 text-green-800 border-green-200">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription>
            Şu anda yönetici aboneliğiniz bulunmaktadır. Abonelik detaylarınızı
            yönetici panelinden görüntüleyebilirsiniz.
          </AlertDescription>
        </Alert>

        <Card>
          <CardHeader>
            <CardTitle>Mevcut Abonelik</CardTitle>
            <CardDescription>Yönetici aboneliğinizin detayları</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="font-medium">Abonelik Tipi:</span>
                <span className="text-primary">Yönetici Aboneliği</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="font-medium">Durum:</span>
                <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">
                  Aktif
                </span>
              </div>
              <Button
                variant="outline"
                className="w-full"
                onClick={() => router.push("/dashboard/manager")}
              >
                Yönetici Paneline Git
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  const handlePlanSelect = (plan: SubscriptionPlan) => {
    setSelectedPlan(plan);
  };

  const handleGymFormChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setGymFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleGymTypeChange = (value: string) => {
    setGymFormData((prev) => ({ ...prev, gym_type: value }));
  };

  const handlePaymentSimulation = async () => {
    if (!selectedPlan) {
      setError("Lütfen bir abonelik planı seçin.");
      return;
    }

    setIsLoading(true);

    // Ödeme simülasyonu - gerçek bir ödeme işlemi olmadan
    setTimeout(async () => {
      setIsLoading(false);
      setPaymentSuccess(true);
      setCurrentStep("gym-setup");
      const subscription = await createManager(
        userId,
        selectedPlan,
        selectedTier
      );

      if (subscription.error) {
        setError(subscription.error);
        setCurrentStep("payment");
        return;
      }
    }, 1500);
  };

  const handleGymSetupSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedPlan) {
      setError("Abonelik planı seçilmedi.");
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Form verilerini hazırla
      const formData = new FormData();
      formData.append("userId", userId);
      formData.append("planType", selectedPlan);

      // Salon bilgilerini ekle
      Object.entries(gymFormData).forEach(([key, value]) => {
        formData.append(key, value);
      });

      // Server action'ı çağır
      //const result = await createManagerFromSettings(formData);

      //if (result.error) {
      //  throw new Error(result.error);
      //}

      setSuccess("Yönetici aboneliğiniz ve salonunuz başarıyla oluşturuldu.");

      // Başarılı ise, manager dashboard'una yönlendir
      setTimeout(() => {
        router.push("/dashboard/manager");
      }, 1500);
    } catch (err: any) {
      console.error("Manager subscription error:", err);
      setError(
        err.message || "İşlem sırasında bir hata oluştu. Lütfen tekrar deneyin."
      );
    } finally {
      setIsLoading(false);
    }
  };

  // Abonelik planı seçim ekranı
  if (currentStep === "select") {
    return (
      <div className="space-y-6">
        <Alert variant="default">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Şu anda standart üye hesabınız bulunmaktadır. Salon sahibi veya
            yöneticisi iseniz, yönetici aboneliğine geçiş yapabilirsiniz.
          </AlertDescription>
        </Alert>

        <Card className="border-2 border-dashed border-primary/50 hover:border-primary transition-colors">
          <CardHeader>
            <div className="flex items-center space-x-2">
              <Dumbbell className="h-5 w-5 text-primary" />
              <CardTitle>Yönetici Olmak İster Misiniz?</CardTitle>
            </div>
            <CardDescription>
              Salon sahibi veya yöneticisi olarak Sportiva'da salonunuzu yönetin
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-2">
              <h3 className="font-medium">Yönetici Aboneliği ile:</h3>
              <ul className="space-y-1 list-disc pl-5 text-sm">
                <li>Salonunuzu platforma ekleyebilirsiniz</li>
                <li>Üyelerinizi yönetebilirsiniz</li>
                <li>Paketler oluşturabilirsiniz</li>
                <li>Dersler ve etkinlikler düzenleyebilirsiniz</li>
                <li>Detaylı raporlar alabilirsiniz</li>
              </ul>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Aylık Plan */}
              <Card
                className={cn(
                  "cursor-pointer hover:border-primary transition-colors",
                  selectedPlan === "monthly" ? "border-2 border-primary" : ""
                )}
                onClick={() => handlePlanSelect("monthly")}
              >
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">Aylık Plan</CardTitle>
                  <CardDescription>Aylık ödeme ile esneklik</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold mb-2">
                    ₺299
                    <span className="text-sm font-normal text-muted-foreground">
                      /ay
                    </span>
                  </div>
                  <ul className="space-y-2 mb-4">
                    <li className="flex items-center text-sm">
                      <Check className="h-4 w-4 mr-2 text-green-500" />
                      <span>Tüm yönetici özellikleri</span>
                    </li>
                    <li className="flex items-center text-sm">
                      <Check className="h-4 w-4 mr-2 text-green-500" />
                      <span>Teknik destek</span>
                    </li>
                    <li className="flex items-center text-sm">
                      <Check className="h-4 w-4 mr-2 text-green-500" />
                      <span>İstatistikler ve raporlar</span>
                    </li>
                  </ul>
                </CardContent>
                <CardFooter>
                  {selectedPlan === "monthly" && (
                    <div className="w-full bg-primary/10 text-primary text-center py-1 rounded-md text-sm">
                      Seçildi
                    </div>
                  )}
                </CardFooter>
              </Card>

              {/* 3 Aylık Plan */}
              <Card
                className={cn(
                  "cursor-pointer hover:border-primary transition-colors",
                  selectedPlan === "quarterly" ? "border-2 border-primary" : ""
                )}
                onClick={() => handlePlanSelect("quarterly")}
              >
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-center">
                    <CardTitle className="text-lg">3 Aylık Plan</CardTitle>
                    <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                      %10 İndirim
                    </span>
                  </div>
                  <CardDescription>3 aylık ödeme ile tasarruf</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold mb-2">
                    ₺809
                    <span className="text-sm font-normal text-muted-foreground">
                      /3 ay
                    </span>
                  </div>
                  <div className="text-sm text-muted-foreground mb-2">
                    ₺269.67/ay olarak
                  </div>
                  <ul className="space-y-2 mb-4">
                    <li className="flex items-center text-sm">
                      <Check className="h-4 w-4 mr-2 text-green-500" />
                      <span>Tüm yönetici özellikleri</span>
                    </li>
                    <li className="flex items-center text-sm">
                      <Check className="h-4 w-4 mr-2 text-green-500" />
                      <span>Teknik destek</span>
                    </li>
                    <li className="flex items-center text-sm">
                      <Check className="h-4 w-4 mr-2 text-green-500" />
                      <span>İstatistikler ve raporlar</span>
                    </li>
                  </ul>
                </CardContent>
                <CardFooter>
                  {selectedPlan === "quarterly" && (
                    <div className="w-full bg-primary/10 text-primary text-center py-1 rounded-md text-sm">
                      Seçildi
                    </div>
                  )}
                </CardFooter>
              </Card>

              {/* Yıllık Plan */}
              <Card
                className={cn(
                  "cursor-pointer hover:border-primary transition-colors",
                  selectedPlan === "yearly" ? "border-2 border-primary" : ""
                )}
                onClick={() => handlePlanSelect("yearly")}
              >
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-center">
                    <CardTitle className="text-lg">Yıllık Plan</CardTitle>
                    <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                      %25 İndirim
                    </span>
                  </div>
                  <CardDescription>Yıllık ödeme ile tasarruf</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold mb-2">
                    ₺2,699
                    <span className="text-sm font-normal text-muted-foreground">
                      /yıl
                    </span>
                  </div>
                  <div className="text-sm text-muted-foreground mb-2">
                    ₺224.92/ay olarak
                  </div>
                  <ul className="space-y-2 mb-4">
                    <li className="flex items-center text-sm">
                      <Check className="h-4 w-4 mr-2 text-green-500" />
                      <span>Tüm yönetici özellikleri</span>
                    </li>
                    <li className="flex items-center text-sm">
                      <Check className="h-4 w-4 mr-2 text-green-500" />
                      <span>Öncelikli teknik destek</span>
                    </li>
                    <li className="flex items-center text-sm">
                      <Check className="h-4 w-4 mr-2 text-green-500" />
                      <span>Gelişmiş istatistikler</span>
                    </li>
                    <li className="flex items-center text-sm">
                      <Check className="h-4 w-4 mr-2 text-green-500" />
                      <span>2 ay bedava</span>
                    </li>
                  </ul>
                </CardContent>
                <CardFooter>
                  {selectedPlan === "yearly" && (
                    <div className="w-full bg-primary/10 text-primary text-center py-1 rounded-md text-sm">
                      Seçildi
                    </div>
                  )}
                </CardFooter>
              </Card>
            </div>
          </CardContent>
          <CardFooter>
            {error && (
              <Alert variant="destructive" className="mb-4">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}
            <Button
              className="w-full"
              onClick={() => setCurrentStep("payment")}
              disabled={!selectedPlan || isLoading}
            >
              Devam Et
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  // Ödeme ekranı
  if (currentStep === "payment") {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <div className="flex items-center space-x-2">
              <CreditCard className="h-5 w-5 text-primary" />
              <CardTitle>Ödeme Bilgileri</CardTitle>
            </div>
            <CardDescription>
              Yönetici aboneliğiniz için ödeme bilgilerinizi girin
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <div className="bg-muted/50 p-4 rounded-lg mb-4">
              <h3 className="font-medium mb-2">Sipariş Özeti</h3>
              <div className="flex justify-between mb-2">
                <span>Abonelik Planı:</span>
                <span className="font-medium">
                  {selectedPlan === "monthly"
                    ? "Aylık Plan"
                    : selectedPlan === "quarterly"
                    ? "3 Aylık Plan"
                    : "Yıllık Plan"}
                </span>
              </div>
              <div className="flex justify-between mb-2">
                <span>Fiyat:</span>
                <span className="font-medium">
                  {selectedPlan === "monthly"
                    ? "₺299.00"
                    : selectedPlan === "quarterly"
                    ? "₺809.00"
                    : "₺2,699.00"}
                </span>
              </div>
              <div className="border-t pt-2 mt-2 flex justify-between font-medium">
                <span>Toplam:</span>
                <span className="text-primary">
                  {selectedPlan === "monthly"
                    ? "₺299.00"
                    : selectedPlan === "quarterly"
                    ? "₺809.00"
                    : "₺2,699.00"}
                </span>
              </div>
            </div>

            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="cardName">Kart Üzerindeki İsim</Label>
                <Input id="cardName" placeholder="Ad Soyad" />
              </div>

              <div className="space-y-2">
                <Label htmlFor="cardNumber">Kart Numarası</Label>
                <Input id="cardNumber" placeholder="1234 5678 9012 3456" />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="expiryDate">Son Kullanma Tarihi</Label>
                  <Input id="expiryDate" placeholder="MM/YY" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="cvv">CVV</Label>
                  <Input id="cvv" placeholder="123" />
                </div>
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button
              variant="outline"
              onClick={() => setCurrentStep("select")}
              disabled={isLoading}
            >
              Geri
            </Button>
            <Button onClick={handlePaymentSimulation} disabled={isLoading}>
              {isLoading ? "İşleniyor..." : "Ödemeyi Tamamla"}
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  // Salon kurulum ekranı
  if (currentStep === "gym-setup") {
    return (
      <div className="space-y-6">
        <Alert className="bg-green-50 text-green-800 border-green-200">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription>
            Ödemeniz başarıyla alındı! Şimdi salonunuzun bilgilerini girerek
            kurulumu tamamlayın.
          </AlertDescription>
        </Alert>

        <Card>
          <CardHeader>
            <CardTitle>Salon Bilgileri</CardTitle>
            <CardDescription>
              Salonunuzun temel bilgilerini girin
            </CardDescription>
          </CardHeader>
          <form onSubmit={handleGymSetupSubmit}>
            <CardContent className="space-y-4">
              {error && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              {success && (
                <Alert className="bg-green-50 text-green-800 border-green-200">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <AlertDescription>{success}</AlertDescription>
                </Alert>
              )}

              <div className="space-y-2">
                <Label htmlFor="name">Salon Adı</Label>
                <Input
                  id="name"
                  name="name"
                  value={gymFormData.name}
                  onChange={handleGymFormChange}
                  placeholder="Örn: Fitness Plus"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Salon Açıklaması</Label>
                <Textarea
                  id="description"
                  name="description"
                  value={gymFormData.description}
                  onChange={handleGymFormChange}
                  placeholder="Salonunuzu kısaca tanıtın"
                  rows={3}
                  required
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="phone">Telefon</Label>
                  <Input
                    id="phone"
                    name="phone"
                    value={gymFormData.phone}
                    onChange={handleGymFormChange}
                    placeholder="Örn: 0212 123 4567"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email">E-posta</Label>
                  <Input
                    id="email"
                    name="email"
                    value={gymFormData.email}
                    onChange={handleGymFormChange}
                    placeholder="Örn: <EMAIL>"
                    type="email"
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="address">Adres</Label>
                <Input
                  id="address"
                  name="address"
                  value={gymFormData.address}
                  onChange={handleGymFormChange}
                  placeholder="Sokak, Mahalle, No"
                  required
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="city">Şehir</Label>
                  <Input
                    id="city"
                    name="city"
                    value={gymFormData.city}
                    onChange={handleGymFormChange}
                    placeholder="Örn: İstanbul"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="district">İlçe</Label>
                  <Input
                    id="district"
                    name="district"
                    value={gymFormData.district}
                    onChange={handleGymFormChange}
                    placeholder="Örn: Kadıköy"
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="gym_type">Salon Türü</Label>
                <Select
                  value={gymFormData.gym_type}
                  onValueChange={handleGymTypeChange}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Salon türünü seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="fitness">Fitness Salonu</SelectItem>
                    <SelectItem value="crossfit">CrossFit</SelectItem>
                    <SelectItem value="yoga">Yoga Stüdyosu</SelectItem>
                    <SelectItem value="pilates">Pilates Stüdyosu</SelectItem>
                    <SelectItem value="martial_arts">
                      Dövüş Sanatları
                    </SelectItem>
                    <SelectItem value="swimming">Yüzme</SelectItem>
                    <SelectItem value="multi">
                      Çok Amaçlı Spor Merkezi
                    </SelectItem>
                    <SelectItem value="other">Diğer</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button
                type="button"
                variant="outline"
                onClick={() => setCurrentStep("payment")}
                disabled={isLoading}
              >
                Geri
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? "İşleniyor..." : "Salonu Oluştur ve Tamamla"}
              </Button>
            </CardFooter>
          </form>
        </Card>
      </div>
    );
  }

  return null;
}

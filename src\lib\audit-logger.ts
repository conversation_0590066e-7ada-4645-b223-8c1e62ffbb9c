"use server";

import { getSupabaseAdmin } from "@/utils/supabase/admin";
import { createClient } from "@/utils/supabase/server";

// Audit log türleri
export type AuditAction =
  | "create_member"
  | "invite_member"
  | "update_gym_settings"
  | "update_member"
  | "delete_member"
  | "approve_membership"
  | "reject_membership"
  | "create_gym"
  | "update_gym"
  | "delete_gym"
  | "create_manager"
  | "update_manager"
  | "login"
  | "logout"
  | "password_reset"
  | "search_users"
  | "upload_logo"
  | "create_announcement"
  | "update_announcement"
  | "delete_announcement"
  | "view_audit_logs";

export type ResourceType =
  | "gym"
  | "member"
  | "membership"
  | "user"
  | "announcement"
  | "auth"
  | "system";

export type AuditStatus = "success" | "failed" | "warning";

export interface AuditLogData {
  action: AuditAction;
  resourceType: ResourceType;
  resourceId?: string;
  gymId?: string;
  description: string;
  oldValues?: Record<string, any>;
  newValues?: Record<string, any>;
  metadata?: Record<string, any>;
  status?: AuditStatus;
  errorMessage?: string;
}

export interface AuditContext {
  userId?: string;
  userEmail?: string;
  userName?: string;
  gymId?: string;
  gymName?: string;
  ipAddress?: string;
  userAgent?: string;
}

/**
 * Audit log kaydı oluşturur
 * @param logData Audit log verisi
 * @param context Kullanıcı ve ortam bilgileri
 */
export async function createAuditLog(
  logData: AuditLogData,
  context?: AuditContext
): Promise<void> {
  try {
    const adminClient = getSupabaseAdmin();

    // Eğer context verilmemişse, mevcut kullanıcı bilgilerini al
    let auditContext = context;
    if (!auditContext) {
      auditContext = await getCurrentUserContext();
    }

    // Gym bilgilerini al (eğer gymId varsa)
    let gymName = auditContext?.gymName;
    if (logData.gymId && !gymName) {
      const { data: gymData } = await adminClient
        .from("gyms")
        .select("name")
        .eq("id", logData.gymId)
        .maybeSingle();

      gymName = gymData?.name;
    }

    // Audit log kaydını oluştur
    const auditRecord = {
      user_id: auditContext?.userId || null,
      user_email: auditContext?.userEmail || null,
      user_name: auditContext?.userName || null,
      action: logData.action,
      resource_type: logData.resourceType,
      resource_id: logData.resourceId || null,
      gym_id: logData.gymId || auditContext?.gymId || null,
      gym_name: gymName || null,
      description: logData.description,
      old_values: logData.oldValues || null,
      new_values: logData.newValues || null,
      metadata: {
        ip_address: auditContext?.ipAddress,
        user_agent: auditContext?.userAgent,
        timestamp: new Date().toISOString(),
        ...logData.metadata,
      },
      status: logData.status || "success",
      error_message: logData.errorMessage || null,
      created_at: new Date().toISOString(),
    };

    // Veritabanına kaydet (RLS bypass için admin client kullan)
    const { error } = await adminClient.from("audit_logs").insert(auditRecord);

    if (error) {
      console.error("Audit log kaydedilemedi:", error);
      // Audit log hatası uygulamayı durdurmamalı
    }
  } catch (error) {
    console.error("Audit log oluşturulurken hata:", error);
    // Audit log hatası uygulamayı durdurmamalı
  }
}

/**
 * Mevcut kullanıcı context bilgilerini alır
 */
async function getCurrentUserContext(): Promise<AuditContext> {
  try {
    const supabase = await createClient();

    const { data: userData } = await supabase.auth.getUser();

    if (!userData?.user) {
      return {};
    }

    // Kullanıcı profil bilgilerini al
    const { data: profileData } = await supabase
      .from("users")
      .select("name, surname")
      .eq("id", userData.user.id)
      .maybeSingle();

    return {
      userId: userData.user.id,
      userEmail: userData.user.email || undefined,
      userName: profileData
        ? `${profileData.name} ${profileData.surname}`
        : undefined,
    };
  } catch (error) {
    console.error("Kullanıcı context alınırken hata:", error);
    return {};
  }
}

/**
 * Başarılı işlem için audit log oluşturur
 */
export async function logSuccess(
  action: AuditAction,
  resourceType: ResourceType,
  description: string,
  options?: {
    resourceId?: string;
    gymId?: string;
    oldValues?: Record<string, any>;
    newValues?: Record<string, any>;
    metadata?: Record<string, any>;
    context?: AuditContext;
  }
): Promise<void> {
  await createAuditLog(
    {
      action,
      resourceType,
      description,
      status: "success",
      resourceId: options?.resourceId,
      gymId: options?.gymId,
      oldValues: options?.oldValues,
      newValues: options?.newValues,
      metadata: options?.metadata,
    },
    options?.context
  );
}

/**
 * Başarısız işlem için audit log oluşturur
 */
export async function logError(
  action: AuditAction,
  resourceType: ResourceType,
  description: string,
  errorMessage: string,
  options?: {
    resourceId?: string;
    gymId?: string;
    metadata?: Record<string, any>;
    context?: AuditContext;
  }
): Promise<void> {
  await createAuditLog(
    {
      action,
      resourceType,
      description,
      status: "failed",
      errorMessage,
      resourceId: options?.resourceId,
      gymId: options?.gymId,
      metadata: options?.metadata,
    },
    options?.context
  );
}

/**
 * Uyarı durumu için audit log oluşturur
 */
export async function logWarning(
  action: AuditAction,
  resourceType: ResourceType,
  description: string,
  options?: {
    resourceId?: string;
    gymId?: string;
    metadata?: Record<string, any>;
    context?: AuditContext;
  }
): Promise<void> {
  await createAuditLog(
    {
      action,
      resourceType,
      description,
      status: "warning",
      resourceId: options?.resourceId,
      gymId: options?.gymId,
      metadata: options?.metadata,
    },
    options?.context
  );
}

/**
 * Audit logları sorgular (sadece yöneticiler için)
 */
export async function getAuditLogs(
  gymId?: string,
  filters?: {
    action?: AuditAction;
    resourceType?: ResourceType;
    status?: AuditStatus;
    startDate?: string;
    endDate?: string;
    limit?: number;
  }
) {
  try {
    const supabase = await createClient();

    let query = supabase
      .from("audit_logs")
      .select("*")
      .order("created_at", { ascending: false });

    // Gym filtresi
    if (gymId) {
      query = query.eq("gym_id", gymId);
    }

    // Diğer filtreler
    if (filters?.action) {
      query = query.eq("action", filters.action);
    }

    if (filters?.resourceType) {
      query = query.eq("resource_type", filters.resourceType);
    }

    if (filters?.status) {
      query = query.eq("status", filters.status);
    }

    if (filters?.startDate) {
      query = query.gte("created_at", filters.startDate);
    }

    if (filters?.endDate) {
      query = query.lte("created_at", filters.endDate);
    }

    // Limit
    const limit = filters?.limit || 100;
    query = query.limit(limit);

    const { data, error } = await query;

    if (error) {
      throw new Error(`Audit logları alınırken hata: ${error.message}`);
    }

    return { success: true, data };
  } catch (error: any) {
    console.error("Audit logları alınırken hata:", error);
    return { success: false, error: error.message };
  }
}

"use client"

import { Skeleton } from "@/components/ui/skeleton"

export function NotificationDropdownSkeleton() {
  return (
    <div className="p-2 w-80">
      {/* Başlık */}
      <div className="flex items-center justify-between mb-2 px-2">
        <Skeleton className="h-5 w-24" />
        <Skeleton className="h-4 w-16" />
      </div>
      
      {/* Ayırıcı */}
      <div className="h-px bg-border my-1" />
      
      {/* Bildirimler */}
      <div className="space-y-2 max-h-[300px] overflow-y-auto py-1">
        {[1, 2, 3, 4].map((i) => (
          <div key={`notification-${i}`} className="flex flex-col items-start p-3 rounded-md hover:bg-accent">
            <div className="flex items-center w-full">
              <div className="flex-1 space-y-1">
                <Skeleton className="h-5 w-48" />
                <Skeleton className="h-4 w-64" />
              </div>
              <Skeleton className="h-2 w-2 rounded-full ml-2" />
            </div>
          </div>
        ))}
      </div>
      
      {/* Ayırıcı */}
      <div className="h-px bg-border my-1" />
      
      {/* Alt Kısım */}
      <div className="flex justify-center p-2">
        <Skeleton className="h-9 w-full rounded-md" />
      </div>
    </div>
  )
}

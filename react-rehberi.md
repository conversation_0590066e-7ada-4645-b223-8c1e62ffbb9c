# Kapsamlı React Öğrenme Rehberi

Bu rehber, React kütüphanesini temel seviyeden başlayarak ileri düzey konulara kadar kapsamlı bir şekilde öğrenmenize yardımcı olmak için tasarlanmıştır. Adım adım ilerleyerek React'in gücünü keşfedecek ve modern web uygulamaları geliştirmek için gerekli becerileri kazanacaksınız.

## Bölüm 1: React'e Giriş ve Temel Kavramlar

Bu bö<PERSON>ü<PERSON>, React'in ne olduğunu, neden bu kadar popüler olduğunu, temel yapı taşlarını ve geliştirme ortamınızı nasıl kuracağınızı öğreneceksiniz.

### 1.1. React Nedir? Neden React Kullanmalıyız?

React, kullanıcı a<PERSON><PERSON>zleri (User Interfaces - UI) oluşturmak için kullanılan popüler bir JavaScript kütüphanesidir. Facebook (Meta) ve geniş bir topluluk tarafından geliştirilmekte ve sürdürülmektedir. Temel amacı, karmaşık UI'ları daha küçük, yönetilebilir ve yeniden kullanılabilir parçalara ayırarak geliştirmeyi kolaylaştırmaktır.

**Temel Özellikleri ve Felsefesi:**

*   **Bileşen (Component) Tabanlı Mimari:** React uygulamaları, "bileşen" adı verilen bağımsız ve yeniden kullanılabilir kod parçalarından oluşur. Her bileşen kendi mantığını ve görünümünü yönetir. Bu modüler yaklaşım, büyük uygulamaların geliştirilmesini ve bakımını kolaylaştırır.
    *   Örneğin, bir sosyal medya uygulamasında `GonderiKarti`, `YorumListesi`, `NavigasyonBari` gibi bileşenler olabilir.
*   **Bildirimsel (Declarative) Programlama:** React, bildirimsel bir programlama paradigmasını benimser. Bu, neyin render edileceğini (istenilen durumu) tanımlamanız gerektiği anlamına gelir; React, bu duruma ulaşmak için gerekli adımları (DOM güncellemelerini) arka planda kendisi halleder. Bu, imperatif (adım adım nasıl yapılacağını belirten) yaklaşımlara göre daha az hata içeren ve daha öngörülebilir kod yazmanızı sağlar.
    *   **Örnek:** "Eğer kullanıcı giriş yapmışsa, profil resmini göster; yapmamışsa, giriş yap butonunu göster." gibi durumları tanımlarsınız. React bu tanımlara göre arayüzü günceller.
*   **Sanal DOM (Virtual DOM):** React, performansı artırmak için Sanal DOM adı verilen bir konsept kullanır. Gerçek DOM'da yapılan değişiklikler maliyetli olabilir. React, bir bileşenin state'i veya prop'ları değiştiğinde, öncelikle bu değişikliği Sanal DOM üzerinde yapar. Ardından, Sanal DOM'un önceki ve sonraki hallerini karşılaştırarak (bu işleme "diffing" denir) gerçek DOM'da yapılması gereken en az sayıda ve en verimli güncellemeyi belirler. Bu "reconciliation" (uzlaştırma) süreci, gereksiz DOM manipülasyonlarını önleyerek uygulamaların daha hızlı çalışmasını sağlar.
*   **Tek Yönlü Veri Akışı (One-Way Data Flow):** React'ta veri akışı genellikle tek yönlüdür (parent'tan child'a doğru, props aracılığıyla). Bu, uygulamanın durumunun (state) nerede yaşadığını ve nasıl değiştiğini takip etmeyi kolaylaştırır, böylece hata ayıklama (debugging) süreci basitleşir.

**Neden React Kullanmalıyız?**

*   **Performans:** Sanal DOM sayesinde genellikle yüksek performanslı uygulamalar oluşturulabilir.
*   **Yeniden Kullanılabilirlik:** Bileşen tabanlı mimari, kodun tekrar kullanılmasını teşvik eder, bu da geliştirme sürecini hızlandırır ve tutarlılığı artırır.
*   **Geniş Ekosistem ve Topluluk:** React, çok büyük ve aktif bir topluluğa sahiptir. Bu, bol miktarda kaynak, kütüphane, araç ve destek bulabileceğiniz anlamına gelir. (Örn: Next.js, React Router, Redux, Zustand, Material UI, Ant Design).
*   **Esneklik:** React sadece UI katmanına odaklanır. Bu, onu farklı backend çözümleriyle (Node.js, Python, Java vb.) ve hatta mobil uygulama geliştirme (React Native ile) için kullanabileceğiniz anlamına gelir.
*   **Öğrenme Eğrisi:** Temel kavramları öğrendikten sonra React ile çalışmak oldukça sezgiseldir. Ancak, ekosistemdeki araçlar ve kütüphaneler nedeniyle ileri düzey konular zaman alabilir.
*   **İş Fırsatları:** React, günümüz web geliştirme dünyasında en çok talep gören becerilerden biridir.

**React'in Dezavantajları (Göz Önünde Bulundurulması Gerekenler):**

*   **Sadece UI Kütüphanesi:** React, yönlendirme (routing), state yönetimi (karmaşık uygulamalar için), API istekleri gibi tam bir framework'ün sunduğu tüm çözümleri kutudan çıktığı gibi sunmaz. Bu işlevler için ek kütüphaneler kullanmanız gerekir. (Bu bir esneklik avantajı olabileceği gibi, yeni başlayanlar için seçim yapmayı zorlaştırabilir.)
*   **JSX Öğrenimi:** JSX, HTML'e benzese de JavaScript içinde XML benzeri bir sözdizimidir ve başlangıçta biraz alışma gerektirebilir.
*   **Derleme Adımı:** JSX ve modern JavaScript özellikleri tarayıcılar tarafından doğrudan anlaşılamadığı için bir derleme (transpilation) adımına (genellikle Babel ile) ihtiyaç duyar.
*   **Hızlı Gelişim:** React ve ekosistemi sürekli gelişmektedir. Bu, yeni özellikleri takip etmeyi ve bazen eski yaklaşımları güncellemeyi gerektirebilir.

### 1.2. Geliştirme Ortamının Kurulumu

React ile uygulama geliştirmeye başlamadan önce geliştirme ortamınızı kurmanız gerekir.

#### 1.2.1. Node.js ve npm/yarn Kurulumu

React geliştirme araçları ve kütüphaneleri Node.js çalışma zamanı ortamını ve paket yöneticilerini (npm veya yarn) kullanır.

1.  **Node.js Kurulumu:**
    *   [Node.js resmi web sitesine](https://nodejs.org/) gidin.
    *   Genellikle **LTS (Long Term Support)** sürümünü indirip kurmanız önerilir. Bu sürüm, kararlılık ve uzun süreli destek sunar.
    *   Kurulum sihirbazındaki adımları izleyerek Node.js'i sisteminize kurun. Node.js kurulumuyla birlikte npm (Node Package Manager) de otomatik olarak kurulacaktır.
2.  **Kurulumu Doğrulama:**
    Terminalinizi veya komut istemcinizi açın ve aşağıdaki komutları çalıştırarak Node.js ve npm'in doğru kurulup kurulmadığını kontrol edin:
    ```bash
    node -v
    npm -v
    ```
    Bu komutlar, kurulu Node.js ve npm sürümlerini göstermelidir.
3.  **yarn (Opsiyonel Paket Yöneticisi):**
    npm'e alternatif olarak yarn paket yöneticisini de kullanabilirsiniz. Bazı geliştiriciler performans ve özellik seti nedeniyle yarn'ı tercih eder.
    Aşağıdaki komutla yarn'ı npm üzerinden global olarak kurabilirsiniz:
    ```bash
    npm install --global yarn
    ```
    Kurulumu doğrulamak için:
    ```bash
    yarn --version
    ```

#### 1.2.2. Create React App ile Yeni Proje Oluşturma (Geleneksel Yöntem)

`Create React App (CRA)`, React ekibi tarafından resmi olarak desteklenen, tek komutla modern bir React projesi oluşturmanızı sağlayan bir araçtır. Temel yapılandırmaları (Babel, webpack vb.) sizin için halleder.

**Not:** React topluluğu artık Vite gibi daha modern ve hızlı araçlara yönelmektedir. CRA hala kullanılabilir olsa da, yeni projeler için Vite daha iyi bir seçenek olabilir. Ancak CRA'nın temel mantığını bilmek faydalıdır.

Yeni bir React projesi oluşturmak için terminalde aşağıdaki komutu çalıştırın:

```bash
npx create-react-app benim-ilk-react-uygulamam
```

Veya `yarn` kullanıyorsanız:

```bash
yarn create react-app benim-ilk-react-uygulamam
```

*   `npx`, npm paketlerini global olarak kurmadan çalıştırmanızı sağlar.
*   `benim-ilk-react-uygulamam` kısmını kendi proje adınızla değiştirebilirsiniz.

Bu komut, proje adında yeni bir klasör oluşturacak ve gerekli tüm dosyaları ve bağımlılıkları indirecektir. İşlem tamamlandığında:

```bash
cd benim-ilk-react-uygulamam
npm start
# veya
# yarn start
```

Bu komutlar, geliştirme sunucusunu başlatacak ve uygulamanızı varsayılan tarayıcınızda `http://localhost:3000` adresinde açacaktır. Dosyalarınızda yaptığınız değişiklikler otomatik olarak tarayıcıya yansıyacaktır (Hot Module Replacement - HMR).

#### 1.2.3. Vite ile Alternatif Proje Oluşturma (Modern ve Hızlı Yöntem)

Vite (Fransızca "hızlı" anlamına gelir, /viːt/ olarak telaffuz edilir), modern web geliştirme için tasarlanmış son derece hızlı bir derleme aracı ve geliştirme sunucusudur. Özellikle geliştirme sırasında sunduğu anlık sunucu başlatma ve HMR (Hot Module Replacement) yetenekleriyle bilinir.

Yeni bir React projesini Vite ile oluşturmak için:

```bash
npm create vite@latest benim-vite-react-uygulamam --template react-ts
# veya
yarn create vite benim-vite-react-uygulamam --template react-ts
```

*   `benim-vite-react-uygulamam` proje adınızdır.
*   `--template react` JavaScript tabanlı bir React projesi oluşturur.
*   `--template react-ts` TypeScript tabanlı bir React projesi oluşturur (önerilir).

Vite size proje adı, framework (React seçili olacak) ve variant (JavaScript veya TypeScript) gibi seçenekler sunabilir. İstemleri takip edin.

İşlem tamamlandığında:

```bash
cd benim-vite-react-uygulamam
npm install # veya yarn install (bağımlılıkları kurmak için)
npm run dev # veya yarn dev (geliştirme sunucusunu başlatmak için)
```

Vite, geliştirme sunucusunu genellikle `http://localhost:5173` (veya benzeri bir portta) başlatır. CRA'ya göre çok daha hızlı bir geliştirme deneyimi sunar.

#### 1.2.4. Proje Yapısını Anlama

Hem CRA hem de Vite ile oluşturulan projelerin temel yapıları benzerdir, ancak bazı farklılıklar olabilir. Tipik bir React proje yapısı (Vite ile oluşturulmuş bir `react-ts` projesi üzerinden):

```
benim-vite-react-uygulamam/
├── node_modules/        # Proje bağımlılıkları
├── public/              # Statik dosyalar (favicon, robots.txt vb.)
│   └── vite.svg         # Örnek statik dosya
├── src/                 # Uygulama kaynak kodu
│   ├── assets/          # Bileşenlerin kullandığı resimler, fontlar vb.
│   │   └── react.svg
│   ├── App.css          # Ana uygulama bileşeni için CSS
│   ├── App.tsx          # Ana uygulama bileşeni (React kodu burada başlar)
│   ├── index.css        # Global CSS stilleri
│   ├── main.tsx         # Uygulamanın giriş noktası (React DOM'a render edilir)
│   └── vite-env.d.ts    # Vite için TypeScript tip tanımları
├── .eslintrc.cjs        # ESLint yapılandırması (kod kalitesi için)
├── .gitignore           # Git tarafından takip edilmeyecek dosyalar
├── index.html           # Uygulamanın ana HTML dosyası (Vite bunu işler)
├── package.json         # Proje metadata'sı ve bağımlılık listesi
├── tsconfig.json        # TypeScript yapılandırması
├── tsconfig.node.json   # TypeScript Node ortamı yapılandırması
└── vite.config.ts       # Vite yapılandırma dosyası
```

**Önemli Dosyalar ve Klasörler:**

*   **`public/`**: Bu klasördeki dosyalar doğrudan sunulur ve derleme sürecine dahil edilmez. `index.html` içinde `%PUBLIC_URL%/favicon.ico` gibi yollarla erişilir.
*   **`src/`**: Uygulamanızın ana kaynak kodunun bulunduğu yerdir. Bileşenler, stiller, yardımcı fonksiyonlar vb. burada yer alır.
*   **`src/main.tsx` (veya `index.js` / `index.tsx` CRA'da):** Uygulamanızın giriş noktasıdır. `ReactDOM.createRoot()` (veya `ReactDOM.render()` eski versiyonlarda) ile ana React bileşeniniz (`App.tsx`) HTML'deki bir DOM elemanına bağlanır.
*   **`src/App.tsx` (veya `App.js`):** Genellikle uygulamanızın kök bileşenidir. Diğer tüm bileşenler bu bileşenin altında yer alır.
*   **`package.json`**: Projenizin adı, sürümü, bağımlılıkları (`dependencies` ve `devDependencies`) ve script'leri (`npm start`, `npm run build` gibi) içerir.
*   **`index.html`**: Tek Sayfa Uygulaması (Single Page Application - SPA) için ana HTML dosyasıdır. React, bu dosyadaki genellikle `<div id="root"></div>` gibi bir elemanın içine render edilir. Vite'ta bu dosya proje kökündedir ve bir şablon görevi görür; CRA'da ise `public/` klasöründedir.

Artık geliştirme ortamınız hazır olduğuna göre, React'in temel yapı taşı olan JSX'i öğrenmeye başlayabiliriz!

### 1.3. JSX (JavaScript XML)

JSX, JavaScript XML anlamına gelir ve React ile UI geliştirmeyi çok daha sezgisel hale getiren bir sözdizimi uzantısıdır. HTML'e benzeyen bir yapıda JavaScript kodu içinde UI elemanları tanımlamanızı sağlar. Tarayıcılar JSX'i doğrudan anlayamaz, bu nedenle JSX kodunuzun (genellikle Babel gibi bir derleyici aracılığıyla) saf JavaScript fonksiyon çağrılarına (`React.createElement()` gibi) dönüştürülmesi gerekir. Neyse ki, Vite veya Next.js gibi modern araçlar bu dönüşümü arka planda otomatik olarak yapar.

**Neden JSX Kullanılır?**

1.  **Okunabilirlik ve Yazım Kolaylığı:** JSX, UI'ın nasıl görünmesi gerektiğini HTML'e benzer bir şekilde ifade etmenizi sağlar. Bu, özellikle karmaşık bileşen hiyerarşileri oluştururken kodu daha anlaşılır kılar.
    ```jsx
    // JSX ile
    const element = <h1 className="greeting">Merhaba, Dünya!</h1>;

    // Saf JavaScript ile (React.createElement)
    const elementJS = React.createElement('h1', {className: 'greeting'}, 'Merhaba, Dünya!');
    ```
    Gördüğünüz gibi JSX versiyonu çok daha tanıdık ve kolaydır.

2.  **Hata Yakalama:** JSX, derleme sırasında HTML benzeri sözdizimi hatalarını (etiketlerin kapatılmaması vb.) yakalamaya yardımcı olur.

3.  **JavaScript'in Gücüyle Birlikte:** JSX sadece bir sözdizimi şekeridir. HTML gibi görünse de, altında JavaScript'in tüm gücünü barındırır. JSX içinde JavaScript ifadeleri kullanabilir, döngüler ve koşullar oluşturabilirsiniz.

**JSX Sözdizimi Kuralları:**

1.  **Tek Bir Kök Eleman Döndürme Zorunluluğu:** Bir React bileşeni veya JSX ifadesi her zaman tek bir kök (root) eleman döndürmelidir. Birden fazla eleman döndürmek isterseniz, bunları bir kapsayıcı `<div>` içine alabilir veya React Fragment (`<>...</>`) kullanabilirsiniz.
    ```jsx
    // Yanlış
    // const multiElement = (
    //   <h1>Başlık</h1>
    //   <p>Paragraf</p>
    // );

    // Doğru (div ile)
    const divWrapped = (
      <div>
        <h1>Başlık</h1>
        <p>Paragraf</p>
      </div>
    );

    // Doğru (React Fragment ile - daha temiz)
    const fragmentWrapped = (
      <>
        <h1>Başlık</h1>
        <p>Paragraf</p>
      </>
    );
    ```
    React Fragment, DOM'a ekstra bir düğüm eklemeden gruplama yapmanızı sağlar.

2.  **Etiketlerin Kapatılması:** HTML'de bazı etiketlerin (örn: `<img>`, `<br>`) kapatılması zorunlu olmasa da, JSX'te tüm etiketler kapatılmalıdır. Kendi kendini kapatan etiketler için `/>` kullanılır.
    ```jsx
    <img src="path/to/image.jpg" alt="açıklama" />
    <br />
    <div></div>
    ```

3.  **JavaScript İfadelerini JSX İçinde Kullanma (Embedding Expressions):**
    JSX içinde herhangi bir geçerli JavaScript ifadesini süslü parantezler `{}` içine alarak kullanabilirsiniz.
    ```jsx
    const name = "Sportiva";
    const year = 2024;
    const element = <h1>Merhaba, {name}! {year} yılındayız.</h1>; // Çıktı: Merhaba, Sportiva! 2024 yılındayız.

    function formatUser(user) {
      return user.firstName + ' ' + user.lastName;
    }

    const user = { firstName: 'Can', lastName: 'Yılmaz' };
    const userElement = <p>Kullanıcı: {formatUser(user)}</p>; // Çıktı: Kullanıcı: Can Yılmaz
    ```
    Süslü parantez içine `if` ifadeleri veya `for` döngüleri gibi JavaScript statement'ları doğrudan yazılamaz, ancak ternary operatörler, `map()` gibi fonksiyon çağrıları veya fonksiyonların kendisi kullanılabilir.

4.  **JSX'te Nitelikler (Attributes):**
    HTML niteliklerine benzer şekilde JSX elemanlarına da nitelikler (props olarak da geçer) verebilirsiniz. Ancak bazı HTML niteliklerinin JSX'te farklı adları vardır (JavaScript'teki DOM API özellik adlarıyla uyumlu olması için):

    *   `class` yerine `className` kullanılır (çünkü `class` JavaScript'te rezerve bir kelimedir).
    *   `for` yerine `htmlFor` kullanılır (label elemanlarında).
    *   `tabindex` gibi bazı nitelikler `tabIndex` (camelCase) şeklinde yazılır.
    *   Kendi özel niteliklerinizi (custom attributes) tanımlarken `data-*` veya `aria-*` standartlarına uymanız önerilir.

    Nitelik değerleri tırnak işaretleri (string literalleri için) veya süslü parantezler (JavaScript ifadeleri için) ile belirtilir.
    ```jsx
    const imageUrl = "path/to/image.png";
    const element = <img src={imageUrl} alt="Bir resim" className="profile-picture" />;
    const isDisabled = true;
    const button = <button disabled={isDisabled}>Tıkla</button>;
    ```

5.  **JSX'te Stil Verme:**
    JSX elemanlarına stil vermenin birkaç yolu vardır:

    *   **Inline Stiller:** `style` niteliği bir JavaScript nesnesi alır. Özellik adları camelCase (örn: `backgroundColor` yerine `background-color`) olmalıdır.
        ```jsx
        const divStyle = {
          color: 'blue',
          backgroundColor: 'lightgray',
          padding: '10px'
        };
        const styledElement = <div style={divStyle}>Stilli Div</div>;
        // Veya doğrudan
        const styledElementDirect = <div style={{ color: 'red', fontSize: '16px' }}>Kırmızı Div</div>;
        ```
        Inline stiller dinamik hesaplamalar için kullanışlı olsa da, büyük ölçekli uygulamalarda genellikle CSS dosyaları, CSS Modülleri veya CSS-in-JS kütüphaneleri tercih edilir.

    *   **CSS Sınıfları:** En yaygın yöntem, `className` niteliği ile CSS sınıfları atamaktır. Bu sınıflar ayrı bir CSS dosyasında (`.css`) tanımlanır.
        ```css
        /* App.css */
        .error-message {
          color: red;
          border: 1px solid red;
          padding: 8px;
        }
        ```
        ```jsx
        // App.tsx
        import './App.css'; // CSS dosyasını import et

        function ErrorComponent({ message }) {
          return <div className="error-message">{message}</div>;
        }
        ```

6.  **JSX'te Yorumlar:**
    JSX içinde yorum yazmak için süslü parantezler `{/* ... */}` kullanılır.
    ```jsx
    const element = (
      <div>
        {/* Bu bir başlık yorumudur */}
        <h1>Başlık</h1>
        {/* Paragraf burada bitecek */}
      </div>
    );
    ```

7.  **JSX Boolean Nitelikleri, Sayılar ve Diziler:**
    *   Bir niteliğe değer atamazsanız, varsayılan olarak `true` kabul edilir: `<button disabled />;` eşittir `<button disabled={true} />;`
    *   `false` değeri için süslü parantez kullanmalısınız: `<button disabled={false} />;`
    *   Sayısal değerler ve diziler de süslü parantez içinde verilir: `<MyComponent count={10} items={['elma', 'armut']} />`

8.  **JSX Yayma (Spread) Nitelikleri:**
    Bir nesnedeki tüm özellikleri bir bileşene props olarak geçmek için "spread" operatörünü (`...`) kullanabilirsiniz. Bu, özellikle bir bileşenin aldığı props'ları başka bir bileşene aktarırken kullanışlıdır.
    ```jsx
    function Greeting(props) {
      return <h1>Merhaba, {props.name}! Yaş: {props.age}</h1>;
    }

    function App() {
      const userProps = { name: 'Ayşe', age: 30 };
      return <Greeting {...userProps} id="user-greeting" />;
      // Greeting bileşenine name="Ayşe", age={30} ve id="user-greeting" props'ları geçer.
    }
    ```
    Aynı prop adı hem spread edilen nesnede hem de sonradan ayrıca belirtilirse, son belirtilen değer öncelikli olur (sağdan sola doğru).

**JSX ve `React.createElement()` Arasındaki İlişki**

Unutmayın ki JSX, `React.createElement(component, props, ...children)` fonksiyon çağrıları için sadece bir sözdizimi kolaylığıdır. Örneğin:

```jsx
<MyButton color="blue" shadowSize={2}>
  Click Me
</MyButton>
```

Bu JSX kodu, Babel gibi bir derleyici tarafından şuna benzer bir koda dönüştürülür:

```javascript
React.createElement(
  MyButton,
  {color: 'blue', shadowSize: 2},
  'Click Me'
)
```

React 17 ve sonrasında gelen yeni JSX Dönüşümü (New JSX Transform) sayesinde, artık her dosyada `import React from 'react';` satırını sadece JSX kullanmak için eklemenize gerek kalmamıştır (ancak Hook'lar gibi diğer React özelliklerini kullanmak için hala gereklidir). Vite ve Next.js gibi modern araçlar bu yeni dönüşümü varsayılan olarak kullanır.

JSX, React'in temel taşlarından biridir ve bileşenlerinizi nasıl yapılandıracağınızı ve sunacağınızı tanımlamanın okunabilir ve güçlü bir yolunu sunar. Sonraki bölümlerde JSX'i bileşenler, props ve state ile birlikte nasıl kullanacağımızı daha detaylı göreceğiz.

### 1.4. Bileşenler (Components)

React uygulamalarının temel yapı taşları bileşenlerdir. Bileşenler, kullanıcı arayüzünü (UI) bağımsız, yeniden kullanılabilir parçalara ayırmanızı sağlar. Her bileşen kendi mantığını (JavaScript) ve görünümünü (JSX) yönetir. Bu, karmaşık UI'ları daha küçük, anlaşılır ve yönetilebilir hale getirir.

React'te temel olarak iki tür bileşen vardı: Sınıf (Class) Bileşenleri ve Fonksiyonel (Functional) Bileşenler. Ancak React Hook'larının (ileride detaylı göreceğiz) tanıtılmasıyla birlikte, **Fonksiyonel Bileşenler modern React geliştirmenin standart ve önerilen yolu haline gelmiştir.** Bu rehberde öncelikli olarak Fonksiyonel Bileşenlere odaklanacağız.

**Fonksiyonel Bileşenler (Functional Components)**

Fonksiyonel bileşenler, adından da anlaşılacağı gibi, JavaScript fonksiyonlarıdır. Bu fonksiyonlar:

1.  **Props (properties) adı verilen tek bir nesne argümanı alırlar (opsiyonel).** Props, bileşene dışarıdan veri aktarmak için kullanılır.
2.  **React elemanları (genellikle JSX kullanılarak oluşturulmuş) döndürürler.** Bu, React'e ekranda neyin render edileceğini söyler.

**Bileşen Oluşturma ve Kullanma**

Basit bir fonksiyonel bileşen örneği:

```jsx
// Welcome.tsx (veya Welcome.js)
import React from 'react'; // Hook kullanmasak bile iyi bir pratik

// Fonksiyonel Bileşen Tanımlama
// Props tipi tanımlama (TypeScript kullanıyorsanız)
interface WelcomeProps {
  name: string;
}

// Bileşen adı büyük harfle başlamalıdır (PascalCase)
function Welcome(props: WelcomeProps) {
  return <h1>Merhaba, {props.name}!</h1>;
}

export default Welcome; // Bileşeni dışa aktarma
```

**Önemli Kurallar ve Notlar:**

*   **Bileşen Adları Her Zaman Büyük Harfle Başlamalıdır (PascalCase):** React, küçük harfle başlayan etiketleri yerel HTML etiketleri (örn: `<div>`, `<p>`) olarak, büyük harfle başlayanları ise bileşen olarak ele alır. `welcome` yerine `Welcome` kullanılmalıdır.
*   **`export default` veya `export`:** Bileşenleri başka dosyalarda kullanabilmek için onları dışa aktarmanız gerekir. `export default Welcome;` ile dosyadan varsayılan olarak bu bileşeni dışa aktarırsınız. `export function MyComponent() {...}` şeklinde de birden fazla bileşeni aynı dosyadan dışa aktarabilirsiniz.

Bu `Welcome` bileşenini başka bir bileşende (örneğin `App.tsx`) kullanmak için:

```jsx
// App.tsx
import React from 'react';
import Welcome from './Welcome'; // Welcome bileşenini import et
import './App.css';

function App() {
  return (
    <div>
      <Welcome name="Sportiva" />
      <Welcome name="React Kullanıcısı" />
      <Welcome name="Dünya" />
    </div>
  );
}

export default App;
```

Bu örnekte, `Welcome` bileşeni üç farklı `name` prop'u ile üç kez yeniden kullanılmıştır.

**Bileşenleri İç İçe Kullanma (Nesting Components)**

Bileşenler, daha karmaşık UI'lar oluşturmak için birbirlerinin içine yerleştirilebilir.

```jsx
// UserProfile.tsx
import React from 'react';

interface AvatarProps {
  imageUrl: string;
  altText: string;
}

function Avatar(props: AvatarProps) {
  return <img src={props.imageUrl} alt={props.altText} className="avatar" />;
}

interface UserInfoProps {
  user: {
    name: string;
    bio: string;
  };
}

function UserInfo(props: UserInfoProps) {
  return (
    <div className="user-info">
      <h2>{props.user.name}</h2>
      <p>{props.user.bio}</p>
    </div>
  );
}

interface UserProfileProps {
  user: {
    name: string;
    bio: string;
    avatarUrl: string;
  };
}

function UserProfile(props: UserProfileProps) {
  return (
    <section className="user-profile">
      <Avatar imageUrl={props.user.avatarUrl} altText={props.user.name} />
      <UserInfo user={{ name: props.user.name, bio: props.user.bio }} />
    </section>
  );
}

export default UserProfile;
```

`App.tsx` içinde kullanımı:

```jsx
// App.tsx
import React from 'react';
import UserProfile from './UserProfile';
import './App.css'; // Stil dosyalarını eklemeyi unutmayın

function App() {
  const userData = {
    name: "Volkan K.",
    bio: "React ve TypeScript geliştiricisi.",
    avatarUrl: "https://via.placeholder.com/150"
  };

  return (
    <div>
      <UserProfile user={userData} />
    </div>
  );
}

export default App;
```
Bu örnekte `UserProfile` bileşeni, kendi içinde `Avatar` ve `UserInfo` bileşenlerini render eder. Bu, UI'ı mantıksal parçalara ayırmanın ve yönetmenin güçlü bir yoludur.

**Bileşenleri Farklı Dosyalara Ayırma (Modularity - Modülerlik)**

Yukarıdaki örneklerde olduğu gibi, her bileşeni kendi `.tsx` (veya `.js`) dosyasına yerleştirmek iyi bir pratiktir. Bu yaklaşım:

*   **Okunabilirliği Artırır:** Her dosya tek bir sorumluluğa odaklanır.
*   **Bakımı Kolaylaştırır:** Bir bileşende değişiklik yapmanız gerektiğinde, sadece ilgili dosyayı düzenlersiniz.
*   **Yeniden Kullanılabilirliği Destekler:** Bileşenler projenizin farklı yerlerinde veya hatta farklı projelerde kolayca import edilip kullanılabilir.
*   **Takım Çalışmasını Kolaylaştırır:** Farklı geliştiriciler aynı anda farklı bileşenler üzerinde çakışma olmadan çalışabilir.

Genellikle `src/components` adında bir klasör oluşturulur ve yeniden kullanılabilir bileşenler bu klasör altında mantıksal alt klasörlere ayrılarak saklanır.

Örnek Klasör Yapısı:

```
src/
├── App.tsx
├── main.tsx
├── components/
│   ├── Avatar/
│   │   ├── Avatar.tsx
│   │   └── Avatar.css (isteğe bağlı, bileşene özel stiller)
│   ├── UserInfo/
│   │   ├── UserInfo.tsx
│   │   └── UserInfo.css
│   └── UserProfile/
│       ├── UserProfile.tsx
│       └── UserProfile.css
├── assets/
└── ...
```

Bileşenler, React uygulamalarının kalbidir. Onları etkili bir şekilde nasıl oluşturacağınızı, birleştireceğinizi ve yöneteceğinizi anlamak, başarılı React uygulamaları geliştirmek için kritik öneme sahiptir. Sonraki adımda, bileşenlere veri aktarmanın temel yolu olan "Props" kavramını detaylıca inceleyeceğiz.

### 1.5. Props (Properties)

Props (İngilizce "properties" kelimesinin kısaltması), React bileşenleri arasında veri aktarmanın temel mekanizmasıdır. Bir üst (parent) bileşenden bir alt (child) bileşene veri göndermek için kullanılırlar. Props, alt bileşen için salt okunurdur; yani alt bileşen aldığı props'ları doğrudan değiştiremez. Bu tek yönlü veri akışı, React uygulamalarının anlaşılırlığını ve yönetilebilirliğini artırır.

**Props Nedir ve Nasıl Kullanılır?**

Fonksiyonel bir bileşen, ilk argüman olarak bir `props` nesnesi alır. Bu nesne, bileşen çağrılırken HTML niteliklerine benzer şekilde belirtilen tüm özellikleri içerir.

Örnek:

```tsx
// Greeting.tsx
import React from 'react';

// Props için tip tanımı (TypeScript)
interface GreetingProps {
  name: string;
  message?: string; // message prop'u opsiyonel
}

function Greeting(props: GreetingProps) {
  return (
    <div>
      <h1>Merhaba, {props.name}!</h1>
      {props.message && <p>{props.message}</p>} {/* Mesaj varsa göster */}
    </div>
  );
}

export default Greeting;
```

Bu `Greeting` bileşenini `App.tsx` içinde kullanalım:

```tsx
// App.tsx
import React from 'react';
import Greeting from './Greeting';

function App() {
  return (
    <div>
      <Greeting name="Ali" message="React dünyasına hoş geldin!" />
      <Greeting name="Veli" /> {/* message prop'u gönderilmedi */}
    </div>
  );
}

export default App;
```

Bu örnekte:

*   `Greeting` bileşeni çağrılırken `name` ve `message` adında iki prop (nitelik) tanımlanmıştır.
*   `Greeting` fonksiyonu içinde bu değerlere `props.name` ve `props.message` şeklinde erişilir.
*   TypeScript kullanıyorsanız, `GreetingProps` arayüzü ile `props` nesnesinin beklenen yapısını (hangi prop'ların olduğu, tipleri, zorunlu mu opsiyonel mi olduğu) tanımlamak iyi bir pratiktir. `message?: string;` ifadesindeki `?` işareti, `message` prop'unun opsiyonel olduğunu belirtir.

**Props ile Veriyi Üst Bileşenden Alt Bileşene Aktarma**

Props, veri akışının yukarıdan aşağıya doğru (parent'tan child'a) olmasını sağlar. Bir üst bileşen, state'inde veya kendi props'larından aldığı veriyi alt bileşenlere props aracılığıyla iletebilir.

```tsx
// UserCard.tsx
import React from 'react';

interface User {
  id: number;
  username: string;
  email: string;
  isActive: boolean;
}

interface UserCardProps {
  user: User;
}

function UserCard(props: UserCardProps) {
  const { username, email, isActive } = props.user; // Props'tan yıkım (destructuring) yapılabilir

  return (
    <div style={{ border: '1px solid #ccc', padding: '10px', margin: '10px' }}>
      <h2>{username}</h2>
      <p>E-posta: {email}</p>
      <p>Durum: {isActive ? 'Aktif' : 'Pasif'}</p>
    </div>
  );
}

export default UserCard;
```

```tsx
// UserList.tsx
import React from 'react';
import UserCard from './UserCard';

// User tipini UserCard'dan import edebilir veya burada yeniden tanımlayabiliriz.
// Bu örnek için UserCard'daki tanımı kullanıyor gibi düşünelim.
interface User {
  id: number;
  username: string;
  email: string;
  isActive: boolean;
}

interface UserListProps {
  users: User[];
}

function UserList(props: UserListProps) {
  return (
    <div>
      {props.users.map(currentUser => (
        <UserCard key={currentUser.id} user={currentUser} />
      ))}
    </div>
  );
}

export default UserList;
```

```tsx
// App.tsx
import React, { useState, useEffect } from 'react';
import UserList from './UserList';

// User tipini burada da tanımlayalım tutarlılık için
interface User {
  id: number;
  username: string;
  email: string;
  isActive: boolean;
}

function App() {
  // Genellikle bu veri bir API'den gelir, burada basit bir örnek
  const initialUsers: User[] = [
    { id: 1, username: 'ayse_t', email: '<EMAIL>', isActive: true },
    { id: 2, username: 'mehmet_k', email: '<EMAIL>', isActive: false },
    { id: 3, username: 'zeynep_b', email: '<EMAIL>', isActive: true },
  ];

  // React state'i ileride detaylı görülecek
  const [users, setUsers] = useState<User[]>(initialUsers);

  return (
    <div>
      <h1>Kullanıcı Listesi</h1>
      <UserList users={users} />
    </div>
  );
}

export default App;
```
Bu örnekte `App` bileşeni, `users` dizisini `UserList` bileşenine `users` prop'u olarak geçirir. `UserList` bileşeni de bu diziyi alıp her bir kullanıcı için `UserCard` bileşenini render eder ve her `UserCard`'a ilgili kullanıcı nesnesini `user` prop'u olarak iletir.

**`children` Prop'u**

Her bileşenin özel bir prop'u vardır: `props.children`. Bu prop, bir bileşenin açılış ve kapanış etiketleri arasına yerleştirilen içeriği temsil eder.

```tsx
// Card.tsx
import React from 'react';

interface CardProps {
  title: string;
  children: React.ReactNode; // children için tip tanımı
}

function Card(props: CardProps) {
  return (
    <div style={{ border: '1px solid gray', padding: '15px', margin: '10px' }}>
      <h2>{props.title}</h2>
      <div>
        {props.children}
      </div>
    </div>
  );
}

export default Card;
```

Kullanımı:

```tsx
// App.tsx
import React from 'react';
import Card from './Card';

function App() {
  return (
    <div>
      <Card title="Basit Kart">
        <p>Bu içerik Card bileşeninin children prop'u olarak gönderilecek.</p>
        <button>Tıkla</button>
      </Card>

      <Card title="Başka Bir Kart">
        <ul>
          <li>Madde 1</li>
          <li>Madde 2</li>
        </ul>
      </Card>
    </div>
  );
}

export default App;
```
`Card` bileşeni, `title` prop'unun yanı sıra açılış `<Card>` ve kapanış `</Card>` etiketleri arasına yazılan her şeyi `props.children` olarak alır ve kendi içinde render eder. `React.ReactNode` tipi, render edilebilen her şeyi (sayılar, stringler, React elemanları, diziler vb.) kapsar.

**Props İçin Varsayılan Değerler (Default Props)**

Bir prop'un gönderilmediği durumlarda varsayılan bir değer almasını isteyebilirsiniz. Fonksiyonel bileşenlerde bunu genellikle JavaScript'in varsayılan parametrelerini kullanarak veya prop yıkımı (destructuring) sırasında yapabilirsiniz.

```tsx
// Button.tsx
import React from 'react';

interface ButtonProps {
  label: string;
  theme?: 'primary' | 'secondary' | 'danger';
  onClick?: () => void;
}

// Varsayılan değerleri yıkım (destructuring) ile atama
function Button({ label, theme = 'primary', onClick }: ButtonProps) {
  const buttonStyle = {
    padding: '10px 15px',
    border: 'none',
    borderRadius: '5px',
    cursor: 'pointer',
    backgroundColor: theme === 'primary' ? 'blue' : theme === 'secondary' ? 'gray' : 'red',
    color: 'white'
  };

  return (
    <button style={buttonStyle} onClick={onClick}>
      {label}
    </button>
  );
}

export default Button;
```

Kullanımı:

```tsx
// App.tsx
import React from 'react';
import Button from './Button';

function App() {
  return (
    <div>
      <Button label="Birincil Düğme" onClick={() => alert('Birincil tıklandı!')} />
      <Button label="İkincil Düğme" theme="secondary" />
      <Button label="Tehlike" theme="danger" />
      <Button label="Varsayılan (Birincil)" />
    </div>
  );
}

export default App;
```
Bu örnekte, `Button` bileşenine `theme` prop'u gönderilmezse, varsayılan olarak `'primary'` değeri atanır.

**Props'ların Salt Okunur (Read-Only) Olması**

React'in temel prensiplerinden biri, **bir bileşenin kendi props'larını asla değiştirmemesi gerektiğidir.** Props, üst bileşenden gelir ve alt bileşen için "sahip olunan" değil, "ödünç alınan" veriler gibidir. Bir bileşenin görünümü veya davranışı, kendi iç state'ine (ileride göreceğiz) veya aldığı props'lara göre değişmelidir. Props'ları değiştirmeye çalışmak, veri akışını karmaşıklaştırır ve hatalara yol açar.

```tsx
// Yanlış - Props'u değiştirmeye çalışmak!
function BadComponent(props: { message: string }) {
  // props.message = "Yeni mesaj"; // BU HATALIDIR VE YAPILMAMALIDIR!
  return <p>{props.message}</p>;
}
```

Eğer bir değerin zamanla değişmesi gerekiyorsa, bu değer üst bileşenin state'inde tutulmalı ve prop olarak alt bileşene aktarılmalıdır. Alt bileşenin bu değeri değiştirmesi gerekiyorsa, üst bileşenden bir fonksiyon prop olarak alıp bu fonksiyonu çağırmalıdır (bu konuya state ve olay yönetimi bölümlerinde değineceğiz).

Props, React'te bileşenler arasında iletişim kurmanın ve UI'ı dinamik hale getirmenin temel yoludur. Onları doğru anlamak ve kullanmak, etkili ve bakımı kolay React uygulamaları geliştirmek için hayati önem taşır. Bir sonraki bölümde, bileşenlerin kendi iç verilerini yönetmelerini sağlayan "State" kavramını inceleyeceğiz.

### 1.6. State (Durum)

React'te "state" (durum), bir bileşenin zaman içinde değişebilen ve UI'ın nasıl render edileceğini etkileyen verilerini ifade eder. Props'lar üst bileşenden alt bileşene veri aktarmak için kullanılırken ve alt bileşen tarafından değiştirilemezken, state bir bileşenin kendi içinde yönettiği özel ve değiştirilebilir verileridir. Bir bileşenin state'i değiştiğinde, React bu bileşeni (ve genellikle alt bileşenlerini) otomatik olarak yeniden render eder.

**State Nedir ve Neden Gereklidir?**

Kullanıcı etkileşimleri (tıklamalar, form girdileri), API'den gelen veriler veya zamanla değişen herhangi bir olay, bir bileşenin görünümünü değiştirmesini gerektirebilir. İşte bu tür dinamik verileri tutmak ve yönetmek için state kullanılır.

Örneğin:

*   Bir butona kaç kez tıklandığını saymak.
*   Bir form alanına girilen metni tutmak.
*   Bir API'den yüklenen kullanıcı listesini saklamak.
*   Bir açılır menünün açık mı kapalı mı olduğunu belirlemek.

**`useState` Hook'u**

Fonksiyonel bileşenlerde state yönetimi için React Hook'larından biri olan `useState` kullanılır. Hook'lar, fonksiyonel bileşenlere state ve diğer React özelliklerini "takmanızı" sağlayan özel fonksiyonlardır (Hook'ları ilerleyen bölümlerde daha detaylı göreceğiz).

`useState` Hook'u şu şekilde kullanılır:

1.  **Import Etme:** Öncelikle `useState`'i `react` paketinden import etmeniz gerekir.
    ```javascript
    import React, { useState } from 'react';
    ```

2.  **State Değişkeni Tanımlama:** `useState` fonksiyonu, bir başlangıç değeri (initial state) alır ve iki elemanlı bir dizi döndürür:
    *   **Mevcut state değeri:** Başlangıçta verdiğiniz ilk değerdir.
    *   **State'i güncelleyen fonksiyon:** Bu fonksiyon, state değerini değiştirmek için kullanılır.

    Bu dizi genellikle JavaScript'in dizi yıkımı (array destructuring) sözdizimi ile değişkenlere atanır.

```tsx
// Counter.tsx
import React, { useState } from 'react';

function Counter() {
  // useState kullanımı:
  // count: mevcut state değeri (başlangıçta 0)
  // setCount: count state'ini güncelleyecek fonksiyon
  const [count, setCount] = useState<number>(0); // TypeScript ile tip tanımı

  // Butona tıklandığında çağrılacak fonksiyon
  const increment = () => {
    setCount(count + 1); // count state'ini 1 artır
  };

  const decrement = () => {
    setCount(count - 1); // count state'ini 1 azalt
  };

  return (
    <div>
      <p>Şu anki sayı: {count}</p>
      <button onClick={increment}>Artır (+)</button>
      <button onClick={decrement}>Azalt (-)</button>
      <button onClick={() => setCount(0)}>Sıfırla</button> {/* Doğrudan güncelleme */} 
    </div>
  );
}

export default Counter;
```

**Açıklamalar:**

*   `useState<number>(0)`: `count` adında bir state değişkeni oluşturur. `<number>` TypeScript'te bu state'in bir sayı olacağını belirtir. `0` ise `count` için başlangıç değeridir.
*   `setCount`: `count` state'ini güncellemek için kullanılan fonksiyondur. Bu fonksiyon çağrıldığında, React bileşenin yeniden render edilmesini tetikler ve `count`'un yeni değerine arayüze yansır.
*   **Doğrudan Değişiklik Yapmayın:** State değişkenini (`count`) doğrudan değiştirmemelisiniz (örn: `count = count + 1;` YANLIŞTIR). State'i her zaman `setCount` gibi güncelleme fonksiyonu aracılığıyla değiştirmelisiniz. React'in değişiklikleri takip edebilmesi ve bileşeni doğru zamanda yeniden render edebilmesi için bu gereklidir.

**Fonksiyonel Güncellemeler (Functional Updates)**

State'i güncellerken, yeni state değeri bir önceki state değerine bağlıysa, güncelleme fonksiyonuna bir fonksiyon geçirmek daha güvenli bir yoldur. Bu fonksiyona "updater function" denir ve argüman olarak bir önceki (pending) state değerini alır.

```tsx
const incrementSafely = () => {
  // setCount fonksiyonuna bir fonksiyon geçiyoruz.
  // Bu fonksiyon, parametre olarak bir önceki count değerini (prevCount) alır.
  setCount(prevCount => prevCount + 1);
};
```

Bu özellikle, birden fazla state güncellemesinin kısa bir süre içinde (örneğin aynı olay işleyicisi içinde) yapıldığı veya state güncellemesinin asenkron olduğu durumlarda önemlidir. React, state güncellemelerini toplu (batch) olarak işleyebilir ve bu durumda en son state değerine erişmek için fonksiyonel güncelleme daha güvenilirdir.

Örnek: Bir butona her tıklandığında sayacı 2 artıralım.

```tsx
// Yanlış yaklaşım (beklendiği gibi çalışmayabilir, count değeri kapanış (closure) nedeniyle eski kalabilir)
// const incrementByTwoIncorrect = () => {
//   setCount(count + 1);
//   setCount(count + 1); // İkisi de aynı eski count değerini kullanır
// };

// Doğru yaklaşım (fonksiyonel güncelleme ile)
const incrementByTwoCorrect = () => {
  setCount(prevCount => prevCount + 1);
  setCount(prevCount => prevCount + 1); // Her biri bir önceki güncellenmiş değeri alır
};
```
React 18 ve sonrasında otomatik batching (birleştirme) sayesinde yukarıdaki "yanlış" yaklaşım bazı durumlarda çalışabilir gibi görünse de, fonksiyonel güncellemeler state'in bir önceki haline dayalı güncellemeler için her zaman en iyi ve en güvenli pratiktir.

**State'in Bileşene Özel Olması**

Her `useState` çağrısı, yalnızca çağrıldığı bileşene ait bir state parçası oluşturur. Eğer aynı bileşeni birden fazla kez render ederseniz, her bir örneğin kendi bağımsız state'i olur.

```tsx
// App.tsx
import React from 'react';
import Counter from './Counter';

function App() {
  return (
    <div>
      <h2>Sayaç 1:</h2>
      <Counter />
      <h2>Sayaç 2:</h2>
      <Counter />
    </div>
  );
}

export default App;
```
Bu `App` bileşeninde iki adet `Counter` bileşeni render edilir. Her bir `Counter` kendi `count` state'ine sahip olacaktır ve birbirlerinden bağımsız olarak artırılıp azaltılabilirler.

**State'i Yukarı Taşıma (Lifting State Up)**

Bazen birden fazla alt bileşenin aynı state verisine erişmesi veya bu veriyi değiştirmesi gerekebilir. Bu durumda, state'i bu alt bileşenlerin en yakın ortak ata (common ancestor) bileşenine taşımak ve props aracılığıyla alt bileşenlere iletmek yaygın bir React desenidir. Bu desene "lifting state up" denir.

Örnek senaryo: İki adet sıcaklık giriş alanı (Celsius ve Fahrenheit) olsun ve biri değiştiğinde diğeri de otomatik olarak güncellensin.

1.  **State'i Tanımla:** En yakın ortak ata bileşende (örneğin `Calculator`) sıcaklık ve ölçek (scale) state'lerini tanımlarız.
2.  **Props Olarak Geç:** Bu state değerlerini ve state'i güncelleyecek fonksiyonları alt bileşenlere (`TemperatureInput`) props olarak geçeriz.
3.  **Alt Bileşenlerde Kullan:** Alt bileşenler, props'tan gelen değerleri gösterir ve bir değişiklik olduğunda yine props'tan gelen güncelleme fonksiyonlarını çağırır.

Bu konuyu ilerleyen bölümlerde, özellikle bileşenler arası iletişim ve form yönetimi konularında daha detaylı örneklerle ele alacağız.

**State ile İlgili Önemli Noktalar:**

*   **State Asenkron Olabilir:** `setCount` gibi state güncelleme fonksiyonları çağrıldığında, state hemen güncellenmeyebilir. React, performansı optimize etmek için güncellemeleri birleştirebilir. Bu nedenle, bir state güncellemesinden hemen sonra yeni state değerine güvenmemelisiniz. `useEffect` Hook'u (ileride göreceğiz) bu tür durumları yönetmek için kullanılabilir.
*   **Doğrudan Değiştirmeyin (Immutability):** Dizileri veya nesneleri state'te tutuyorsanız, bunları doğrudan değiştirmek yerine her zaman yeni bir dizi veya nesne oluşturarak state'i güncellemelisiniz. Bu, React'in değişiklikleri doğru bir şekilde algılamasına ve beklenmedik yan etkilerden kaçınmanıza yardımcı olur.
    ```tsx
    // Yanlış (nesneyi doğrudan değiştirme)
    // const [user, setUser] = useState({ name: 'Ali', age: 30 });
    // const handleAgeIncrement = () => {
    //   user.age += 1; // DOĞRUDAN DEĞİŞTİRMEK YANLIŞ!
    //   setUser(user); // React değişikliği fark etmeyebilir
    // };

    // Doğru (yeni nesne oluşturma)
    const [user, setUser] = useState({ name: 'Ali', age: 30 });
    const handleAgeIncrement = () => {
      setUser(prevUser => ({ ...prevUser, age: prevUser.age + 1 }));
    };
    ```

State, React bileşenlerine dinamizm ve etkileşim katmanın anahtarıdır. `useState` Hook'u, bu gücü fonksiyonel bileşenlerde kullanmanın basit ve etkili bir yolunu sunar. Bir sonraki bölümde, kullanıcı etkileşimlerini nasıl yakalayacağımızı ve bunlara nasıl tepki vereceğimizi "Olay Yönetimi (Handling Events)" başlığı altında inceleyeceğiz.

### 1.7. Olay Yönetimi (Handling Events)

React uygulamaları genellikle kullanıcı etkileşimleriyle canlanır. Kullanıcının bir butona tıklaması, bir forma veri girmesi, fareyi bir elemanın üzerine getirmesi gibi olaylara tepki vermek, modern web uygulamalarının temel bir parçasıdır. React, DOM elemanlarındaki olayları yönetmek için HTML'e benzer ancak birkaç önemli farklılığı olan bir sistem sunar.

**React'te Olay Dinleyicileri Tanımlama**

React elemanlarına olay dinleyicileri (event listeners) atamak, HTML'dekilere çok benzer, ancak şu farklarla:

1.  **camelCase Adlandırma:** React olay adları, HTML'deki gibi küçük harfle değil, camelCase (deve notasyonu) ile yazılır.
    *   HTML: `onclick`, `onmouseover`
    *   React: `onClick`, `onMouseOver`

2.  **Fonksiyon Geçirme:** Olay işleyici fonksiyonlar, bir olay tetiklendiğinde çalıştırılacak olan JavaScript fonksiyonlarıdır.
    *   HTML: `<button onclick="myFunction()">Tıkla</button>`
    *   React: `<button onClick={myFunction}>Tıkla</button>` (Burada `myFunction` bir JavaScript fonksiyonudur)

**Olay İşleyici (Event Handler) Fonksiyonları**

Olay işleyici fonksiyonlar, bir olay tetiklendiğinde çalıştırılacak olan JavaScript fonksiyonlarıdır.

```tsx
// ButtonClick.tsx
import React from 'react';

function ButtonClick() {
  // Olay işleyici fonksiyonu
  const handleClick = () => {
    alert('Butona tıklandı!');
  };

  const handleMouseEnter = () => {
    console.log('Fare düğmenin üzerine geldi.');
  };

  return (
    <button onClick={handleClick} onMouseEnter={handleMouseEnter} style={{ padding: '10px', margin: '10px' }}>
      Bana Tıkla veya Üzerime Gel
    </button>
  );
}

export default ButtonClick;
```

Bu örnekte:
*   `handleClick` fonksiyonu, butona tıklandığında (`onClick` olayı) çağrılır.
*   `handleMouseEnter` fonksiyonu, fare imleci düğmenin üzerine geldiğinde (`onMouseEnter` olayı) çağrılır.

**Olay Nesnesi (Event Object)**

Bir olay tetiklendiğinde, React olay işleyici fonksiyona otomatik olarak bir "sentetik olay nesnesi" (SyntheticEvent) geçirir. Bu nesne, tarayıcıların yerel olay nesnelerini sarmalayan (wrap eden) ve tarayıcılar arası tutarlılık sağlayan bir arayüzdür. Standart DOM olay nesnelerindeki `preventDefault()` ve `stopPropagation()` gibi metodlara ve `target`, `type` gibi özelliklere sahiptir.

```tsx
// FormSubmit.tsx
import React, { useState } from 'react';

function FormSubmit() {
  const [inputValue, setInputValue] = useState('');

  const handleSubmit = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault(); // Formun varsayılan gönderme davranışını engelle
    alert(`Gönderilen değer: ${inputValue}`);
    console.log('Olay nesnesi:', event); // Sentetik olay nesnesini incele
    console.log('Olay hedefi (input): ', event.target); // formun kendisi
    // event.target içindeki elemanlara erişmek için:
    // const form = event.target as HTMLFormElement;
    // const inputElement = form.elements.namedItem('myInput') as HTMLInputElement;
    // console.log('Input değeri (formdan): ', inputElement.value);
  };

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(event.target.value); // Input değerini state'e kaydet
  };

  return (
    <form onSubmit={handleSubmit} style={{ margin: '10px' }}>
      <input
        type="text"
        name="myInput" // Form elemanlarına isim vermek onSubmit içinden erişimi kolaylaştırabilir
        value={inputValue} // Kontrollü bileşen (ileride detaylı göreceğiz)
        onChange={handleChange}
        placeholder="Bir şeyler yazın..."
        style={{ marginRight: '5px' }}
      />
      <button type="submit">Gönder</button>
    </form>
  );
}

export default FormSubmit;
```

Bu örnekte:
*   `handleSubmit` fonksiyonu bir `event` nesnesi alır.
*   `event.preventDefault()` çağrısı, formun geleneksel HTTP isteğiyle gönderilmesini engelle
*   `handleChange` fonksiyonu, input alanındaki her değişiklikte tetiklenir. `event.target.value` ile input'un güncel değerine erişilir.
*   TypeScript kullanırken, olay nesnelerinin tiplerini belirtmek (`React.FormEvent<HTMLFormElement>`, `React.ChangeEvent<HTMLInputElement>`) iyi bir pratiktir ve otomatik tamamlama gibi avantajlar sağlar.

**Olay İşleyicilerine Argüman Geçme**

Bazen bir olay işleyici fonksiyona, olay nesnesinin yanı sıra ek argümanlar da geçmek isteyebilirsiniz. Bunu yapmanın birkaç yolu vardır:

1.  **Arrow Function ile Sarmalama:**
    En yaygın yöntem, olay işleyicisini bir arrow function içine alıp bu arrow function içinden kendi fonksiyonunuzu istediğiniz argümanlarla çağırmaktır.
    ```tsx
    function ItemList() {
      const items = [
        { id: 1, name: 'Elma' },
        { id: 2, name: 'Armut' },
        { id: 3, name: 'Muz' },
      ];

      const handleItemClick = (itemId: number, itemName: string, event: React.MouseEvent) => {
        alert(`ID: ${itemId}, İsim: ${itemName} tıklandı!`);
        console.log('Olay tipi:', event.type);
      };

      return (
        <ul>
          {items.map(item => (
            <li key={item.id} onClick={(e) => handleItemClick(item.id, item.name, e)}>
              {item.name}
            </li>
          ))}
        </ul>
      );
    }
    ```
    Burada `onClick` prop'una doğrudan `handleItemClick` değil, `(e) => handleItemClick(item.id, item.name, e)` şeklinde bir arrow function atanmıştır. Bu sayede `item.id` ve `item.name` değerlerini ve ayrıca React tarafından otomatik olarak sağlanan olay nesnesini (`e`) `handleItemClick` fonksiyonuna iletebiliyoruz.

2.  **`.bind()` Kullanımı (Daha Az Yaygın):**
    Fonksiyonların `.bind()` metodu da argüman bağlamak için kullanılabilir, ancak arrow function yaklaşımı genellikle daha okunabilir ve modern kabul edilir.
    ```tsx
    // <li key={item.id} onClick={this.handleItemClick.bind(this, item.id, item.name)}>
    // Bu sözdizimi sınıf bileşenlerinde daha yaygındı.
    ```

**`this` Bağlamı ve Olay İşleyicileri**

Fonksiyonel bileşenlerde `this` bağlamı genellikle bir sorun teşkil etmez, çünkü arrow function'lar kendi `this` bağlamını oluşturmaz ve leksikal (lexical) `this`'i kullanır (yani tanımlandıkları kapsamdaki `this`'i).

Sınıf bileşenlerinde ise olay işleyicilerinin `this` bağlamını doğru şekilde bileşenin örneğine bağlamak için ek adımlar (constructor içinde `.bind()` yapmak veya sınıf özelliği olarak arrow function kullanmak) gerekirdi. Fonksiyonel bileşenler ve Hook'lar bu tür karmaşıklıkları büyük ölçüde ortadan kaldırmıştır.

**Yaygın Olay Türleri**

React, standart DOM olaylarının çoğunu destekler. İşte en sık kullanılanlardan bazıları:

*   **Klavye Olayları:** `onKeyDown`, `onKeyPress`, `onKeyUp`
*   **Form Olayları:** `onChange` (input, select, textarea için), `onSubmit`, `onInvalid`, `onReset`
*   **Fare Olayları:** `onClick`, `onContextMenu` (sağ tık), `onDoubleClick`, `onMouseDown`, `onMouseEnter`, `onMouseLeave`, `onMouseMove`, `onMouseOut`, `onMouseOver`, `onMouseUp`
*   **Odak Olayları:** `onFocus`, `onBlur`
*   **Dokunma Olayları (Mobil için):** `onTouchStart`, `onTouchMove`, `onTouchEnd`, `onTouchCancel`
*   **Kaydırma Olayları:** `onScroll`
*   **Medya Olayları:** `onPlay`, `onPause`, `onEnded` (audio/video elemanları için)

Tam liste ve detaylar için React dokümantasyonundaki "SyntheticEvent" bölümüne bakabilirsiniz.

Olay yönetimi, React uygulamalarınıza etkileşim katmanın temelidir. Kullanıcı girdilerine doğru şekilde yanıt vermek, zengin ve kullanıcı dostu arayüzler oluşturmanın anahtarıdır. Bir sonraki bölümde, UI'ın belirli koşullara göre nasıl farklı şekillerde render edileceğini "Koşullu Renderlama (Conditional Rendering)" başlığı altında inceleyeceğiz.

### 1.8. Koşullu Renderlama (Conditional Rendering)

React'te koşullu renderlama, uygulamanızın mevcut durumuna (state) veya aldığı özelliklere (props) bağlı olarak belirli bileşenleri veya JSX parçalarını render etme veya etmeme yeteneğidir. Bu, dinamik ve etkileşimli kullanıcı arayüzleri oluşturmanın temel bir parçasıdır.

Örneğin, bir kullanıcı giriş yapmışsa profilini, yapmamışsa giriş formunu göstermek isteyebilirsiniz. Veya bir listede öğe yoksa "Hiç öğe bulunamadı" mesajı göstermek isteyebilirsiniz.

React'te koşullu renderlama için JavaScript'in standart koşul ifadelerini ve operatörlerini kullanırız. İşte en yaygın yöntemler:

**1. `if` İfadeleri ile**

En temel yöntem, bir JavaScript fonksiyonu içinde `if` (veya `if-else`) ifadeleri kullanarak hangi JSX'in döndürüleceğine karar vermektir.

```tsx
// UserGreeting.tsx
import React from 'react';

interface UserGreetingProps {
  isLoggedIn: boolean;
  username?: string;
}

function UserGreeting(props: UserGreetingProps) {
  if (props.isLoggedIn) {
    return <h1>Hoş geldin, {props.username || 'Kullanıcı'}!</h1>;
  }
  return (
    <div>
      <h1>Lütfen Giriş Yapın</h1>
      <button>Giriş Yap</button>
    </div>
  );
}

// App.tsx
function App() {
  const [loggedIn, setLoggedIn] = React.useState(false);
  const [name, setName] = React.useState("Gizem");

  return (
    <div>
      <UserGreeting isLoggedIn={loggedIn} username={name} />
      <button onClick={() => setLoggedIn(!loggedIn)}>
        {loggedIn ? 'Çıkış Yap' : 'Giriş Yap'}
      </button>
    </div>
  );
}

export default App;
```
Bu örnekte, `UserGreeting` bileşeni `isLoggedIn` prop'una göre farklı JSX blokları döndürür.

**2. Mantıksal `&&` Operatörü ile (Inline If)**

JSX içinde, bir koşul doğruysa bir şeyi render etmek ve yanlışsa hiçbir şeyi render etmemek için mantıksal `&&` (AND) operatörünü kullanabilirsiniz. Bu, "inline if" olarak da bilinir.

JavaScript'te `true && expression` her zaman `expression` olarak, `false && expression` ise her zaman `false` olarak değerlendirilir. React, `false` veya `null` gibi değerleri render etmez, bu yüzden bu yöntem işe yarar.

```tsx
// Mailbox.tsx
import React from 'react';

interface MailboxProps {
  unreadMessages: string[];
}

function Mailbox(props: MailboxProps) {
  const count = props.unreadMessages.length;
  return (
    <div>
      <h2>Merhaba!</h2>
      {count > 0 && (
        <p>
          {count} adet okunmamış mesajınız var.
        </p>
      )}
      {!count && <p>Okunmamış mesajınız bulunmamaktadır.</p>}
    </div>
  );
}

// App.tsx
function App() {
  const messages = ['Mesaj 1', 'Mesaj 2'];
  const noMessages: string[] = [];
  return (
    <div>
      <Mailbox unreadMessages={messages} />
      <hr />
      <Mailbox unreadMessages={noMessages} />
    </div>
  );
}

export default App;
```
Bu örnekte, `count > 0` ise mesaj sayısı gösterilir, aksi takdirde hiçbir şey (veya `!count` koşulu ile alternatif bir mesaj) gösterilir.

**Dikkat:** `&&` operatörünün sol tarafındaki ifade sayı gibi "falsy" olmayan ama `false` da olmayan bir değer (örn: `0`) döndürürse, React bu `0` değerini render edebilir. Bu nedenle, koşulunuzun her zaman kesin bir boolean (`true` veya `false`) döndürdüğünden emin olun. Örneğin, `count && ...` yerine `count > 0 && ...` kullanmak daha güvenlidir.

**3. Üçlü (Ternary) Operatör (`koşul ? doğruysa_ifade : yanlışsa_ifade`) ile (Inline If-Else)**

JSX içinde bir koşula bağlı olarak iki farklı ifadeden birini render etmek için üçlü (ternary) operatörü kullanabilirsiniz. Bu, "inline if-else" gibidir.

```tsx
// LoginStatus.tsx
import React, { useState } from 'react';

function LoginButton(props: { onClick: () => void }) {
  return <button onClick={props.onClick}>Giriş Yap</button>;
}

function LogoutButton(props: { onClick: () => void }) {
  return <button onClick={props.onClick}>Çıkış Yap</button>;
}

function LoginStatus() {
  const [isLoggedIn, setIsLoggedIn] = useState(false);

  const handleLoginClick = () => {
    setIsLoggedIn(true);
  };

  const handleLogoutClick = () => {
    setIsLoggedIn(false);
  };

  return (
    <div>
      <p>
        Kullanıcı şu anda {isLoggedIn ? 'giriş yapmış' : 'giriş yapmamış'}.
      </p>
      {isLoggedIn 
        ? <LogoutButton onClick={handleLogoutClick} /> 
        : <LoginButton onClick={handleLoginClick} />
      }
    </div>
  );
}

export default LoginStatus;
```
Bu örnekte, `isLoggedIn` durumuna göre ya `LogoutButton` ya da `LoginButton` render edilir.

Üçlü operatörler daha karmaşık koşullar için iç içe kullanılabilir, ancak bu durum okunabilirliği azaltabilir. Çok karmaşık koşullarda `if` ifadelerini veya bileşeni parçalamayı düşünmek daha iyi olabilir.

**4. Değişkenlere JSX Atayarak**

Bazen koşullu mantığınız daha karmaşık olabilir. Bu durumda, render edilecek JSX'i bir değişkene atayabilir ve bu değişkeni render metodunuzda kullanabilirsiniz.

```tsx
// SystemStatus.tsx
import React from 'react';

interface SystemStatusProps {
  status: 'loading' | 'success' | 'error';
  errorMessage?: string;
}

function SystemStatus(props: SystemStatusProps) {
  let statusMessageComponent;

  if (props.status === 'loading') {
    statusMessageComponent = <p>Veriler yükleniyor...</p>;
  } else if (props.status === 'success') {
    statusMessageComponent = <p style={{ color: 'green' }}>Veriler başarıyla yüklendi!</p>;
  } else if (props.status === 'error') {
    statusMessageComponent = <p style={{ color: 'red' }}>Hata: {props.errorMessage || 'Bilinmeyen bir hata oluştu.'}</p>;
  } else {
    statusMessageComponent = <p>Durum bilinmiyor.</p>;
  }

  return (
    <div>
      <h2>Sistem Durumu:</h2>
      {statusMessageComponent}
    </div>
  );
}

// App.tsx
function App() {
  return (
    <div>
      <SystemStatus status="loading" />
      <hr />
      <SystemStatus status="success" />
      <hr />
      <SystemStatus status="error" errorMessage="API bağlantısı kurulamadı."/>
    </div>
  );
}

export default App;
```
Bu yaklaşım, render mantığını daha temiz ve organize tutmanıza yardımcı olabilir, özellikle birden fazla koşul olduğunda.

**5. `null` veya `false` Döndürerek Hiçbir Şey Render Etmeme**

Bir bileşenin belirli bir koşulda hiçbir şey render etmemesini istiyorsanız, render fonksiyonundan `null` veya `false` döndürebilirsiniz. React, bu değerleri DOM'a herhangi bir çıktı üretmeden görmezden gelir.

```tsx
// WarningBanner.tsx
import React from 'react';

interface WarningBannerProps {
  showWarning: boolean;
  message: string;
}

function WarningBanner(props: WarningBannerProps) {
  if (!props.showWarning) {
    return null; // Eğer uyarı gösterilmeyecekse, hiçbir şey render etme
  }

  return (
    <div style={{ backgroundColor: 'yellow', padding: '10px', border: '1px solid orange' }}>
      <strong>Uyarı:</strong> {props.message}
    </div>
  );
}

// App.tsx
function App() {
  const [displayWarning, setDisplayWarning] = React.useState(true);

  return (
    <div>
      <WarningBanner showWarning={displayWarning} message="Bu önemli bir uyarıdır!"/>
      <button onClick={() => setDisplayWarning(!displayWarning)}>
        {displayWarning ? 'Uyarıyı Gizle' : 'Uyarıyı Göster'}
      </button>
    </div>
  );
}

export default App;
```
Bu, bir bileşeni koşullu olarak "gizlemenin" veya "göstermenin" etkili bir yoludur.

**Hangi Yöntemi Seçmeli?**

*   **Basit "if" veya "if-else" durumları için:** JSX dışında `if` ifadeleri veya JSX içinde üçlü operatör (`? :`) genellikle yeterlidir.
*   **Bir koşul doğruysa bir şey render et, yanlışsa hiçbir şey etme:** Mantıksal `&&` operatörü kısa ve etkilidir.
*   **Daha karmaşık mantıklar için:** JSX'i değişkenlere atamak veya bileşeni daha küçük, yönetilebilir parçalara bölmek okunabilirliği artırır.
*   **Hiçbir şey render etmemek için:** `null` döndürmek standart bir yaklaşımdır.

Koşullu renderlama, React uygulamalarınıza esneklik ve dinamizm kazandırır. Kullanıcı arayüzünüzün farklı durumlara ve verilere akıllıca tepki vermesini sağlar. Bir sonraki bölümde, listeleri ve koleksiyonları nasıl verimli bir şekilde render edeceğimizi "Listeler ve Anahtarlar (Lists and Keys)" başlığı altında inceleyeceğiz.

### 1.9. `useCallback` ve `useMemo` ile Performans Optimizasyonu

React, state veya props değiştiğinde bileşenleri yeniden render ederek UI'ı güncel tutar. Ancak bazen bu yeniden render işlemleri gereksiz olabilir ve performans sorunlarına yol açabilir, özellikle büyük ve karmaşık bileşenlerde veya sık güncellenen verilerle çalışırken.

`useCallback` ve `useMemo` Hook'ları, bu tür gereksiz yeniden hesaplamaları ve yeniden render'ları önlemek için kullanılan iki önemli performans optimizasyon aracıdır. Her ikisi de "memoization" (ezberleme) adı verilen bir tekniği kullanır: Bir fonksiyonun veya değerin sonucunu hesaplar, bu sonucu saklar ve bağımlılıkları değişmediği sürece bir sonraki render'da bu saklanan sonucu yeniden hesaplamak yerine doğrudan kullanır.

**`useMemo` Hook'u: Değerleri Ezberleme**

`useMemo`, maliyetli hesaplamaların sonucunu ezberlemek (memoize etmek) için kullanılır. Bir fonksiyon ve bir bağımlılık dizisi alır. Fonksiyon, sadece bağımlılık dizisindeki değerlerden herhangi biri değiştiğinde yeniden çalıştırılır. Aksi takdirde, `useMemo` önceki render'da hesaplanan değeri döndürür.

**Sözdizimi:**

```tsx
const memoizedValue = useMemo(() => computeExpensiveValue(a, b), [a, b]);
```

*   **`computeExpensiveValue(a, b)`:** Ezberlenecek değeri hesaplayan fonksiyondur.
*   **`[a, b]`:** Bağımlılık dizisidir. `a` veya `b` değiştiğinde `computeExpensiveValue` yeniden çalıştırılır.

**Ne Zaman Kullanılır?**

1.  **Maliyetli Hesaplamalar:** Bir bileşen içinde her render'da yeniden çalıştırılması pahalı olan bir hesaplama varsa (örneğin, büyük bir veri setini filtreleme, sıralama veya dönüştürme).
2.  **Referans Eşitliğini Koruma:** Bir nesne veya dizi gibi referans tipli bir değeri bir alt bileşene prop olarak geçiriyorsanız ve bu değerin gereksiz yere değişmesini istemiyorsanız. Eğer bu değer her render'da yeniden oluşturulursa, alt bileşen (özellikle `React.memo` ile sarmalanmışsa veya `useEffect` içinde bu değere bağımlıysa) gereksiz yere yeniden render olabilir.

**Örnek: Maliyetli Hesaplama**

```tsx
import React, { useState, useMemo } from 'react';

// Maliyetli bir fonksiyon simülasyonu
function expensiveCalculation(num: number): number {
  console.log('Maliyetli hesaplama yapılıyor...');
  let result = 0;
  for (let i = 0; i < num * 10000000; i++) {
    result += Math.random(); // Sadece zaman harcamak için
  }
  return result + num; // Sonuca num'ı ekleyelim ki bir anlamı olsun
}

function OptimizedComponent() {
  const [number, setNumber] = useState(5);
  const [text, setText] = useState('');

  // `number` değişmediği sürece `expensiveCalculation` yeniden çalışmaz.
  // `text` state'i değiştiğinde bileşen yeniden render olur, ancak `calculatedValue` yeniden hesaplanmaz.
  const calculatedValue = useMemo(() => expensiveCalculation(number), [number]);

  return (
    <div>
      <h2>useMemo ile Optimize Edilmiş Hesaplama</h2>
      <input
        type="number"
        value={number}
        onChange={(e) => setNumber(parseInt(e.target.value, 10) || 0)}
      />
      <p>Hesaplanan Değer: {calculatedValue}</p>
      <hr />
      <input
        type="text"
        value={text}
        onChange={(e) => setText(e.target.value)}
        placeholder="Metin girin (bu hesaplamayı etkilemez)"
      />
      <p>Girilen Metin: {text}</p>
    </div>
  );
}

export default OptimizedComponent;
```
Bu örnekte, `text` input'una bir şeyler yazdığınızda `OptimizedComponent` yeniden render olur. Ancak `useMemo` sayesinde `expensiveCalculation` fonksiyonu sadece `number` state'i değiştiğinde çalışır. `text` değişimi `calculatedValue`'nun yeniden hesaplanmasını tetiklemez.

**`useCallback` Hook'u: Fonksiyonları Ezberleme**

`useCallback`, fonksiyonların kendisini ezberlemek (memoize etmek) için kullanılır. `useMemo`'ya çok benzer, ancak bir değer yerine bir fonksiyon döndürür.

Bir fonksiyon ve bir bağımlılık dizisi alır. `useCallback`, sadece bağımlılık dizisindeki değerlerden herhangi biri değiştiğinde yeni bir fonksiyon referansı oluşturur. Aksi takdirde, önceki render'da oluşturulan aynı fonksiyon referansını döndürür.

**Sözdizimi:**

```tsx
const memoizedCallback = useCallback(
  () => {
    doSomething(a, b);
  },
  [a, b],
);
```

*   **`() => { doSomething(a, b); }`:** Ezberlenecek olan callback fonksiyonudur.
*   **`[a, b]`:** Bağımlılık dizisidir. `a` veya `b` değiştiğinde `memoizedCallback` için yeni bir fonksiyon referansı oluşturulur.

**Ne Zaman Kullanılır?**

1.  **Alt Bileşenlere Callback Prop'u Geçerken:** Bir fonksiyonu bir alt bileşene prop olarak geçiriyorsanız ve bu alt bileşen `React.memo` ile optimize edilmişse veya `useEffect` içinde bu callback fonksiyonuna bağımlıysa. Eğer callback fonksiyonu her üst bileşen render'ında yeniden oluşturulursa, alt bileşen gereksiz yere yeniden render olabilir çünkü fonksiyon referansı değişmiş olur.
2.  **`useEffect` Bağımlılık Dizisinde Fonksiyon Kullanırken:** Eğer bir `useEffect` Hook'u, bileşen kapsamında tanımlanmış bir fonksiyona bağımlıysa, o fonksiyonu `useCallback` ile sarmalamak `useEffect`'in gereksiz yere sık çalışmasını engelleyebilir.

**Örnek: `React.memo` ile Alt Bileşene Callback Geçme**

```tsx
import React, { useState, useCallback } from 'react';

// React.memo ile sarmalanmış bir alt bileşen
// Sadece props'ları değişirse yeniden render olur.
const ChildButton = React.memo(({ onClick, label }: { onClick: () => void; label: string }) => {
  console.log(`${label} butonu render edildi!`);
  return <button onClick={onClick}>{label}</button>;
});

function ParentComponent() {
  const [count, setCount] = useState(0);
  const [otherState, setOtherState] = useState(false); // Bu state değişimi ChildButton'ı etkilememeli

  // `otherState` değiştiğinde ParentComponent yeniden render olur.
  // Eğer `increment` ve `decrement` useCallback ile sarmalanmazsa,
  // her ParentComponent render'ında yeni fonksiyon referansları oluşur
  // ve ChildButton'lar gereksiz yere yeniden render olur.

  const increment = useCallback(() => {
    setCount(c => c + 1);
  }, []); // Bağımlılık yok, setCount referansı stabil olduğu için

  const decrement = useCallback(() => {
    setCount(c => c - 1);
  }, []); // Bağımlılık yok

  console.log('ParentComponent render edildi.');

  return (
    <div>
      <h2>useCallback ile Optimize Edilmiş Callback'ler</h2>
      <p>Sayı: {count}</p>
      <ChildButton onClick={increment} label="Artır" />
      <ChildButton onClick={decrement} label="Azalt" />
      <hr />
      <button onClick={() => setOtherState(!otherState)}>
        Diğer State'i Değiştir (Butonları Etkilememeli)
      </button>
      <p>Diğer State: {otherState.toString()}</p>
    </div>
  );
}

export default ParentComponent;
```
Bu örnekte, `increment` ve `decrement` fonksiyonları `useCallback` ile sarmalandığı için, `ParentComponent` içindeki `otherState` değiştiğinde ve `ParentComponent` yeniden render olduğunda bu fonksiyonların referansları aynı kalır. Bu sayede `React.memo` ile sarmalanmış `ChildButton` bileşenleri, `onClick` prop'ları değişmediği için gereksiz yere yeniden render olmaz.

Eğer `useCallback` kullanmasaydık, `otherState` her değiştiğinde `increment` ve `decrement` için yeni fonksiyonlar oluşturulur, bu da `ChildButton`ların her seferinde yeniden render olmasına neden olurdu.

**`useMemo(() => fn, [])` vs `useCallback(fn, [])`**

Aslında `useCallback(fn, deps)` şu anlama gelir: `useMemo(() => fn, deps)`.

Yani, `useCallback` esasen `useMemo`'nun fonksiyonlar için özel bir halidir. Okunabilirlik açısından, bir fonksiyonu memoize etmek istediğinizde `useCallback` kullanmak daha semantiktir.

**Önemli Hususlar ve Yanlış Kullanımlar:**

1.  **Her Yerde Kullanmayın:** `useMemo` ve `useCallback`'i her fonksiyon veya değer için körü körüne kullanmak, kod karmaşıklığını artırabilir ve hatta küçük kazançlar için (veya bazen negatif etkiyle) hafıza tüketimini artırabilir. Bu Hook'lar, performans sorunlarını *çözmek* için hedeflenmiş araçlardır, varsayılan olarak her yere uygulanması gereken kurallar değildir.

2.  **Bağımlılık Dizisi Çok Önemlidir:**
    *   `useMemo` ve `useCallback`'in bağımlılık dizisi, `useEffect`'teki gibi çalışır. Hook içinde kullanılan ve bileşen kapsamında olan (props, state, diğer Hook'lardan gelen değerler) tüm değişkenler bu diziye dahil edilmelidir.
    *   Eksik bağımlılıklar, eski (stale) kapanışlara ve hatalı davranışlara yol açabilir.
    *   Boş bir bağımlılık dizisi (`[]`) verdiğinizde, değer veya fonksiyon sadece ilk render'da hesaplanır/oluşturulur ve sonraki render'larda hep aynı kalır. Bu, içindeki değerlerin hiçbir zaman güncellenmeyeceği anlamına gelir, bu yüzden dikkatli kullanılmalıdır.

3.  **Performans Profillemesi Yapın:** `useMemo` ve `useCallback` kullanmadan önce, gerçekten bir performans sorunu olup olmadığını React Developer Tools (Profiler sekmesi) gibi araçlarla belirleyin. Optimizasyonları sadece kanıtlanmış darboğazlara uygulayın.

4.  **Fonksiyonların Maliyeti:** Çoğu durumda, basit fonksiyonların her render'da yeniden oluşturulmasının maliyeti düşüktür. `useCallback` genellikle, bu fonksiyonların `React.memo` ile sarmalanmış alt bileşenlere prop olarak geçtiği veya `useEffect` bağımlılıklarında kullanıldığı senaryolarda en faydalıdır.

`useMemo` ve `useCallback`, React uygulamalarında performansı optimize etmek için güçlü araçlardır. Ancak, ne zaman ve nasıl kullanılacaklarını anlamak, gereksiz karmaşıklık yaratmadan ve potansiyel hatalardan kaçınarak bu araçlardan en iyi şekilde faydalanmanızı sağlar.

Bir sonraki bölümde, DOM elemanlarına veya React bileşenlerine doğrudan erişmek için kullanılan `useRef` Hook'unu inceleyeceğiz.

### 2.6. `useRef` ile DOM Elemanlarına ve Değişkenlere Referans Verme

`useRef` Hook'u, React'te iki temel amaç için kullanılan çok yönlü bir araçtır:

1.  **DOM Elemanlarına Doğrudan Erişim:** Bir DOM elemanına (örneğin bir input, div veya video elemanı) doğrudan erişmeniz gerektiğinde `useRef` kullanabilirsiniz. Bu, React'in bildirimsel (declarative) akışının dışına çıkmanız gereken nadir durumlar için kullanışlıdır (örneğin, bir input alanına odaklanmak, bir medya elemanını oynatmak/durdurmak, bir animasyonu tetiklemek veya bir DOM elemanının boyutlarını/konumunu ölçmek).
2.  **Değişken Benzeri Bir Kutu (Mutable Instance Variable):** `useRef`, `.current` özelliği aracılığıyla erişilebilen ve render'lar arasında değerini koruyan "değişken benzeri" bir nesne döndürür. `useRef` ile tutulan bir değerin değişmesi, `useState`'teki gibi **bileşenin yeniden render edilmesini tetiklemez.** Bu, render döngüsünden bağımsız olarak bazı değerleri (örneğin, bir interval ID'si, bir abonelik nesnesi veya önceki bir state/prop değeri) saklamak için kullanışlıdır.

**`useRef` Kullanımı:**

`useRef` Hook'u bir başlangıç değeri (initialValue) alır ve tek bir özelliği olan (`.current`) bir nesne döndürür. Bu `.current` özelliği, başlangıçta verdiğiniz `initialValue`'ya ayarlanır.

```tsx
const myRef = useRef(initialValue);
```

*   `myRef.current` üzerinden bu değere erişebilir ve onu değiştirebilirsiniz.
*   `myRef` nesnesinin kendisi render'lar arasında aynı kalır.

**1. DOM Elemanlarına Erişim:**

React'te genellikle DOM'u doğrudan manipüle etmek yerine state ve props aracılığıyla bildirimsel olarak çalışırız. Ancak bazen kaçınılmaz olarak DOM elemanlarına doğrudan erişmemiz gerekebilir.

**Adımlar:**

1.  `useRef` ile bir ref nesnesi oluşturun: `const myInputRef = useRef<HTMLInputElement>(null);` (TypeScript'te ref'in hangi tür DOM elemanına bağlanacağını belirtmek iyidir, başlangıç değeri genellikle `null` olur).
2.  Bu ref nesnesini, erişmek istediğiniz DOM elemanının `ref` niteliğine atayın: `<input ref={myInputRef} type="text" />`.
3.  React, bu DOM elemanı oluşturulduğunda (mount edildiğinde), `myInputRef.current` özelliğini bu DOM elemanının kendisine ayarlayacaktır.
4.  Artık `myInputRef.current` üzerinden DOM elemanının metotlarına ve özelliklerine erişebilirsiniz (örneğin, `myInputRef.current.focus()`, `myInputRef.current.value`).

**Örnek: Input Alanına Otomatik Odaklanma**

```tsx
import React, { useRef, useEffect } from 'react';

function AutoFocusInput() {
  // HTMLInputElement tipinde bir ref oluşturuyoruz, başlangıç değeri null.
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    // Bileşen mount edildikten sonra inputRef.current DOM elemanını işaret edecektir.
    // inputRef.current'ın null olmadığından emin olmak iyi bir pratiktir.
    if (inputRef.current) {
      inputRef.current.focus(); // Input alanına odaklan
      console.log('Input'a odaklanıldı!');
    }
  }, []); // Boş bağımlılık dizisi: Sadece ilk render'dan sonra çalışır

  return (
    <div>
      <h2>Otomatik Odaklanan Input</h2>
      <input ref={inputRef} type="text" placeholder="Burası otomatik odaklanacak" />
      <button onClick={() => inputRef.current?.select()}>Metni Seç</button>
    </div>
  );
}

export default AutoFocusInput;
```
Bu örnekte:
*   `inputRef`, `input` DOM elemanına bağlanır.
*   `useEffect` içinde (bileşen mount edildikten sonra), `inputRef.current.focus()` çağrılarak input alanına otomatik olarak odaklanılır.
*   Butona tıklandığında `inputRef.current?.select()` ile input içindeki metin seçilir (`?.` optional chaining, `current` null ise hata vermesini engeller).

**Dikkat Edilmesi Gerekenler (DOM Ref'leri):**

*   **Zamanlama:** `ref.current` değeri, bileşen mount edilene kadar `null` olur. Eğer DOM elemanına hemen erişmeniz gerekiyorsa (örneğin ilk render sırasında), bu mümkün olmayabilir. Genellikle `useEffect` içinde (mount sonrası) veya olay işleyicilerinde (event handlers) ref'lere erişilir.
*   **Callback Ref'ler:** `ref` niteliğine bir fonksiyon da geçebilirsiniz (`<div ref={(node) => { /* ... */ }}></div>`). Bu callback fonksiyonu, DOM elemanı mount edildiğinde DOM düğümü ile, unmount edildiğinde ise `null` ile çağrılır. Bu, ref'ler üzerinde daha hassas kontrol sağlar.
*   **Fonksiyonel Bileşenlere Ref Geçme (`forwardRef`):** Varsayılan olarak, fonksiyonel bir bileşene doğrudan `ref` prop'u geçemezsiniz. Eğer bir fonksiyonel alt bileşenin içindeki bir DOM elemanına üst bileşenden erişmek istiyorsanız, alt bileşeni `React.forwardRef` ile sarmalamanız gerekir. Bu konuya ileri düzey rehberlerde değinilebilir.

**2. Değişken Benzeri Bir Kutu (Mutable Instance Variable):**

`useRef`, render döngüsünden bağımsız olarak değerleri saklamak için de kullanılabilir. `useState`'ten farklı olarak, `ref.current` değerini değiştirmek **bileşeni yeniden render etmez.**

**Ne Zaman Kullanılır?**

*   **Render Tetiklemeyen Değerler:** Bir değeri saklamak istiyorsunuz ama bu değerin değişmesi UI'ın yeniden çizilmesini gerektirmiyorsa.
*   **Önceki State/Props Değerlerini Tutma:** Bir state veya prop'un bir önceki değerini saklamak için kullanılabilir.
*   **Zamanlayıcı ID'leri veya Abonelikler:** `setTimeout`, `setInterval` ID'lerini veya WebSocket gibi abonelik nesnelerini saklamak ve temizlemek için.

**Örnek: Önceki State Değerini Tutma**

```tsx
import React, { useState, useEffect, useRef } from 'react';

function PreviousValueLogger() {
  const [count, setCount] = useState(0);
  const previousCountRef = useRef<number>(); // Başlangıçta undefined

  useEffect(() => {
    // Her render'dan sonra (count güncellendikten sonra) çalışır.
    // mevcut count değerini bir sonraki render için "önceki değer" olarak ref'e kaydeder.
    previousCountRef.current = count;
  }, [count]); // Sadece count değiştiğinde çalışır

  const previousCount = previousCountRef.current; // undefined veya bir önceki count değeri

  return (
    <div>
      <h2>Önceki ve Şimdiki Değer</h2>
      <p>Şimdiki Sayı: {count}</p>
      <p>Önceki Sayı: {previousCount === undefined ? 'Yok' : previousCount}</p>
      <button onClick={() => setCount(prev => prev + 1)}>Artır</button>
    </div>
  );
}

export default PreviousValueLogger;
```
Bu örnekte:
*   `previousCountRef.current`, `count` state'inin bir önceki değerini tutar.
*   `useEffect` içinde, `count` her değiştiğinde `previousCountRef.current` güncellenir. Bu güncelleme yeniden render tetiklemez.
*   Bir sonraki render'da, `previousCount` değişkeni bir önceki render'daki `count` değerini içerir.

**Örnek: Bir Interval ID'sini Saklama**

```tsx
import React, { useState, useEffect, useRef } from 'react';

function IntervalTimer() {
  const [seconds, setSeconds] = useState(0);
  const intervalRef = useRef<NodeJS.Timeout | null>(null); // Timer ID'sini tutacak ref

  const startTimer = () => {
    if (intervalRef.current !== null) return; // Zaten çalışıyorsa başlatma

    intervalRef.current = setInterval(() => {
      setSeconds(prev => prev + 1);
    }, 1000);
    console.log('Timer başlatıldı, ID:', intervalRef.current);
  };

  const stopTimer = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
      console.log('Timer durduruldu.');
    }
  };

  // Bileşen unmount olduğunda interval'i temizle
  useEffect(() => {
    // Bu, sadece unmount sırasında çalışacak bir temizlik fonksiyonu döndürür.
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        console.log('Bileşen kaldırılırken timer temizlendi.');
      }
    };
  }, []); // Boş bağımlılık dizisi, sadece mount ve unmount için

  return (
    <div>
      <h2>Interval Timer (useRef ile)</h2>
      <p>Saniye: {seconds}</p>
      <button onClick={startTimer}>Başlat</button>
      <button onClick={stopTimer}>Durdur</button>
      <button onClick={() => setSeconds(0)}>Sıfırla (State'i)</button>
    </div>
  );
}

export default IntervalTimer;
```
Bu örnekte `intervalRef.current`, `setInterval` tarafından döndürülen ID'yi saklar. Bu ID'yi `clearInterval` ile kullanmak için saklamamız gerekir. `intervalRef.current`'ı değiştirmek yeniden render tetiklemediği için bu senaryo için idealdir.

**`useRef` vs `useState`**

| Özellik          | `useRef`                                     | `useState`                                        |
| ---------------- | -------------------------------------------- | ------------------------------------------------- |
| **Değer Değişimi** | `.current` özelliği değiştirilebilir.          | State güncelleme fonksiyonu (`setState`) ile.      |
| **Render Tetikler mi?** | Hayır, `.current` değişimi render tetiklemez. | Evet, state değişimi genellikle render tetikler.    |
| **Kullanım Amacı**  | DOM erişimi, render tetiklemeyen değişkenler. | Bileşenin render'ını etkileyen yönetilen durum.    |
| **Asenkronluk**    | `.current` hemen güncellenir.                 | State güncellemeleri asenkron olabilir (batching). |

`useRef`, React'in bildirimsel paradigmasının biraz dışına çıkmanız gerektiğinde veya render döngüsünden bağımsız verileri yönetmek istediğinizde kullanışlı bir araçtır. DOM manipülasyonları için dikkatli kullanılmalı ve genellikle son çare olarak düşünülmelidir.

Bu bölümle birlikte temel ve bazı ileri düzey Hook'ları (`useState`, `useEffect`, `useContext`, `useReducer`, `useCallback`, `useMemo`, `useRef`) incelemiş olduk. Bu Hook'lar, modern React uygulamaları geliştirmek için güçlü bir temel oluşturur.

Bir sonraki bölümde, React uygulamalarında formların nasıl yönetileceğine dair farklı yaklaşımları ele alacağız.
---
description:
globs:
alwaysApply: false
---
# React Performans Optimizasyonu

React uygulamalarının performansını artırmak için önemli teknikler ve en iyi pratikler.

## Gereksiz Render'ları Önleme

### `React.memo`

Bileşenleri memoize ederek, props değişmediğinde yeniden render edilmesini engeller.

```jsx
import { memo } from 'react';

const ExpensiveComponent = memo(function ExpensiveComponent({ data }) {
  console.log('Expensive component render edildi');
  // Maliyetli işlemler...
  return <div>{/* Render çıktısı */}</div>;
});
```

- Sadece props eşitliği kontrolü yapılır
- Basit bileşenler için veya state değişimleri sıksa gereksiz olabilir
- İkinci parametre olarak özel karşılaştırma fonksiyonu (areEqual) alabilir

### `useCallback` ile Fonksiyon Referanslarını Ezberleme

Alt bileşenlere gönderilen callback fonksiyonlarının referanslarının değişmesini önler.

```jsx
import { useState, useCallback } from 'react';

function Parent() {
  const [value, setValue] = useState('');
  
  // Değişmeyen bir handleChange referansı
  const handleChange = useCallback((e) => {
    setValue(e.target.value);
  }, []); // Boş bağımlılık dizisi: Fonksiyon referansı hiç değişmez
  
  return <ChildInput onChange={handleChange} />;
}

const ChildInput = memo(function ChildInput({ onChange }) {
  return <input onChange={onChange} />;
});
```

### `useMemo` ile Değerleri Ezberleme

Maliyetli hesaplamaları veya referans tipli değerleri önbelleğe almak için kullanılır.

```jsx
import { useState, useMemo } from 'react';

function FilteredList({ items, filterTerm }) {
  // Sadece items veya filterTerm değiştiğinde yeniden hesaplanır
  const filteredItems = useMemo(() => {
    console.log('Filtreleme yapılıyor...');
    return items.filter(item => 
      item.name.toLowerCase().includes(filterTerm.toLowerCase())
    );
  }, [items, filterTerm]);
  
  return (
    <ul>
      {filteredItems.map(item => <li key={item.id}>{item.name}</li>)}
    </ul>
  );
}
```

## Verimli State Yönetimi

### State Yapılandırması

İlgili state'leri gruplandırarak veya bağımsız state'lere ayırarak optimizasyon yapın.

```jsx
// İyi bir yaklaşım: Bağımsız state'ler
const [name, setName] = useState('');
const [email, setEmail] = useState('');

// İlişkili state'leri gruplandırma
const [form, setForm] = useState({ name: '', email: '' });
const updateField = (field, value) => {
  setForm(prev => ({ ...prev, [field]: value }));
};
```

### State Güncellemelerini Optimize Etme

Fonksiyonel güncellemeler kullanarak state'in güncel değerine erişin.

```jsx
const [count, setCount] = useState(0);

// Yanlış (önceki state'e dayalı olduğunda sorun çıkarabilir)
// const increment = () => setCount(count + 1);

// Doğru (her zaman güncel state'e dayanır)
const increment = () => setCount(prevCount => prevCount + 1);
```

## Liste Optimizasyonu

### Sabit `key` Prop'u

Listeler için doğru ve sabit `key` prop'ları kullanın.

```jsx
// Yanlış - index kullanımı performans sorunlarına yol açabilir
{items.map((item, index) => (
  <Item key={index} data={item} />
))}

// Doğru - benzersiz ID kullanımı
{items.map(item => (
  <Item key={item.id} data={item} />
))}
```

### Büyük Listelerin Render Edilmesi

Çok uzun listeler için "windowing" tekniklerini kullanın (react-window, react-virtualized).

```jsx
import { FixedSizeList } from 'react-window';

function LongList({ items }) {
  const Row = ({ index, style }) => (
    <div style={style}>{items[index].name}</div>
  );

  return (
    <FixedSizeList
      height={400}
      width={300}
      itemCount={items.length}
      itemSize={35}
    >
      {Row}
    </FixedSizeList>
  );
}
```

## Kod Bölme (Code Splitting)

React.lazy ve Suspense ile dinamik kod bölme yaparak başlangıç yükleme süresini azaltın.

```jsx
import React, { Suspense, lazy } from 'react';

// Bileşen sadece gerektiğinde yüklenecek
const LazyComponent = lazy(() => import('./LazyComponent'));

function App() {
  return (
    <div>
      <Suspense fallback={<div>Yükleniyor...</div>}>
        <LazyComponent />
      </Suspense>
    </div>
  );
}
```

## Render Sürecini İzleme

React DevTools Profiler kullanarak render sürecini izleyin ve performans darboğazlarını belirleyin.

```jsx
// Geliştirme sırasında kullanılır, ürün koduna eklenmez
// npm install --save-dev @welldone-software/why-did-you-render
import React from 'react';
import whyDidYouRender from '@welldone-software/why-did-you-render';

// whyDidYouRender'ı etkinleştir
if (process.env.NODE_ENV === 'development') {
  whyDidYouRender(React, {
    trackAllPureComponents: true,
  });
}

// Sonra izlemek istediğiniz bileşeni işaretleyin
YourComponent.whyDidYouRender = true;
```

## En İyi Performans Pratikleri

1. **Gereksiz Bağımlılıklardan Kaçının:** Hook'ların bağımlılık dizilerini dikkatli tanımlayın
2. **İç İçe Render Fonksiyonlarında Dikkatli Olun:** Render fonksiyonları içinde yeni fonksiyonlar tanımlamaktan kaçının
3. **Throttling ve Debouncing Kullanın:** Sık tetiklenen olaylar (scroll, resize) için kısıtlayıcı teknikler kullanın
4. **Büyük Durumları Yerelleştirin:** Global state'i en düşük seviyede tutun ve mümkünse en alt ortak ataya taşıyın
5. **Erken return veya koşullu render kullanarak gereksiz hesaplamaları önleyin:** `{loading ? <LoadingSpinner /> : <ActualContent />}`

-- Add metadata column to notifications table
-- This will store JSON data for membership requests and other notification types

ALTER TABLE public.notifications 
ADD COLUMN IF NOT EXISTS metadata JSONB;

-- Add comment to explain the column
COMMENT ON COLUMN public.notifications.metadata IS 'JSON metadata for storing additional notification data like membership request details';

-- Create index for better performance when querying metadata
CREATE INDEX IF NOT EXISTS idx_notifications_metadata_gin 
ON public.notifications USING GIN (metadata);

-- Example metadata structure for membership requests:
-- {
--   "requesterId": "uuid",
--   "requesterName": "<PERSON>", 
--   "requesterEmail": "<EMAIL>",
--   "gymId": "uuid",
--   "gymName": "Gym Name",
--   "requestDate": "2024-01-01T00:00:00.000Z"
-- }

export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[];

export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string;
          email: string;
          email_verified_at: string | null;
          phone: string | null;
          phone_verified_at: string | null;
          name: string;
          surname: string;
          is_manager: boolean;
          age: number;
          gender: string;
          height_cm: number;
          weight_kg: number;
          fitness_goal: string;
          profile_picture_url: string | null;
          created_at: string | null;
          updated_at: string | null;
        };
        Insert: {
          id?: string | null;
          email: string;
          email_verified_at?: string | null;
          phone?: string | null;
          phone_verified_at?: string | null;
          name: string;
          surname: string;
          is_manager?: boolean | null;
          age?: number | null;
          gender?: string | null;
          height_cm?: number | null;
          weight_kg?: number | null;
          fitness_goal?: string | null;
          profile_picture_url?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
        Update: {
          id?: string;
          email?: string;
          email_verified_at?: string | null;
          phone?: string | null;
          phone_verified_at?: string | null;
          name?: string;
          surname?: string;
          is_manager?: boolean | null;
          age?: number | null;
          gender?: string | null;
          height_cm?: number | null;
          weight_kg?: number | null;
          fitness_goal?: string | null;
          profile_picture_url?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
      };
      managers: {
        Row: {
          user_id: string; // PK, users.id ile birebir
          platform_package_type: "monthly" | "quarterly" | "yearly";
          tier: "starter" | "professional" | "enterprise";
          subscription_start_date: string | null;
          subscription_end_date: string | null;
          status:
            | "active"
            | "expired"
            | "cancelled"
            | "pending_payment"
            | "failed";
          last_payment_transaction_id: string | null;
          created_at: string | null;
          updated_at: string | null;
        };
        Insert: {
          user_id: string;
          platform_package_type: "monthly" | "quarterly" | "yearly";
          tier?: "starter" | "professional" | "enterprise";
          subscription_start_date?: string | null;
          subscription_end_date?: string | null;
          status?:
            | "active"
            | "expired"
            | "cancelled"
            | "pending_payment"
            | "failed";
          last_payment_transaction_id?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
        Update: {
          user_id?: string;
          platform_package_type?: "monthly" | "quarterly" | "yearly";
          tier?: "starter" | "professional" | "enterprise";
          subscription_start_date?: string | null;
          subscription_end_date?: string | null;
          status?:
            | "active"
            | "expired"
            | "cancelled"
            | "pending_payment"
            | "failed";
          last_payment_transaction_id?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
      };

      platform_packages: {
        Row: {
          id: string;
          name: string;
          tier: "starter" | "professional" | "enterprise";
          duration: "monthly" | "quarterly" | "yearly";
          price_amount: number;
          max_gyms: number | null;
          max_members: number | null;
          features: Record<string, any>;
          is_active: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          tier: "starter" | "professional" | "enterprise";
          duration: "monthly" | "quarterly" | "yearly";
          price_amount: number;
          max_gyms?: number | null;
          max_members?: number | null;
          features?: Record<string, any>;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          tier?: "starter" | "professional" | "enterprise";
          duration?: "monthly" | "quarterly" | "yearly";
          price_amount?: number;
          max_gyms?: number | null;
          max_members?: number | null;
          features?: Record<string, any>;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };

      gyms: {
        Row: {
          id: string;
          manager_user_id: string;
          name: string;
          address: string | null;
          city: string | null;
          district: string | null;
          gym_phone: string | null;
          email: string | null;
          description: string | null;
          gym_type: string | null;
          status: string;
          logo_url: string | null;
          cover_image_url: string | null;
          created_at: string;
          updated_at: string;
          features: string[];
          slug: string;
        };
        Insert: {
          id?: string;
          manager_user_id: string;
          name: string;
          address?: string | null;
          city?: string | null;
          district?: string | null;
          gym_phone?: string | null;
          email?: string | null;
          description?: string | null;
          gym_type?: string | null;
          status?: string;
          logo_url?: string | null;
          cover_image_url?: string | null;
          created_at?: string;
          updated_at?: string;
          features?: string[];
          slug?: string;
        };
        Update: {
          id?: string;
          manager_user_id?: string;
          name?: string;
          address?: string | null;
          city?: string | null;
          district?: string | null;
          gym_phone?: string | null;
          email?: string | null;
          description?: string | null;
          gym_type?: string | null;
          status?: string;
          logo_url?: string | null;
          cover_image_url?: string | null;
          created_at?: string;
          updated_at?: string;
          features?: string[];
          slug?: string;
        };
      };

      gym_packages: {
        Row: {
          id: string;
          gym_id: string;
          name: string;
          package_type: "monthly" | "quarterly" | "yearly" | "daily" | "trial";
          duration_days: number | null;
          price_amount: number;
          description: string | null;
          is_active: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          gym_id: string;
          name: string;
          package_type: "monthly" | "quarterly" | "yearly" | "daily" | "trial";
          duration_days?: number | null;
          price_amount: number;
          description?: string | null;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          gym_id?: string;
          name?: string;
          package_type?: "monthly" | "quarterly" | "yearly" | "daily" | "trial";
          duration_days?: number | null;
          price_amount?: number;
          description?: string | null;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };
      memberships: {
        Row: {
          id: string;
          user_id: string;
          gym_id: string;
          status: "active" | "passive";
          approved_at: string | null;
          request_date: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          gym_id: string;
          status: "active" | "passive";
          approved_at?: string | null;
          request_date?: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          gym_id?: string;
          status?: "active" | "passive";
          approved_at?: string | null;
          request_date?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
      subscriptions: {
        Row: {
          id: string;
          membership_id: string;
          gym_package_id: string;
          purchase_price: number;
          start_date: string;
          end_date: string | null;
          payment_status: string;
          payment_transaction_id: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          membership_id: string;
          gym_package_id: string;
          purchase_price: number;
          start_date: string;
          end_date?: string | null;
          payment_status?: string;
          payment_transaction_id?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          membership_id?: string;
          gym_package_id?: string;
          purchase_price?: number;
          start_date?: string;
          end_date?: string | null;
          payment_status?: string;
          payment_transaction_id?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      reviews: {
        Row: {
          id: string;
          user_id: string;
          gym_id: string;
          rating: number;
          comment: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          gym_id: string;
          rating: number;
          comment?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          gym_id?: string;
          rating?: number;
          comment?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      notifications: {
        Row: {
          id: string;
          user_id: string;
          title: string;
          message: string;
          type: string;
          is_read: boolean;
          related_entity_type: string | null;
          related_entity_id: string | null;
          metadata: string | null;
          created_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          title: string;
          message: string;
          type: string;
          is_read?: boolean;
          related_entity_type?: string | null;
          related_entity_id?: string | null;
          metadata?: string | null;
          created_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          title?: string;
          message?: string;
          type?: string;
          is_read?: boolean;
          related_entity_type?: string | null;
          related_entity_id?: string | null;
          metadata?: string | null;
          created_at?: string;
        };
      };
      gym_invitations: {
        Row: {
          id: string;
          inviter_user_id: string;
          invitee_user_id: string;
          gym_id: string;
          invitation_type: "member_request" | "manager_invite";
          status: "pending" | "accepted" | "rejected" | "expired";
          message: string | null;
          expires_at: string | null;
          responded_at: string | null;
          created_at: string | null;
          updated_at: string | null;
        };
        Insert: {
          id?: string;
          inviter_user_id: string;
          invitee_user_id: string;
          gym_id: string;
          invitation_type: "member_request" | "manager_invite";
          status?: "pending" | "accepted" | "rejected" | "expired";
          message?: string | null;
          expires_at?: string | null;
          responded_at?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
        Update: {
          id?: string;
          inviter_user_id?: string;
          invitee_user_id?: string;
          gym_id?: string;
          invitation_type?: "member_request" | "manager_invite";
          status?: "pending" | "accepted" | "rejected" | "expired";
          message?: string | null;
          expires_at?: string | null;
          responded_at?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
      };
      announcements: {
        Row: {
          id: string;
          gym_id: string;
          title: string;
          content: string;
          is_public: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          gym_id: string;
          title: string;
          content: string;
          is_public?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          gym_id?: string;
          title?: string;
          content?: string;
          is_public?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };
      attendance_records: {
        Row: {
          id: string;
          user_id: string | null;
          gym_id: string | null;
          check_in_time: string;
          check_out_time: string | null;
          created_at: string | null;
          updated_at: string | null;
        };
        Insert: {
          id?: string;
          user_id?: string | null;
          gym_id?: string | null;
          check_in_time: string;
          check_out_time?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
        Update: {
          id?: string;
          user_id?: string | null;
          gym_id?: string | null;
          check_in_time?: string;
          check_out_time?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
      };
      body_measurements: {
        Row: {
          id: string;
          user_id: string | null;
          record_date: string;
          measurement_type: string;
          value_cm: number;
          created_at: string | null;
          updated_at: string | null;
        };
        Insert: {
          id?: string;
          user_id?: string | null;
          record_date: string;
          measurement_type: string;
          value_cm: number;
          created_at?: string | null;
          updated_at?: string | null;
        };
        Update: {
          id?: string;
          user_id?: string | null;
          record_date?: string;
          measurement_type?: string;
          value_cm?: number;
          created_at?: string | null;
          updated_at?: string | null;
        };
      };
      progress_records: {
        Row: {
          id: string;
          user_id: string | null;
          record_date: string;
          weight_kg: number | null;
          body_fat_percentage: number | null;
          muscle_mass_kg: number | null;
          notes: string | null;
          created_at: string | null;
          updated_at: string | null;
        };
        Insert: {
          id?: string;
          user_id?: string | null;
          record_date: string;
          weight_kg?: number | null;
          body_fat_percentage?: number | null;
          muscle_mass_kg?: number | null;
          notes?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
        Update: {
          id?: string;
          user_id?: string | null;
          record_date?: string;
          weight_kg?: number | null;
          body_fat_percentage?: number | null;
          muscle_mass_kg?: number | null;
          notes?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
      };
      messages: {
        Row: {
          id: string;
          sender_id: string | null;
          recipient_id: string | null;
          content: string;
          is_read: boolean | null;
          created_at: string | null;
          updated_at: string | null;
        };
        Insert: {
          id?: string;
          sender_id?: string | null;
          recipient_id?: string | null;
          content: string;
          is_read?: boolean | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
        Update: {
          id?: string;
          sender_id?: string | null;
          recipient_id?: string | null;
          content?: string;
          is_read?: boolean | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
      };
      workout_progress: {
        Row: {
          id: string;
          user_id: string | null;
          record_date: string;
          exercise_name: string;
          weight_kg: number | null;
          reps: number | null;
          sets: number | null;
          duration_minutes: number | null;
          distance_km: number | null;
          notes: string | null;
          created_at: string | null;
          updated_at: string | null;
        };
        Insert: {
          id?: string;
          user_id?: string | null;
          record_date: string;
          exercise_name: string;
          weight_kg?: number | null;
          reps?: number | null;
          sets?: number | null;
          duration_minutes?: number | null;
          distance_km?: number | null;
          notes?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
        Update: {
          id?: string;
          user_id?: string | null;
          record_date?: string;
          exercise_name?: string;
          weight_kg?: number | null;
          reps?: number | null;
          sets?: number | null;
          duration_minutes?: number | null;
          distance_km?: number | null;
          notes?: string | null;
          created_at?: string | null;
          updated_at?: string | null;
        };
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      [_ in never]: never;
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
}

export type Tables<T extends keyof Database["public"]["Tables"]> =
  Database["public"]["Tables"][T]["Row"];
export type InsertTables<T extends keyof Database["public"]["Tables"]> =
  Database["public"]["Tables"][T]["Insert"];
export type UpdateTables<T extends keyof Database["public"]["Tables"]> =
  Database["public"]["Tables"][T]["Update"];
export type Gyms = Tables<"gyms">;
export type Users = Tables<"users">;
export type Managers = Tables<"managers">;
export type PlatformPackages = Tables<"platform_packages">;
export type GymPackages = Tables<"gym_packages">;
export type Memberships = Tables<"memberships">;
export type Subscriptions = Tables<"subscriptions">;
export type Reviews = Tables<"reviews">;
export type Notifications = Tables<"notifications">;
export type Announcements = Tables<"announcements">;
export type AttendanceRecords = Tables<"attendance_records">;
export type BodyMeasurements = Tables<"body_measurements">;
export type ProgressRecords = Tables<"progress_records">;
export type Messages = Tables<"messages">;
export type WorkoutProgress = Tables<"workout_progress">;

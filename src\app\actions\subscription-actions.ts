"use server";

import { createAction } from "@/lib/actions/core";
import { ApiResponse } from "@/lib/actions/types";
import { createNotification } from "@/lib/actions/notifications";
import { z } from "zod";
import type { Tables, InsertTables, UpdateTables } from "@/lib/supabase/types";

/**
 * Paket satın alma işlemi
 */
export async function purchasePackage(
  userId: string,
  gymId: string,
  packageId: string
): Promise<ApiResponse> {
  if (!userId || !gymId || !packageId) {
    return {
      success: false,
      error: "Gerekli bilgiler eksik.",
    };
  }

  return await createAction(
    async (_, supabase) => {
      // Paket bilgilerini al
      const { data: gymPackage, error: packageError } = await supabase
        .from("gym_packages")
        .select("*")
        .eq("id", packageId)
        .single();

      if (packageError || !gymPackage) {
        throw new Error("Paket bilgileri alınamadı.");
      }

      // Kullanıcının üyeliği var mı kontrol et
      const { data: membership, error: membershipError } = await supabase
        .from("memberships")
        .select("*")
        .eq("user_id", userId)
        .eq("gym_id", gymId)
        .in("status", ["approved_passive", "active"])
        .maybeSingle();

      if (membershipError || !membership) {
        throw new Error(
          "Bu işlemi gerçekleştirebilmek için onaylı üyeliğiniz olmalıdır."
        );
      }

      // Paket başlangıç ve bitiş tarihlerini hesapla
      const startDate = new Date();
      let endDate: Date;

      // Paket süresine göre bitiş tarihini hesapla
      endDate = new Date(startDate);
      endDate.setDate(endDate.getDate() + (gymPackage.duration_days || 30));

      // Aboneliği oluştur
      const { data: subscription, error: subscriptionError } = await supabase
        .from("subscriptions")
        .insert({
          user_id: userId,
          gym_id: gymId,
          gym_package_id: packageId,
          start_date: startDate.toISOString(),
          end_date: endDate.toISOString(),
          payment_status: "pending",
          created_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (subscriptionError) {
        throw new Error(
          `Abonelik oluşturulurken hata: ${subscriptionError.message}`
        );
      }

      // Salon bilgilerini al
      const { data: gym, error: gymError } = await supabase
        .from("gyms")
        .select("*")
        .eq("id", gymId)
        .single();

      if (gymError || !gym) {
        throw new Error("Salon bilgileri alınamadı.");
      }

      // Kullanıcıya bildirim gönder
      await createNotification({
        userId,
        title: "Paket Satın Alma",
        message: `${gym.name} salonunda ${gymPackage.name} paketini satın aldınız. Ödeme onayı bekleniyor.`,
        type: "package_purchased",
        relatedEntityType: "subscriptions",
        relatedEntityId: subscription.id,
      });

      // Salon yöneticisine bildirim gönder
      await createNotification({
        userId: gym.manager_user_id,
        title: "Yeni Paket Satın Alma",
        message: `Bir kullanıcı ${gymPackage.name} paketini satın aldı. Ödemeyi onaylayın.`,
        type: "package_purchased",
        relatedEntityType: "subscriptions",
        relatedEntityId: subscription.id,
      });

      return subscription;
    },
    { revalidatePaths: ["/dashboard/member"] }
  );
}

/**
 * Abonelik ödemesini onaylar
 */
export async function confirmSubscriptionPayment(
  subscriptionId: string
): Promise<ApiResponse> {
  if (!subscriptionId) {
    return {
      success: false,
      error: "Abonelik ID'si gereklidir.",
    };
  }

  return await createAction(
    async (_, supabase) => {
      // Abonelik bilgilerini al
      const { data: subscription, error: fetchError } = await supabase
        .from("subscriptions")
        .select("*")
        .eq("id", subscriptionId)
        .single();

      if (fetchError || !subscription) {
        throw new Error("Abonelik bilgileri alınamadı.");
      }

      // Aboneliği güncelle
      const { error: updateError } = await supabase
        .from("subscriptions")
        .update({
          payment_status: "completed",
          updated_at: new Date().toISOString(),
        })
        .eq("id", subscriptionId);

      if (updateError) {
        throw new Error(`Abonelik güncellenirken hata: ${updateError.message}`);
      }

      // Üyeliği aktif olarak işaretle
      const { error: membershipError } = await supabase
        .from("memberships")
        .update({
          status: "active",
          updated_at: new Date().toISOString(),
        })
        .eq("user_id", subscription.user_id)
        .eq("gym_id", subscription.gym_id);

      if (membershipError) {
        throw new Error(
          `Üyelik güncellenirken hata: ${membershipError.message}`
        );
      }

      // Paket bilgilerini al
      const { data: gymPackage, error: packageError } = await supabase
        .from("gym_packages")
        .select("name")
        .eq("id", subscription.gym_package_id)
        .single();

      // Salon bilgilerini al
      const { data: gym, error: gymError } = await supabase
        .from("gyms")
        .select("name")
        .eq("id", subscription.gym_id)
        .single();

      // Kullanıcıya bildirim gönder
      await createNotification({
        userId: subscription.user_id,
        title: "Ödeme Onaylandı",
        message: `${gym?.name || "Salon"} için ${
          gymPackage?.name || "paket"
        } ödemesi onaylandı. Üyeliğiniz aktifleştirildi.`,
        type: "package_purchase_confirmed",
        relatedEntityType: "subscriptions",
        relatedEntityId: subscriptionId,
      });

      return subscription;
    },
    { revalidatePaths: ["/dashboard/manager/members", "/dashboard/member"] }
  );
}

/**
 * Aboneliği iptal eder
 */
export async function cancelSubscription(
  subscriptionId: string
): Promise<ApiResponse> {
  if (!subscriptionId) {
    return {
      success: false,
      error: "Abonelik ID'si gereklidir.",
    };
  }

  return await createAction(
    async (_, supabase) => {
      // Aboneliği güncelle
      const { data: subscription, error } = await supabase
        .from("subscriptions")
        .update({
          is_canceled: true,
          canceled_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })
        .eq("id", subscriptionId)
        .select()
        .single();

      if (error) {
        throw new Error(`Abonelik iptal edilirken hata: ${error.message}`);
      }

      return subscription;
    },
    { revalidatePaths: ["/dashboard/member", "/dashboard/manager/members"] }
  );
}

/**
 * Süreleri dolmuş abonelikleri kontrol et ve üyeliği pasife çevir
 */
export async function checkExpiredSubscriptions(): Promise<ApiResponse> {
  return await createAction(async (_, supabase) => {
    const now = new Date().toISOString();

    // Süresi dolmuş abonelikleri bul
    const { data: expiredSubscriptions, error } = await supabase
      .from("subscriptions")
      .select("id, user_id, gym_id, end_date")
      .lt("end_date", now)
      .eq("is_canceled", false)
      .eq("payment_status", "completed");

    if (error) {
      throw new Error(
        `Süresi dolmuş abonelikler alınırken hata: ${error.message}`
      );
    }

    // Her bir süresi dolmuş abonelik için işlem yap
    for (const subscription of expiredSubscriptions || []) {
      // Bu kullanıcının aktif başka aboneliği var mı kontrol et
      const { data: activeSubscriptions, error: activeError } = await supabase
        .from("subscriptions")
        .select("id")
        .eq("user_id", subscription.user_id)
        .eq("gym_id", subscription.gym_id)
        .gt("end_date", now)
        .eq("is_canceled", false)
        .eq("payment_status", "completed");

      if (!activeError && !activeSubscriptions?.length) {
        // Aktif abonelik yoksa üyeliği pasif yap
        await supabase
          .from("memberships")
          .update({
            status: "approved_passive",
            updated_at: now,
          })
          .eq("user_id", subscription.user_id)
          .eq("gym_id", subscription.gym_id);

        // Kullanıcıya bildirim gönder
        await createNotification({
          userId: subscription.user_id,
          title: "Üyelik Durumu Değişikliği",
          message:
            "Aktif paketinizin süresi doldu, üyeliğiniz pasif duruma geçti.",
          type: "membership_expiry",
          relatedEntityType: "subscriptions",
          relatedEntityId: subscription.id,
        });
      }
    }

    return { processed: expiredSubscriptions?.length || 0 };
  });
}

// Validasyon şemaları
const subscriptionSchema = z.object({
  userId: z.string().uuid("Geçerli bir kullanıcı ID'si gereklidir"),
  gymId: z.string().uuid("Geçerli bir salon ID'si gereklidir"),
  membershipId: z.string().uuid("Geçerli bir üyelik ID'si gereklidir"),
  gymPackageId: z.string().uuid("Geçerli bir paket ID'si gereklidir"),
});

/**
 * Verilen üyelik ID'sine göre abonelikleri getirir
 * @param membershipId Üyelik ID'si
 * @returns Abonelikler veya hata
 */
export async function getSubscriptionsByMembershipId(
  membershipId: string
): Promise<ApiResponse<Tables<"subscriptions">[]>> {
  if (!membershipId) {
    return { success: false, error: "Üyelik ID'si gereklidir." };
  }

  return await createAction(
    async (_, supabase, authUserId) => {
      // İlgili üyeliğin kullanıcısını bul
      const { data: membership } = await supabase
        .from("memberships")
        .select("user_id, gym_id")
        .eq("id", membershipId)
        .single();

      if (!membership) {
        throw new Error("Üyelik bulunamadı.");
      }

      // Salon yöneticisinin yetkisini kontrol et
      const { data: gym } = await supabase
        .from("gyms")
        .select("manager_user_id")
        .eq("id", membership.gym_id)
        .single();

      // Kullanıcının kendi verisine veya yöneticinin salon üyelerinin verilerine erişim izni var
      const isOwner = membership.user_id === authUserId;
      const isManager = gym?.manager_user_id === authUserId;

      if (!isOwner && !isManager) {
        throw new Error("Bu abonelikleri görüntüleme yetkiniz bulunmuyor.");
      }

      const { data, error } = await supabase
        .from("subscriptions")
        .select(
          `
        *,
        gym_package:gym_packages(*)
      `
        )
        .eq("membership_id", membershipId);

      if (error) {
        throw new Error(`Abonelikler getirilirken hata: ${error.message}`);
      }

      return data || [];
    },
    { requireAuth: true }
  );
}

/**
 * Verilen üyelik ID'sine göre aktif abonelikleri getirir
 * @param membershipId Üyelik ID'si
 * @returns Aktif abonelikler veya hata
 */
export async function getActiveSubscriptionsByMembershipId(
  membershipId: string
): Promise<ApiResponse<Tables<"subscriptions">[]>> {
  if (!membershipId) {
    return { success: false, error: "Üyelik ID'si gereklidir." };
  }

  return await createAction(
    async (_, supabase, authUserId) => {
      // İlgili üyeliğin kullanıcısını bul
      const { data: membership } = await supabase
        .from("memberships")
        .select("user_id, gym_id")
        .eq("id", membershipId)
        .single();

      if (!membership) {
        throw new Error("Üyelik bulunamadı.");
      }

      // Salon yöneticisinin yetkisini kontrol et
      const { data: gym } = await supabase
        .from("gyms")
        .select("manager_user_id")
        .eq("id", membership.gym_id)
        .single();

      // Kullanıcının kendi verisine veya yöneticinin salon üyelerinin verilerine erişim izni var
      const isOwner = membership.user_id === authUserId;
      const isManager = gym?.manager_user_id === authUserId;

      if (!isOwner && !isManager) {
        throw new Error("Bu abonelikleri görüntüleme yetkiniz bulunmuyor.");
      }

      const now = new Date().toISOString();

      const { data, error } = await supabase
        .from("subscriptions")
        .select(
          `
        *,
        gym_package:gym_packages(*)
      `
        )
        .eq("membership_id", membershipId)
        .eq("payment_status", "completed")
        .gt("end_date", now)
        .is("is_canceled", false);

      if (error) {
        throw new Error(
          `Aktif abonelikler getirilirken hata: ${error.message}`
        );
      }

      return data || [];
    },
    { requireAuth: true }
  );
}

/**
 * Abonelik oluşturur
 * @param formData Abonelik bilgileri
 * @returns Oluşturulan abonelik veya hata
 */
export async function createSubscription(
  formData: FormData
): Promise<ApiResponse<Tables<"subscriptions">>> {
  // Form verilerini al
  const userId = formData.get("userId") as string;
  const gymId = formData.get("gymId") as string;
  const membershipId = formData.get("membershipId") as string;
  const gymPackageId = formData.get("gymPackageId") as string;

  // Validasyon
  if (!userId || !gymId || !membershipId || !gymPackageId) {
    return {
      success: false,
      error: "Gerekli bilgiler eksik.",
    };
  }

  return await createAction(
    async (_, supabase, authUserId) => {
      // Yetki kontrolü - Sadece salon yöneticileri abonelik oluşturabilir
      const { data: gym } = await supabase
        .from("gyms")
        .select("manager_user_id")
        .eq("id", gymId)
        .single();

      if (!gym || gym.manager_user_id !== authUserId) {
        throw new Error("Abonelik oluşturmak için yetkiniz bulunmuyor.");
      }

      // Paket bilgilerini al
      const { data: gymPackage, error: packageError } = await supabase
        .from("gym_packages")
        .select("*")
        .eq("id", gymPackageId)
        .single();

      if (packageError || !gymPackage) {
        throw new Error("Paket bilgileri alınamadı.");
      }

      // Paket başlangıç ve bitiş tarihlerini hesapla
      const startDate = new Date();
      let endDate: Date;

      // Paket süresine göre bitiş tarihini hesapla
      endDate = new Date(startDate);
      endDate.setDate(endDate.getDate() + (gymPackage.duration_days || 30));

      // Aboneliği oluştur
      const { data: subscription, error: subscriptionError } = await supabase
        .from("subscriptions")
        .insert({
          user_id: userId,
          gym_id: gymId,
          membership_id: membershipId,
          gym_package_id: gymPackageId,
          start_date: startDate.toISOString(),
          end_date: endDate.toISOString(),
          payment_status: "completed", // Yönetici doğrudan onaylı oluşturur

          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (subscriptionError) {
        throw new Error(
          `Abonelik oluşturulurken hata: ${subscriptionError.message}`
        );
      }

      // Üyeliği aktif olarak işaretle
      await supabase
        .from("memberships")
        .update({
          status: "active",
          updated_at: new Date().toISOString(),
        })
        .eq("id", membershipId);

      // Kullanıcıya bildirim gönder
      await createNotification({
        userId,
        title: "Yeni Abonelik",
        message: `${gymPackage.name} paketi aboneliğiniz başarıyla oluşturuldu.`,
        type: "package_purchased",
        relatedEntityType: "subscriptions",
        relatedEntityId: subscription.id,
      });

      return subscription;
    },
    {
      requireAuth: true,
      revalidatePaths: [
        "/dashboard/manager/members",
        `/dashboard/manager/${gymId}/members`,
      ],
    }
  );
}

/**
 * Abonelik bilgilerini günceller
 * @param subscriptionId Abonelik ID'si
 * @param subscriptionData Güncellenecek abonelik bilgileri
 * @returns Güncellenmiş abonelik veya hata
 */
export async function updateSubscription(
  subscriptionId: string,
  subscriptionData: UpdateTables<"subscriptions">
): Promise<ApiResponse<Tables<"subscriptions">>> {
  if (!subscriptionId) {
    return { success: false, error: "Abonelik ID'si gereklidir." };
  }

  return await createAction(
    async (_, supabase, authUserId) => {
      // Abonelik bilgilerini al
      const { data: subscription } = await supabase
        .from("subscriptions")
        .select("gym_id")
        .eq("id", subscriptionId)
        .single();

      if (!subscription) {
        throw new Error("Abonelik bulunamadı.");
      }

      // Salon yöneticisinin yetkisini kontrol et
      const { data: gym } = await supabase
        .from("gyms")
        .select("manager_user_id")
        .eq("id", subscription.gym_id)
        .single();

      if (!gym || gym.manager_user_id !== authUserId) {
        throw new Error("Bu aboneliği güncellemek için yetkiniz bulunmuyor.");
      }

      // Aboneliği güncelle
      const { data: updatedSubscription, error } = await supabase
        .from("subscriptions")
        .update({
          ...subscriptionData,
          updated_at: new Date().toISOString(),
        })
        .eq("id", subscriptionId)
        .select()
        .single();

      if (error) {
        throw new Error(`Abonelik güncellenirken hata: ${error.message}`);
      }

      return updatedSubscription;
    },
    { requireAuth: true, revalidatePaths: ["/dashboard/manager/members"] }
  );
}

/**
 * Tüm aktif platform paketlerini getirir
 */
export async function getAllPlatformPackages(): Promise<
  ApiResponse<Tables<"platform_packages">[]>
> {
  return await createAction(async (_, supabase) => {
    const { data, error } = await supabase
      .from("platform_packages")
      .select("*")
      .eq("is_active", true)
      .order("tier", { ascending: true })
      .order("duration", { ascending: true });

    if (error) {
      throw new Error(`Platform paketleri getirilirken hata: ${error.message}`);
    }

    return data || [];
  });
}

/**
 * Platform paketlerini tier'a göre getirir
 */
export async function getPlatformPackagesByTier(
  tier: string
): Promise<ApiResponse<Tables<"platform_packages">[]>> {
  return await createAction(async (_, supabase) => {
    const { data, error } = await supabase
      .from("platform_packages")
      .select("*")
      .eq("tier", tier)
      .eq("is_active", true);

    if (error) {
      throw new Error(`Platform paketleri getirilirken hata: ${error.message}`);
    }

    return data || [];
  });
}

/**
 * Kullanıcının mevcut manager abonelik bilgilerini getirir
 */
export async function getCurrentManagerSubscription(userId: string): Promise<
  ApiResponse<{
    tier: string;
    duration: string;
    packageName: string;
    maxGyms: number | null;
    maxMembers: number | null;
    features: Record<string, any>;
    subscriptionEndDate: string;
    subscriptionStartDate: string;
    status: string;
    priceAmount: number;
  } | null>
> {
  return await createAction(async (_, supabase) => {
    // Yöneticinin bilgilerini al
    const { data: managerData, error: managerError } = await supabase
      .from("managers")
      .select(
        "tier, platform_package_type, subscription_end_date, subscription_start_date, status"
      )
      .eq("user_id", userId)
      .single();

    if (managerError) {
      // Kullanıcı manager değilse null döndür
      if (managerError.code === "PGRST116") {
        return null;
      }
      throw new Error(`Yönetici bilgileri alınamadı: ${managerError.message}`);
    }

    if (!managerData) {
      return null;
    }

    // Platform paket bilgilerini al
    const { data: packageData, error: packageError } = await supabase
      .from("platform_packages")
      .select("name, max_gyms, max_members, features, price_amount")
      .eq("tier", managerData.tier)
      .eq("duration", managerData.platform_package_type)
      .eq("is_active", true)
      .single();

    if (packageError) {
      throw new Error(
        `Platform paketi bilgileri alınamadı: ${packageError.message}`
      );
    }

    return {
      tier: managerData.tier,
      duration: managerData.platform_package_type,
      packageName: packageData.name,
      maxGyms: packageData.max_gyms,
      maxMembers: packageData.max_members,
      features: packageData.features,
      subscriptionEndDate: managerData.subscription_end_date,
      subscriptionStartDate: managerData.subscription_start_date,
      status: managerData.status,
      priceAmount: packageData.price_amount,
    };
  });
}

/**
 * Yöneticinin platform abonelik geçmişini getirir
 */
export async function getManagerSubscriptionHistory(userId: string): Promise<
  ApiResponse<
    {
      tier: string;
      duration: string;
      packageName: string;
      priceAmount: number;
      subscriptionStartDate: string;
      subscriptionEndDate: string;
      status: string;
      isActive: boolean;
    }[]
  >
> {
  return await createAction(async (_, supabase) => {
    // Audit logs'dan manager subscription geçmişini al
    const { data: auditData, error: auditError } = await supabase
      .from("audit_logs")
      .select("*")
      .eq("user_id", userId)
      .eq("action", "create_manager")
      .order("created_at", { ascending: false });

    if (auditError) {
      throw new Error(`Abonelik geçmişi alınamadı: ${auditError.message}`);
    }

    // Mevcut manager bilgilerini de al
    const { data: currentManager, error: managerError } = await supabase
      .from("managers")
      .select(
        "tier, platform_package_type, subscription_start_date, subscription_end_date, status"
      )
      .eq("user_id", userId)
      .single();

    const history: {
      tier: string;
      duration: string;
      packageName: string;
      priceAmount: number;
      subscriptionStartDate: string;
      subscriptionEndDate: string;
      status: string;
      isActive: boolean;
    }[] = [];

    // Mevcut aboneliği ekle
    if (currentManager && !managerError) {
      const { data: currentPackage } = await supabase
        .from("platform_packages")
        .select("name, price_amount")
        .eq("tier", currentManager.tier)
        .eq("duration", currentManager.platform_package_type)
        .eq("is_active", true)
        .single();

      if (currentPackage) {
        history.push({
          tier: currentManager.tier,
          duration: currentManager.platform_package_type,
          packageName: currentPackage.name,
          priceAmount: currentPackage.price_amount,
          subscriptionStartDate: currentManager.subscription_start_date || "",
          subscriptionEndDate: currentManager.subscription_end_date || "",
          status: currentManager.status,
          isActive: currentManager.status === "active",
        });
      }
    }

    // Audit logs'dan geçmiş abonelikleri ekle
    for (const audit of auditData || []) {
      if (audit.new_values && typeof audit.new_values === "object") {
        const newValues = audit.new_values as any;
        if (
          newValues.tier &&
          newValues.package_type &&
          newValues.price_amount
        ) {
          // Bu abonelik mevcut abonelik değilse ekle
          const isCurrentSubscription =
            currentManager &&
            currentManager.tier === newValues.tier &&
            currentManager.platform_package_type === newValues.package_type;

          if (!isCurrentSubscription) {
            history.push({
              tier: newValues.tier,
              duration: newValues.package_type,
              packageName:
                audit.metadata?.package_name ||
                `${newValues.tier} ${newValues.package_type}`,
              priceAmount: newValues.price_amount,
              subscriptionStartDate:
                newValues.subscription_start || audit.created_at,
              subscriptionEndDate: newValues.subscription_end || "",
              status: "expired",
              isActive: false,
            });
          }
        }
      }
    }

    return history;
  });
}

/**
 * Yöneticinin mevcut tier'ına göre salon limitini getirir
 */
export async function getManagerGymLimit(
  userId: string
): Promise<ApiResponse<{ maxGyms: number | null; tier: string }>> {
  return await createAction(async (_, supabase) => {
    // Yöneticinin tier'ını al
    const { data: managerData, error: managerError } = await supabase
      .from("managers")
      .select("tier")
      .eq("user_id", userId)
      .single();

    if (managerError) {
      throw new Error(`Yönetici bilgileri alınamadı: ${managerError.message}`);
    }

    if (!managerData?.tier) {
      throw new Error("Yönetici tier bilgisi bulunamadı");
    }

    // Bu tier'a ait platform paketlerinden birini al (max_gyms aynı olacak)
    const { data: packageData, error: packageError } = await supabase
      .from("platform_packages")
      .select("max_gyms")
      .eq("tier", managerData.tier)
      .eq("is_active", true)
      .limit(1)
      .single();

    if (packageError) {
      throw new Error(
        `Platform paketi bilgileri alınamadı: ${packageError.message}`
      );
    }

    return {
      maxGyms: packageData.max_gyms,
      tier: managerData.tier,
    };
  });
}

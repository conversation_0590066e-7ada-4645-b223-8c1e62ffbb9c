"use server";
import { InsertTables } from "@/lib/supabase/types";
import { createAction } from "./core";
import { ApiResponse } from "./types";
import { getSupabaseAdmin } from "@/utils/supabase/admin";

export type NotificationTypes =
  | "new_announcement"
  | "membership_request"
  | "membership_request_sent"
  | "membership_approved"
  | "membership_rejected"
  | "new_review"
  | "package_created"
  | "package_purchased"
  | "package_purchase_confirmed"
  | "message_received"
  | "attendance_reminder"
  | "membership_expiry";

export type EntityType =
  | "announcements"
  | "memberships"
  | "reviews"
  | "gym_packages"
  | "subscriptions"
  | "messages"
  | "gyms"
  | "users"
  | "attendances"
  | "gym_membership_request";

export type CreateNotificationParams = {
  userId: string;
  title: string;
  message: string;
  type: NotificationTypes;
  relatedEntityType?: EntityType;
  relatedEntityId?: string;
  metadata?: Record<string, any>;
};

/**
 * <PERSON><PERSON><PERSON>im oluşturmak için helper fonksiyon
 * Admin client kullanarak RLS politikalarını bypass eder
 */
export async function createNotification(
  params: CreateNotificationParams
): Promise<ApiResponse> {
  try {
    const {
      userId,
      title,
      message,
      type,
      relatedEntityType,
      relatedEntityId,
      metadata,
    } = params;

    const notification: InsertTables<"notifications"> = {
      user_id: userId,
      title,
      message,
      type,
      is_read: false,
      related_entity_type: relatedEntityType,
      related_entity_id: relatedEntityId,
      metadata: metadata ? JSON.stringify(metadata) : null,
      created_at: new Date().toISOString(),
    };

    // Admin client kullanarak RLS politikalarını bypass et
    const adminClient = getSupabaseAdmin();
    const { data, error } = await adminClient
      .from("notifications")
      .insert(notification)
      .select()
      .single();

    if (error) {
      throw new Error(`Bildirim oluşturulurken hata: ${error.message}`);
    }

    return { success: true, data };
  } catch (error: any) {
    console.error("Notification creation error:", error);
    return {
      success: false,
      error: error.message || "Bildirim oluşturulurken bir hata oluştu.",
    };
  }
}

/**
 * Bildirim grupları oluşturma (birden fazla kullanıcıya aynı bildirim)
 * Admin client kullanarak RLS politikalarını bypass eder
 */
export async function createBulkNotifications(
  userIds: string[],
  notificationParams: Omit<CreateNotificationParams, "userId">
): Promise<ApiResponse> {
  try {
    const notifications = userIds.map((userId) => ({
      user_id: userId,
      title: notificationParams.title,
      message: notificationParams.message,
      type: notificationParams.type,
      is_read: false,
      related_entity_type: notificationParams.relatedEntityType,
      related_entity_id: notificationParams.relatedEntityId,
      created_at: new Date().toISOString(),
    }));

    // Admin client kullanarak RLS politikalarını bypass et
    const adminClient = getSupabaseAdmin();
    const { data, error } = await adminClient
      .from("notifications")
      .insert(notifications)
      .select();

    if (error) {
      throw new Error(
        `Toplu bildirimler oluşturulurken hata: ${error.message}`
      );
    }

    return { success: true, data };
  } catch (error: any) {
    console.error("Bulk notifications creation error:", error);
    return {
      success: false,
      error:
        error.message || "Toplu bildirimler oluşturulurken bir hata oluştu.",
    };
  }
}

/**
 * Bildirimleri okundu olarak işaretle
 */
export async function markNotificationsAsRead(
  notificationIds: string[]
): Promise<ApiResponse> {
  return await createAction<{ updated: number }, null>(
    async (_, supabase) => {
      const { error } = await supabase
        .from("notifications")
        .update({ is_read: true })
        .in("id", notificationIds);

      if (error) {
        throw new Error(
          `Bildirimler okundu olarak işaretlenirken hata: ${error.message}`
        );
      }

      return { updated: notificationIds.length };
    },
    { revalidatePaths: ["/dashboard"] }
  );
}

/**
 * Bildirimleri temizle / sil
 */
export async function deleteNotifications(
  notificationIds: string[]
): Promise<ApiResponse> {
  return await createAction<{ deleted: number }, null>(
    async (_, supabase) => {
      const { error } = await supabase
        .from("notifications")
        .delete()
        .in("id", notificationIds);

      if (error) {
        throw new Error(`Bildirimler silinirken hata: ${error.message}`);
      }

      return { deleted: notificationIds.length };
    },
    { revalidatePaths: ["/dashboard"] }
  );
}

/**
 * Bir kullanıcının tüm bildirimlerini okundu olarak işaretle
 */
export async function markAllNotificationsAsRead(
  userId: string
): Promise<ApiResponse> {
  return await createAction<{ updated: boolean }, null>(
    async (_, supabase) => {
      const { error } = await supabase
        .from("notifications")
        .update({ is_read: true })
        .eq("user_id", userId)
        .eq("is_read", false);

      if (error) {
        throw new Error(
          `Tüm bildirimler okundu olarak işaretlenirken hata: ${error.message}`
        );
      }

      return { updated: true };
    },
    { revalidatePaths: ["/dashboard"] }
  );
}

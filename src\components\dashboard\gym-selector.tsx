"use client";

import { useState, useEffect, useCallback } from "react";
import {
  Building,
  Plus,
  ChevronDown,
  Loader2,
  MapPin,
  Settings,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useRouter, usePathname, useParams } from "next/navigation";
import { Badge } from "@/components/ui/badge";
import { useAuth } from "@/components/auth/auth-provider";
import { createClient } from "@/utils/supabase/client";
import { getManagerGymLimit } from "@/app/actions/subscription-actions";

// Define types
type Gym = {
  id: string;
  name: string;
  slug: string;
  logo_url?: string;
  address?: string;
  city?: string;
};

type SubscriptionTier = "starter" | "professional" | "enterprise";

export function GymSelector() {
  const [gyms, setGyms] = useState<Gym[]>([]);
  const [selectedGym, setSelectedGym] = useState<Gym | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [subscriptionTier, setSubscriptionTier] =
    useState<SubscriptionTier>("starter");
  const [maxGyms, setMaxGyms] = useState<number | null>(1);
  const router = useRouter();
  const pathname = usePathname();
  const params = useParams();
  const currentGymSlug = params?.slug as string | undefined;
  const { authUser } = useAuth();
  const supabase = createClient();

  // Calculate whether user can add more gyms
  const canAddMoreGyms = !maxGyms || gyms.length < maxGyms;

  // Load gyms for the current user
  const fetchGyms = useCallback(async () => {
    console.log("🔄 fetchGyms called with authUser.id:", authUser?.id);
    console.log("🔄 fetchGyms called with currentGymSlug:", currentGymSlug);
    console.log("🔄 fetchGyms called with pathname:", pathname);

    if (!authUser?.id) {
      console.log("❌ No authUser.id, returning early");
      setGyms([]);
      setSelectedGym(null);
      setIsLoading(false);
      return;
    }

    console.log("🚀 Starting fetchGyms process");
    setIsLoading(true);

    try {
      console.log("📞 Calling getManagerGymLimit...");
      // Get manager's gym limit from server action
      const limitResponse = await getManagerGymLimit(authUser.id);
      console.log("📊 Limit response:", limitResponse);

      if (limitResponse.success && limitResponse.data) {
        console.log("✅ Setting tier and maxGyms:", limitResponse.data);
        setSubscriptionTier(limitResponse.data.tier as SubscriptionTier);
        setMaxGyms(limitResponse.data.maxGyms);
      } else {
        console.error("❌ Failed to get gym limit:", limitResponse.error);
        // Fallback to default values
        setSubscriptionTier("starter");
        setMaxGyms(1);
      }

      // Then, fetch gyms managed by this user using the correct manager_user_id
      const { data, error } = await supabase
        .from("gyms")
        .select("id, name, slug, logo_url, address, city")
        .eq("manager_user_id", authUser.id)
        .order("created_at", { ascending: false });

      if (error) throw error;

      setGyms(data || []);

      // Set selected gym based on URL slug parameter
      if (currentGymSlug && data?.some((gym) => gym.slug === currentGymSlug)) {
        setSelectedGym(data.find((gym) => gym.slug === currentGymSlug) || null);
      }
      // If URL doesn't have a valid slug and we have gyms, select the first one
      else if (data && data.length > 0) {
        setSelectedGym(data[0]);
        // Only redirect if we're already on a dashboard page that should have a slug
        if (pathname.includes("/dashboard/manager/gym/") && !currentGymSlug) {
          redirectToGymDashboard(data[0].slug);
        }
      } else {
        setSelectedGym(null);
      }
    } catch (err: any) {
      console.error("Error fetching gyms:", err);
    } finally {
      setIsLoading(false);
    }
  }, [authUser?.id, currentGymSlug, pathname]);

  // Redirect to the gym dashboard page
  const redirectToGymDashboard = useCallback(
    (gymSlug: string) => {
      // Determine the base path for the dashboard
      const basePath = "/dashboard/manager/gym";

      // Get the current pathname to determine the correct route
      if (pathname.includes(basePath)) {
        // If already on a gym dashboard page, use the same page with new slug
        if (pathname.includes("/dashboard/manager/gym/") && currentGymSlug) {
          // Replace current slug with the new one
          const newPath = pathname.replace(`/${currentGymSlug}`, `/${gymSlug}`);
          router.push(newPath);
        } else {
          // Otherwise just go to the main dashboard for that gym
          router.push(`${basePath}/${gymSlug}`);
        }
      } else {
        // If not on a dashboard page, go to the main dashboard
        router.push(`${basePath}/${gymSlug}`);
      }
    },
    [pathname, router, currentGymSlug]
  );

  // Select a different gym
  const selectGym = useCallback(
    (gymSlug: string) => {
      const gym = gyms.find((g) => g.slug === gymSlug);
      if (gym) {
        setSelectedGym(gym);
        redirectToGymDashboard(gymSlug);
      }
    },
    [gyms, redirectToGymDashboard]
  );

  // Load gyms when user changes or role changes
  useEffect(() => {
    console.log("🔄 useEffect triggered with dependencies:", {
      authUserId: authUser?.id,
      currentGymSlug,
      pathname,
    });
    fetchGyms();
  }, [authUser?.id, currentGymSlug, pathname]);

  if (isLoading) {
    return (
      <Button
        variant="outline"
        size="sm"
        disabled
        className="flex items-center gap-1 h-9 px-2 md:px-3"
      >
        <Loader2 className="h-4 w-4 animate-spin mr-1" />
        <span className="hidden md:inline">Yükleniyor...</span>
      </Button>
    );
  }

  if (!selectedGym && gyms.length === 0) {
    return (
      <Button
        variant="outline"
        size="sm"
        className="flex items-center gap-1 h-9 px-2 md:px-3 text-primary"
        onClick={() => router.push("/gym-setup")}
      >
        <Plus className="h-4 w-4 mr-1" />
        <span className="hidden md:inline">Salon Ekle</span>
      </Button>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className="flex items-center gap-1 h-9 px-2 md:px-3"
        >
          <Building className="h-4 w-4 mr-1" />
          <span className="truncate max-w-[90px] md:max-w-[150px]">
            {selectedGym?.name || "Salon Seç"}
          </span>
          <ChevronDown className="h-3.5 w-3.5 ml-0.5 text-muted-foreground" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuLabel>Salon Seçin</DropdownMenuLabel>
        <DropdownMenuSeparator />

        {gyms.length === 0 ? (
          <div className="px-2 py-3 text-sm text-center text-muted-foreground">
            Henüz salon bulunmuyor
          </div>
        ) : (
          gyms.map((gym) => (
            <DropdownMenuItem
              key={gym.id}
              onClick={() => selectGym(gym.slug)}
              className={`flex flex-col items-start cursor-pointer py-2 ${
                selectedGym?.id === gym.id ? "bg-accent" : ""
              }`}
            >
              <div className="flex w-full items-center">
                <Building className="mr-2 h-4 w-4 flex-shrink-0" />
                <span className="truncate font-medium">{gym.name}</span>
                {selectedGym?.id === gym.id && (
                  <span className="ml-auto text-xs text-primary">✓</span>
                )}
              </div>

              {gym.city && (
                <div className="flex items-center pl-6 mt-1 text-xs text-muted-foreground">
                  <MapPin className="h-3 w-3 mr-1" />
                  <span>{gym.city}</span>
                </div>
              )}
            </DropdownMenuItem>
          ))
        )}

        <DropdownMenuSeparator />

        {canAddMoreGyms && (
          <DropdownMenuItem
            onClick={() => router.push("/gym-setup")}
            className="flex cursor-pointer items-center text-primary"
          >
            <Plus className="mr-2 h-4 w-4" />
            <span>Yeni Salon Ekle</span>
          </DropdownMenuItem>
        )}

        <DropdownMenuItem
          onClick={() => router.push("/dashboard/manager/gyms")}
          className="flex cursor-pointer items-center text-muted-foreground hover:text-foreground"
        >
          <Settings className="mr-2 h-4 w-4" />
          <span>Salonları Yönet</span>
        </DropdownMenuItem>

        {maxGyms && (
          <div className="px-2 py-1.5 text-xs text-center text-muted-foreground">
            <Badge variant="outline" className="px-2 py-0">
              {gyms.length} / {maxGyms} salon
            </Badge>
          </div>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

import { getSupabaseAdmin } from '@/utils/supabase/admin';
import fs from 'fs';
import path from 'path';

async function runMigration() {
  try {
    const adminClient = getSupabaseAdmin();
    
    // Read the migration file
    const migrationPath = path.join(process.cwd(), 'migrations', 'add_metadata_to_notifications.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    console.log('Running migration...');
    console.log('SQL:', migrationSQL);
    
    // Execute the migration
    const { data, error } = await adminClient.rpc('exec_sql', {
      sql: migrationSQL
    });
    
    if (error) {
      console.error('Migration failed:', error);
      process.exit(1);
    }
    
    console.log('Migration completed successfully!');
    console.log('Result:', data);
    
  } catch (error) {
    console.error('Error running migration:', error);
    process.exit(1);
  }
}

runMigration();

---
description: 
globs: 
alwaysApply: true
---
# Her Konuşma Öncesi Kontrol Edilecek Kurallar

## Dil Tercihi
- Kullan<PERSON><PERSON><PERSON> ile her zaman Türkçe iletişim kurulmalıdır
- Teknik terimler gerektiğinde İngilizce kullanılabilir
- Açıklamalar ve yönlendirmeler Türkçe olmalıdır

## Veritabanı Değişiklik Kontrolü

Her yeni konuşmada aşağıdaki kontroller yapılmalıdır:

1. Supabase projesinde herhangi bir şema değişikliği olup olmadığı kontrol edilmeli
   - `mcp_supabase_list_tables` ile mevcut tablo yapısı incelenmeli
   - Yeni tablolar, sütunlar veya ilişkiler tespit edilmeli

2. Değişiklik varsa, etkilenen kural dosyaları belirlenmeli:
   - Tablo yapısı değişiklikleri → database-structure.mdc
   - İlişki değişiklikleri → database-relationships.mdc ve database-schema-diagram.mdc
   - Kısıtlama değişiklikleri → database-constraints.mdc
   - Kullanım senaryosu değişiklikleri → database-usage.mdc

3. İlgili kural dosyaları güncellenmeli
4. Değişiklikler hakkında kullanıcı bilgilendirilmeli
5. Yapılan değişikliklerin geriye dönük uyumluluk sorunlarına yol açıp açmayacağı değerlendirilmeli

## Kod Kalitesi Kontrolü

Her kod değişikliği şu prensipler açısından değerlendirilmelidir:

1. **DRY (Kendini Tekrar Etme)**: Kod tekrarından kaçınılmalı
2. **Okunabilirlik**: Kod açık ve anlaşılır olmalı
3. **Tip Güvenliği**: TypeScript tipleri doğru kullanılmalı
4. **Performans**: Optimizasyon fırsatları değerlendirilmeli
5. **Test Edilebilirlik**: Kodun test edilebilir olup olmadığı kontrol edilmeli

## Kural Dosyalarının Güncelliği

- Her konuşma başlangıcında kural dosyalarının güncelliği kontrol edilmeli
- Veritabanında yapılan değişiklikler ile kural dosyaları karşılaştırılmalı
- Tutarsızlık tespit edilirse, ilgili kural dosyaları güncellenmeli
- Önemli değişiklikler kullanıcıya bildirilmeli

## Önemli Hatırlatmalar

1. Her zaman Türkçe iletişim kurulmalı
2. Teknik terimler açıklanarak kullanılmalı
3. Veritabanı değişiklikleri düzenli kontrol edilmeli
4. Kural dosyaları güncel tutulmalı
5. Kullanıcı değişiklikler hakkında bilgilendirilmeli
6. Kod kalitesi standartları her zaman uygulanmalı


"use server"

import { createClient } from "@/utils/supabase/server"
import { revalidatePath } from "next/cache"
import { z } from "zod"
import { ApiResponse, ActionOptions } from "./types"

/**
 * Server Action oluşturucu fonksiyon
 * Try/catch, client oluşturma ve yanıt formatlama işlemlerini soyutlar
 */
export async function createAction<T = any, P = any>(
  handler: (params: P, supabase: any, userId?: string) => Promise<T>,
  options: ActionOptions = {}
): Promise<ApiResponse<T>> {
  try {
    const supabase = await createClient()

    // Kullanıcı kimlik doğrulaması gerekiyorsa
    if (options.requireAuth) {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) {
        return { success: false, error: "Bu işlem için giriş yapmanız gerekiyor." }
      }

      // Handler'a kullanıcı ID'si ile birlikte çağır
      const result = await handler(null as P, supabase, user.id)

      // Yolları yeniden doğrula
      if (options.revalidatePaths) {
        options.revalidatePaths.forEach(path => revalidatePath(path))
      }

      return { success: true, data: result }
    }

    // Normal handler çağrısı
    const result = await handler(null as P, supabase)

    // Yolları yeniden doğrula
    if (options.revalidatePaths) {
      options.revalidatePaths.forEach(path => revalidatePath(path))
    }

    return { success: true, data: result }
  } catch (error: any) {
    console.error(`Server action error:`, error)
    return {
      success: false,
      error: error.message || "İşlem sırasında bir hata oluştu. Lütfen tekrar deneyin."
    }
  }
}

/**
 * Form verilerini belirtilen schema ile doğrulayan yardımcı fonksiyon
 */
export async function validateFormData<T>(
  formData: FormData,
  schema: z.ZodSchema<T>
): Promise<{ data?: T; error?: string }> {
  try {
    // FormData'yı objeye dönüştür
    const rawData: Record<string, any> = {}
    formData.forEach((value, key) => {
      // JSON string olarak gelen array'leri parse et
      if (key === "features" && typeof value === "string") {
        try {
          rawData[key] = JSON.parse(value)
        } catch {
          rawData[key] = []
        }
      } else {
        rawData[key] = value
      }
    })

    // Zod şemasıyla doğrula
    const result = schema.safeParse(rawData)

    if (!result.success) {
      const errors = result.error.format()
      console.error("Validation errors:", errors)
      return { error: "Geçersiz veri formatı. Lütfen tüm alanları kontrol edin." }
    }

    return { data: result.data }
  } catch (error: any) {
    console.error("Form validation error:", error)
    return { error: error.message || "Form verisi doğrulanırken bir hata oluştu." }
  }
}

/**
 * Birden fazla yolu doğrulayan yardımcı fonksiyon
 */
export async function revalidatePaths(paths: string[]) {
  paths.forEach(path => revalidatePath(path))
}
import { Metada<PERSON> } from "next";
import { Suspense } from "react";
import { GymSetupClient } from "@/components/gym-setup/gym-setup-client";
import GymSetupLoading from "./loading";

export const metadata: Metadata = {
  title: "Salon Kurulumu - Sportiva | Spor Salonu Yönetim Sistemi",
  description:
    "Sportiva spor salonu yönetim sistemi salon kurulum sayfası. Salonunuzun bilgilerini girin ve sistemi kullanmaya başlayın.",
  robots: "noindex, nofollow", // Kurulum sayfası indexlenmemeli
};

export default function GymSetupPage() {
  return (
    <Suspense fallback={<GymSetupLoading />}>
      <GymSetupClient />
    </Suspense>
  );
}

"use client";

import { useState, useEffect } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Skeleton } from "@/components/ui/skeleton";
import { useAuth } from "@/components/auth/auth-provider";
import { formatDistanceToNow } from "date-fns";
import { tr } from "date-fns/locale";
import { createClient } from "@/utils/supabase/client";

export function RecentTransactionsTable() {
  const { authUser } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [transactions, setTransactions] = useState<any[]>([]);

  useEffect(() => {
    const fetchTransactions = async () => {
      if (!authUser) return;

      setIsLoading(true);
      const supabase = createClient();

      try {
        // Yöneticinin salonlarını al
        const { data: gyms } = await supabase
          .from("gyms")
          .select("id")
          .eq("manager_user_id", authUser.id);

        if (!gyms || gyms.length === 0) {
          setIsLoading(false);
          return;
        }

        const gymIds = gyms.map((gym: any) => gym.id);

        // Son işlemleri al
        const { data: subscriptions, error } = await supabase
          .from("subscriptions")
          .select(
            `
            id,
            purchase_price,
            start_date,
            membership:memberships(
              id,
              user:users(id, name, surname, email, profile_picture_url)
            ),
            gym_package:gym_packages(id, name, gym_id)
          `
          )
          .in("gym_package.gym_id", gymIds)
          .order("start_date", { ascending: false })
          .limit(10);

        if (error) {
          console.error("Subscription fetch error:", error);
          return;
        }

        if (subscriptions) {
          setTransactions(
            subscriptions.map((sub: any) => ({
              id: sub.id,
              name: `${sub.membership.user.name || ""} ${
                sub.membership.user.surname || ""
              }`,
              email: sub.membership.user.email,
              profile_picture_url: sub.membership.user.profile_picture_url,
              amount: `₺${sub.purchase_price.toLocaleString("tr-TR")}`,
              package: sub.gym_package.name,
              date: sub.start_date,
              status: "successful",
            }))
          );
        }
      } catch (error) {
        console.error("Error fetching transactions:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchTransactions();
  }, [authUser]);

  if (isLoading) {
    return (
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Üye</TableHead>
              <TableHead>Paket</TableHead>
              <TableHead>Tarih</TableHead>
              <TableHead className="text-right">Tutar</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {Array(5)
              .fill(0)
              .map((_, i) => (
                <TableRow key={i}>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <Skeleton className="h-9 w-9 rounded-full" />
                      <div>
                        <Skeleton className="h-4 w-24 mb-1" />
                        <Skeleton className="h-3 w-32" />
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-4 w-20" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-4 w-24" />
                  </TableCell>
                  <TableCell className="text-right">
                    <Skeleton className="h-4 w-16 ml-auto" />
                  </TableCell>
                </TableRow>
              ))}
          </TableBody>
        </Table>
      </div>
    );
  }

  if (!transactions.length) {
    return (
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Üye</TableHead>
              <TableHead>Paket</TableHead>
              <TableHead>Tarih</TableHead>
              <TableHead className="text-right">Tutar</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <TableRow>
              <TableCell colSpan={4} className="h-24 text-center">
                Henüz hiçbir işlem bulunmuyor.
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </div>
    );
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Üye</TableHead>
            <TableHead>Paket</TableHead>
            <TableHead>Tarih</TableHead>
            <TableHead className="text-right">Tutar</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {transactions.map((transaction) => {
            // İsim kısaltması oluşturma
            const nameParts = transaction.name.trim().split(" ");
            const initials =
              nameParts.length > 0
                ? `${nameParts[0].charAt(0)}${
                    nameParts.length > 1
                      ? nameParts[nameParts.length - 1].charAt(0)
                      : ""
                  }`.toUpperCase()
                : "??";

            return (
              <TableRow key={transaction.id}>
                <TableCell>
                  <div className="flex items-center gap-3">
                    <Avatar>
                      <AvatarImage
                        src={transaction.profile_picture_url || ""}
                        alt={transaction.name}
                      />
                      <AvatarFallback>{initials}</AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="font-medium">
                        {transaction.name || "İsimsiz Üye"}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {transaction.email}
                      </div>
                    </div>
                  </div>
                </TableCell>
                <TableCell>{transaction.package}</TableCell>
                <TableCell>
                  {formatDistanceToNow(new Date(transaction.date), {
                    addSuffix: true,
                    locale: tr,
                  })}
                </TableCell>
                <TableCell className="text-right font-medium">
                  {transaction.amount}
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
}

---
description:
globs:
alwaysApply: false
---
# React Uygulama Mimarisi ve Yapısı

React uygulamaları geliştirirken tutarlı bir yapı ve mimari kullan<PERSON>k, kodun bakı<PERSON>n<PERSON>, okunabilirliğini ve genişletilebilirliğini artırır. Bu belge, React projelerinde en iyi mimari yaklaşımları ve dosya yapısı düzenlemelerini açıklar.

## Klasör Yapısı ve Organizasyon

### Önerilen Klasör Yapısı

```
src/
├── assets/             # Resimler, fontlar, statik dosyalar
├── components/         # Yeniden kullanılabilir UI bileşenleri
│   ├── common/         # Tüm uygulama genelinde kullanılan bileşenler
│   ├── layout/         # Sayfa düzeni bileşenleri
│   └── [feature]/      # Özelliğe özgü bileşenler
├── hooks/              # Özel React hook'ları
├── context/            # React context tanımları ve provider'lar
├── pages/              # Sayfa veya ekran bileşenleri
├── services/           # API istekleri, veri manipülasyonu
├── utils/              # Yardımcı fonksiyonlar ve sabitler
├── styles/             # Global stiller ve tema
├── types/              # TypeScript tip tanımları
├── store/              # State yönetimi (Redux, Zustand vb.)
├── config/             # Konfigürasyon dosyaları
├── App.tsx             # Ana uygulama bileşeni
└── main.tsx            # Uygulama giriş noktası
```

### Dosya İsimlendirmesi

Tutarlı bir dosya isimlendirme stratejisi belirleyin:

- **Bileşen dosyaları:** PascalCase (`Button.tsx`, `UserProfile.tsx`)
- **Diğer dosyalar:** camelCase (`useAuth.ts`, `apiService.ts`)
- **Birden fazla bileşen içeren klasör yapısı:**
  ```
  Button/
  ├── Button.tsx
  ├── Button.test.tsx
  ├── Button.module.css
  └── index.ts        # Re-export için
  ```

## Bileşen Mimarisi

### Bileşen Hiyerarşisi

React bileşenleri genellikle bu hiyerarşi içinde düzenlenir:

1. **Sayfa Bileşenleri:** Rotalara karşılık gelen, diğer bileşenleri bir araya getiren temel yapı (`pages/`)
2. **Konteyner Bileşenleri:** Veri ve iş mantığı sağlayan, durum yönetimi yapan bileşenler
3. **UI Bileşenleri:** Daha küçük, yeniden kullanılabilir, mümkün olduğunca stateless bileşenler (`components/`)

### Atom Tasarım Sistemi (İsteğe Bağlı)

Bileşenleri karmaşıklık düzeylerine göre sınıflandırma:

```
components/
├── atoms/          # Butonlar, inputlar gibi en basit UI bileşenleri
├── molecules/      # Formlar, arama kutuları gibi birkaç atomdan oluşan bileşenler 
├── organisms/      # Üst başlık, altbilgi, sidebar gibi daha karmaşık bileşenler
├── templates/      # Sayfa düzenleri
└── pages/          # Gerçek içeriği gösteren sayfa bileşenleri
```

## Kod Düzenleme ve Stil Yaklaşımları

### Bileşen Yapısı

Tutarlı bir bileşen yapısı izleyin:

```jsx
// İmportlar
import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import styles from './Component.module.css';

// Türler (TypeScript)
interface ComponentProps {
  title: string;
  onAction: () => void;
}

// Bileşen
function Component({ title, onAction }: ComponentProps) {
  // State, Effects, ve diğer hook'lar
  const [isOpen, setIsOpen] = useState(false);
  
  useEffect(() => {
    // Yan etkiler...
  }, []);
  
  // Event handlers
  const handleClick = () => {
    setIsOpen(!isOpen);
    onAction();
  };
  
  // Yardımcı render fonksiyonları
  const renderContent = () => {
    return (
      <div className={styles.content}>
        {/* ... */}
      </div>
    );
  };
  
  // Ana render
  return (
    <div className={styles.container}>
      <h2>{title}</h2>
      <button onClick={handleClick}>Tıkla</button>
      {isOpen && renderContent()}
    </div>
  );
}

// PropTypes (JavaScript projelerinde)
Component.propTypes = {
  title: PropTypes.string.isRequired,
  onAction: PropTypes.func.isRequired
};

// Default props
Component.defaultProps = {
  // Opsiyonel prop'lar için varsayılan değerler
};

export default Component;
```

### Stil Yaklaşımları

Birkaç yaygın CSS yaklaşımı:

1. **CSS Modules:** Yerel kapsamlı, bileşene özgü stiller
   ```jsx
   import styles from './Button.module.css';
   <button className={styles.primary}>Tıkla</button>
   ```

2. **Styled Components:** JS içinde CSS yazmanızı sağlayan CSS-in-JS çözümü
   ```jsx
   const StyledButton = styled.button`
     background: ${props => props.primary ? 'blue' : 'white'};
     color: ${props => props.primary ? 'white' : 'black'};
   `;
   <StyledButton primary>Tıkla</StyledButton>
   ```

3. **Tailwind CSS:** Yardımcı sınıflarla stillendirilmiş bileşenler
   ```jsx
   <button className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
     Tıkla
   </button>
   ```

## State Yönetimi

### Yerel State

Basit, bileşene özgü state için `useState` hook'u:

```jsx
const [count, setCount] = useState(0);
```

### Karmaşık/Form State

Daha karmaşık yerel state için `useReducer` hook'u:

```jsx
const [state, dispatch] = useReducer(formReducer, initialFormState);
```

### Uygulama Genelinde State

Büyük uygulamalar için daha geniş kapsamlı state yönetimi:

1. **React Context API:** Orta ölçekli uygulamalar için
   ```jsx
   // ThemeContext.tsx
   export const ThemeContext = createContext({ theme: 'light', toggleTheme: () => {} });
   
   // ThemeProvider.tsx
   export const ThemeProvider = ({ children }) => {
     const [theme, setTheme] = useState('light');
     const toggleTheme = () => setTheme(theme === 'light' ? 'dark' : 'light');
     
     return (
       <ThemeContext.Provider value={{ theme, toggleTheme }}>
         {children}
       </ThemeContext.Provider>
     );
   };
   ```

2. **Redux Toolkit:** Daha büyük uygulamalar için
   ```jsx
   // store/slices/counterSlice.ts
   export const counterSlice = createSlice({
     name: 'counter',
     initialState: { value: 0 },
     reducers: {
       increment: (state) => { state.value += 1 },
       decrement: (state) => { state.value -= 1 }
     }
   });
   ```

3. **Zustand:** Daha basit alternatif
   ```jsx
   // store/useStore.ts
   import create from 'zustand';
   
   export const useStore = create((set) => ({
     count: 0,
     increment: () => set((state) => ({ count: state.count + 1 })),
     decrement: () => set((state) => ({ count: state.count - 1 }))
   }));
   ```

## Veri Alımı ve Yönetimi

### API İstekleri

API çağrıları için özel hook'lar oluşturun:

```jsx
// hooks/useAPI.ts
export function useGetUser(userId) {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  useEffect(() => {
    async function fetchUser() {
      try {
        setLoading(true);
        const response = await fetch(`/api/users/${userId}`);
        const data = await response.json();
        setUser(data);
      } catch (err) {
        setError(err);
      } finally {
        setLoading(false);
      }
    }
    
    fetchUser();
  }, [userId]);
  
  return { user, loading, error };
}
```

### Veri Alma Kütüphaneleri

Daha gelişmiş veri alımı için:

1. **React Query (TanStack Query):**
   ```jsx
   const { data, isLoading, error } = useQuery(['user', userId], 
     () => fetch(`/api/users/${userId}`).then(res => res.json())
   );
   ```

2. **SWR:**
   ```jsx
   const { data, error, isLoading } = useSWR(
     `/api/users/${userId}`, 
     fetcher
   );
   ```

## Route Yönetimi

### React Router (v6)

```jsx
// App.tsx
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import Home from './pages/Home';
import About from './pages/About';
import Layout from './components/Layout';

function App() {
  return (
    <BrowserRouter>
      <Routes>
        <Route path="/" element={<Layout />}>
          <Route index element={<Home />} />
          <Route path="about" element={<About />} />
          <Route path="users/:userId" element={<UserProfile />} />
          <Route path="*" element={<NotFound />} />
        </Route>
      </Routes>
    </BrowserRouter>
  );
}
```

## Form Yönetimi

### Kontrolsüz Formlar

```jsx
function SimpleForm() {
  const formRef = useRef(null);
  
  const handleSubmit = (e) => {
    e.preventDefault();
    const formData = new FormData(formRef.current);
    const name = formData.get('name');
    // Form işleme...
  };
  
  return (
    <form ref={formRef} onSubmit={handleSubmit}>
      <input type="text" name="name" />
      <button type="submit">Gönder</button>
    </form>
  );
}
```

### Kontrollü Formlar

```jsx
function ControlledForm() {
  const [name, setName] = useState('');
  
  const handleSubmit = (e) => {
    e.preventDefault();
    // Form işleme...
  };
  
  return (
    <form onSubmit={handleSubmit}>
      <input 
        type="text" 
        value={name} 
        onChange={(e) => setName(e.target.value)} 
      />
      <button type="submit">Gönder</button>
    </form>
  );
}
```

### Formik veya React Hook Form

```jsx
// React Hook Form
const { register, handleSubmit, errors } = useForm();
const onSubmit = data => console.log(data);

return (
  <form onSubmit={handleSubmit(onSubmit)}>
    <input {...register("name", { required: true })} />
    {errors.name && <span>Bu alan zorunludur</span>}
    <button type="submit">Gönder</button>
  </form>
);
```

## En İyi Pratikler

1. **Bileşen Büyüklüğü:** Bileşenleri tek bir sorumluluğa sahip olacak şekilde küçük tutun
2. **Props Drilling'ten Kaçının:** Çok derinlere prop geçmek yerine Context API veya state yönetim kütüphanesi kullanın
3. **Ayrılık İlkesi:** Business/domain logic'i UI'den ayırın
4. **Tip Güvenliği:** TypeScript kullanarak daha güvenli kod yazın
5. **Test Edilebilirlik:** Bileşenlerinizi ve iş mantığınızı test edilebilir şekilde tasarlayın
6. **Code Splitting:** React.lazy ve Suspense ile kod bölümleme yapın
7. **Performans Optimizasyonu:** React.memo, useCallback, useMemo kullanarak gereksiz render'ları önleyin

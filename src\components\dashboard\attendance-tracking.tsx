"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Calendar } from "lucide-react"
import { useAuth } from "@/components/auth/auth-provider"
import { getAttendanceRecords } from "@/app/actions/attendance-actions"
import { toast } from "sonner"

interface AttendanceTrackingProps {
  gymId: string
}

type AttendanceRecord = {
  id: string;
  name: string;
  email: string;
  lastVisit: string;
  visits: number;
  avatar: string;
}

export function AttendanceTracking({ gymId }: AttendanceTrackingProps) {
  const { session } = useAuth()
  const user = session?.user
  const [isLoading, setIsLoading] = useState(true)
  const [attendanceData, setAttendanceData] = useState<AttendanceRecord[]>([])
  const [searchTerm, setSearchTerm] = useState("")

  useEffect(() => {
    const fetchAttendance = async () => {
      if (!user || !gymId) return

      setIsLoading(true)
      try {
        // Server Action kullanarak katılım verilerini getir
        const response = await getAttendanceRecords(gymId)
        
        if (response.success && response.data) {
          setAttendanceData(response.data.map((record: any) => ({
            id: record.id,
            name: `${record.user?.name || ''} ${record.user?.surname || ''}`.trim() || 'İsimsiz Üye',
            email: record.user?.email || '',
            lastVisit: record.check_in_time ? new Date(record.check_in_time).toLocaleDateString('tr-TR') : '-',
            visits: record.visit_count || 0,
            avatar: record.user?.profile_picture_url || "/placeholder.svg?height=40&width=40",
          })))
        } else {
          toast.error("Katılım verileri getirilemedi")
        }
      } catch (error) {
        console.error("Attendance data fetch error:", error)
        toast.error("Katılım verileri yüklenirken bir hata oluştu")
      } finally {
        setIsLoading(false)
      }
    }

    fetchAttendance()
  }, [user, gymId])

  const filteredAttendance = attendanceData.filter((member) => {
    if (!searchTerm) return true

    return (
      member.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      member.email.toLowerCase().includes(searchTerm.toLowerCase())
    )
  })

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Üye Katılım Takibi</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2">
            <Input
              type="text"
              placeholder="Üye ara..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <Button variant="outline" size="sm">
              <Calendar className="mr-2 h-4 w-4" />
              Tarihe Göre Filtrele
            </Button>
          </div>
          <div className="mt-4 overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Üye</TableHead>
                  <TableHead>Son Ziyaret</TableHead>
                  <TableHead>Toplam Ziyaret</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={3} className="text-center py-4">
                      Yükleniyor...
                    </TableCell>
                  </TableRow>
                ) : filteredAttendance.length > 0 ? (
                  filteredAttendance.map((member) => (
                    <TableRow key={member.id}>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Avatar>
                            <AvatarImage src={member.avatar || "/placeholder.svg"} alt={member.name} />
                            <AvatarFallback>{member.name.substring(0, 2).toUpperCase()}</AvatarFallback>
                          </Avatar>
                          <span>{member.name}</span>
                        </div>
                      </TableCell>
                      <TableCell>{member.lastVisit}</TableCell>
                      <TableCell>{member.visits}</TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={3} className="text-center py-4">
                      Üye bulunamadı.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

import SettingsClient from "@/components/settings/settings-client";
import { notFound, redirect } from "next/navigation";
import { Metadata } from "next";
import { getUserProfile } from "@/app/actions/user-actions";
import { getGymsByManagerId } from "@/app/actions/gym-actions";
import { getMembershipsByUserId } from "@/app/actions/membership-actions";
import { Gyms } from "@/lib/supabase/types";
import { createClient } from "@/utils/supabase/server";
export const metadata: Metadata = {
  title: "Hesap Ayarları | Sportiva",
  description: "Sportiva hesap ayarlarınızı yönetin.",
};

export default async function SettingsPage() {
  const supabase = await createClient();
  const {
    data: { user: authUser },
    error: authError,
  } = await supabase.auth.getUser();

  if (!authUser || authError) {
    redirect("/auth/login");
  }

  // Paralel veri çekme
  const [profileResponse, membershipsResponse] = await Promise.all([
    getUserProfile(authUser.id),
    getMembershipsByUserId(authUser.id),
  ]);

  if (!profileResponse.success || !profileResponse.data) {
    throw new Error(profileResponse.error || "Kullanıcı profili getirilemedi");
  }

  const profile = profileResponse.data;
  const memberships = membershipsResponse.success
    ? membershipsResponse.data
    : [];

  // Kullanıcı yönetici ise salonları getir
  const isManager = profile?.is_manager === true;
  let ownedGyms: Gyms[] = [];

  if (isManager) {
    const gymsResponse = await getGymsByManagerId(authUser.id);
    ownedGyms =
      gymsResponse.success && gymsResponse.data ? gymsResponse.data : [];
  }

  if (!profile) {
    notFound();
  }

  return (
    <SettingsClient
      authUser={authUser}
      initialProfile={profile}
      initialOwnedGyms={ownedGyms}
      initialMemberships={memberships}
      serverError={null}
    />
  );
}

---
description:
globs:
alwaysApply: false
---
# Next.js ve Supabase İleri Düzey Kimlik Doğrulama

Bu kural, Next.js ve Supabase kullanarak ileri düzey kimlik doğrulama özelliklerini nasıl uygulayacağınızı açıklar.

## Sosyal Medya ile Giriş (OAuth)

### Supabase Dashboard Ayarları

1. Supabase projenizin Authentication > Providers bölümünden kullanmak istediğiniz OAuth sağlayıcısını etkinleştirin.
2. Her sağlayıcı için gereken Client ID ve Client Secret bilgilerini ilgili sağlayıcının geliştirici konsolundan alın.
3. Yönlendirme URI'sini (`https://<PROJE_REF>.supabase.co/auth/v1/callback`) sağlayıcının ayarlarında yapılandırın.
4. Authentication > URL Configuration bölümünde Site URL ve yönlendirme URL'lerini ayarlayın.

### OAuth Giriş Düğmeleri

```tsx
// src/components/OAuthButtons.tsx
'use client'

import { createSupabaseBrowserClient } from '@/lib/supabase/client'
import { useState } from 'react'
import { FaGoogle, FaGithub } from 'react-icons/fa' // Örnek ikonlar için

export default function OAuthButtons() {
  const supabase = createSupabaseBrowserClient()
  const [loading, setLoading] = useState<string | null>(null)

  const handleOAuthLogin = async (provider: 'google' | 'github' | 'facebook') => {
    setLoading(provider)
    
    const { error } = await supabase.auth.signInWithOAuth({
      provider,
      options: {
        redirectTo: `${window.location.origin}/auth/callback`,
        // İsteğe bağlı olarak ek izinler isteyebilirsiniz
        // scopes: 'email profile',
      },
    })
    
    if (error) {
      console.error(`Error logging in with ${provider}:`, error.message)
      setLoading(null)
    }
    // Başarılı olursa, kullanıcı OAuth sağlayıcısının sayfasına yönlendirilir.
    // Kimlik doğrulama sonrası /auth/callback rotasına geri döner.
  }

  return (
    <div className="flex flex-col gap-3 mt-4">
      <button 
        onClick={() => handleOAuthLogin('google')} 
        disabled={loading === 'google'}
        className="flex items-center justify-center gap-2 p-2 border rounded"
      >
        <FaGoogle /> {loading === 'google' ? 'İşleniyor...' : 'Google ile Giriş Yap'}
      </button>
      
      <button 
        onClick={() => handleOAuthLogin('github')} 
        disabled={loading === 'github'}
        className="flex items-center justify-center gap-2 p-2 border rounded"
      >
        <FaGithub /> {loading === 'github' ? 'İşleniyor...' : 'GitHub ile Giriş Yap'}
      </button>
    </div>
  )
}
```

Bu bileşeni herhangi bir giriş sayfasına ekleyebilirsiniz:

```tsx
// Giriş sayfanızda
import OAuthButtons from '@/components/OAuthButtons'

export default function LoginPage() {
  return (
    <div>
      <h1>Giriş Yap</h1>
      {/* Normal e-posta/şifre giriş formu */}
      <hr className="my-4" />
      <div className="text-center">veya</div>
      <OAuthButtons />
    </div>
  )
}
```

## Çok Faktörlü Kimlik Doğrulama (MFA/2FA)

Supabase, TOTP (Time-based One-Time Password) tabanlı MFA desteği sunar.

### MFA Etkinleştirme

```tsx
// src/components/MFASetup.tsx
'use client'

import { useState, useEffect } from 'react'
import { createSupabaseBrowserClient } from '@/lib/supabase/client'
import QRCode from 'qrcode.react' // QR kod gösterimi için bir kütüphane (yüklemeniz gerekir)

export default function MFASetup() {
  const supabase = createSupabaseBrowserClient()
  const [factorId, setFactorId] = useState<string | null>(null)
  const [qrCode, setQrCode] = useState<string | null>(null)
  const [secret, setSecret] = useState<string | null>(null)
  const [verificationCode, setVerificationCode] = useState('')
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)
  const [loading, setLoading] = useState(false)

  // MFA kaydı başlat
  const startMFAEnrollment = async () => {
    setLoading(true)
    setError(null)

    try {
      const { data, error } = await supabase.auth.mfa.enroll({
        factorType: 'totp',
      })
      
      if (error) throw error
      
      setFactorId(data.id)
      setQrCode(data.totp.qr_code)
      setSecret(data.totp.secret)
    } catch (err: any) {
      setError(`MFA kaydını başlatma hatası: ${err.message}`)
    } finally {
      setLoading(false)
    }
  }

  // MFA'yı doğrula ve etkinleştir
  const verifyMFA = async () => {
    if (!factorId || !verificationCode) return
    
    setLoading(true)
    setError(null)

    try {
      // Challenge oluştur
      const { error: challengeError } = await supabase.auth.mfa.challenge({ 
        factorId 
      })
      if (challengeError) throw challengeError

      // AAL (Authenticator Assurance Level) bilgisini al
      const aal = supabase.auth.mfa.getAuthenticatorAssuranceLevel()
      
      // Doğrulama kodunu doğrula
      const { error: verifyError } = await supabase.auth.mfa.verify({
        factorId,
        code: verificationCode,
        challengeId: aal.currentChallenge?.id || ''
      })
      
      if (verifyError) throw verifyError
      
      setSuccess(true)
    } catch (err: any) {
      setError(`MFA doğrulama hatası: ${err.message}`)
    } finally {
      setLoading(false)
    }
  }

  if (success) {
    return <div>MFA başarıyla etkinleştirildi! Artık hesabınız iki faktörlü kimlik doğrulama ile korunuyor.</div>
  }

  return (
    <div>
      <h2>İki Faktörlü Kimlik Doğrulama Kurulumu</h2>
      {!factorId ? (
        <button 
          onClick={startMFAEnrollment} 
          disabled={loading}
        >
          {loading ? 'Yükleniyor...' : 'İki Faktörlü Kimlik Doğrulama Ekle'}
        </button>
      ) : (
        <div>
          <p>Aşağıdaki QR kodunu Google Authenticator, Authy veya başka bir TOTP uygulaması ile tarayın:</p>
          
          {qrCode && <QRCode value={qrCode} size={200} />}
          
          {secret && (
            <div>
              <p>QR kodu tarayamıyorsanız, bu kodu manuel olarak girin:</p>
              <code>{secret}</code>
            </div>
          )}
          
          <div className="mt-4">
            <label>
              Doğrulama Kodu:
              <input
                type="text"
                value={verificationCode}
                onChange={(e) => setVerificationCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
                placeholder="6 haneli kod"
                maxLength={6}
                className="p-2 border rounded"
              />
            </label>
            <button
              onClick={verifyMFA}
              disabled={loading || verificationCode.length !== 6}
              className="ml-2 p-2 bg-blue-500 text-white rounded"
            >
              {loading ? 'Doğrulanıyor...' : 'Doğrula ve Etkinleştir'}
            </button>
          </div>
        </div>
      )}
      
      {error && <p className="text-red-500 mt-2">{error}</p>}
    </div>
  )
}
```

### MFA ile Giriş

```tsx
// src/components/MFAChallenge.tsx
'use client'

import { useState } from 'react'
import { createSupabaseBrowserClient } from '@/lib/supabase/client'
import { useRouter } from 'next/navigation'

interface MFAChallengeProps {
  factorId: string
  onComplete?: () => void
}

export default function MFAChallenge({ factorId, onComplete }: MFAChallengeProps) {
  const supabase = createSupabaseBrowserClient()
  const router = useRouter()
  const [verificationCode, setVerificationCode] = useState('')
  const [error, setError] = useState<string | null>(null)
  const [loading, setLoading] = useState(false)

  const handleVerify = async () => {
    if (verificationCode.length !== 6) return
    
    setLoading(true)
    setError(null)

    try {
      // Challenge oluştur
      const { data: challengeData, error: challengeError } = await supabase.auth.mfa.challenge({
        factorId,
      })
      if (challengeError) throw challengeError

      // Kodu doğrula
      const { error: verifyError } = await supabase.auth.mfa.verify({
        factorId,
        challengeId: challengeData.id,
        code: verificationCode,
      })
      
      if (verifyError) throw verifyError
      
      // Başarılı doğrulama sonrası
      if (onComplete) {
        onComplete()
      } else {
        router.push('/dashboard')
        router.refresh()
      }
    } catch (err: any) {
      setError(`Doğrulama hatası: ${err.message}`)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div>
      <h2>İki Faktörlü Doğrulama</h2>
      <p>Kimlik doğrulama uygulamanızdaki 6 haneli kodu girin:</p>
      
      <div>
        <input
          type="text"
          value={verificationCode}
          onChange={(e) => setVerificationCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
          placeholder="6 haneli kod"
          maxLength={6}
          className="p-2 border rounded"
        />
        <button
          onClick={handleVerify}
          disabled={loading || verificationCode.length !== 6}
          className="ml-2 p-2 bg-blue-500 text-white rounded"
        >
          {loading ? 'Doğrulanıyor...' : 'Doğrula'}
        </button>
      </div>
      
      {error && <p className="text-red-500 mt-2">{error}</p>}
    </div>
  )
}
```

Giriş sayfasında MFA kontrolü:

```tsx
'use client'

import { useEffect, useState } from 'react'
import { createSupabaseBrowserClient } from '@/lib/supabase/client'
import LoginForm from '@/components/LoginForm'
import MFAChallenge from '@/components/MFAChallenge'

export default function LoginPage() {
  const supabase = createSupabaseBrowserClient()
  const [mfaFactorId, setMfaFactorId] = useState<string | null>(null)

  useEffect(() => {
    // Auth state değişikliklerini dinle
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      if (event === 'MFA_CHALLENGE_REQUIRED') {
        // MFA gerekiyor, kullanıcı şifresini girmiş ama MFA doğrulaması gerekiyor
        if (session?.user?.factors?.totp?.[0]?.id) {
          setMfaFactorId(session.user.factors.totp[0].id)
        }
      } else if (event === 'SIGNED_IN') {
        // Tam olarak giriş yapıldı
        setMfaFactorId(null)
      }
    })

    return () => {
      subscription.unsubscribe()
    }
  }, [supabase])

  // MFA doğrulaması gerekiyorsa MFAChallenge bileşenini göster
  if (mfaFactorId) {
    return <MFAChallenge factorId={mfaFactorId} />
  }

  // Normal giriş formunu göster
  return <LoginForm />
}
```

## Kullanıcı Rolleri ve İzinler (RLS)

Veritabanı seviyesinde güvenlik için Supabase'in Row Level Security (RLS) özelliğini kullanabilirsiniz.

### Rol Tablosu Oluşturma

```sql
-- users_with_roles tablosu oluşturma
create table public.users_with_roles (
  id uuid references auth.users not null primary key,
  email text not null,
  role text not null check (role in ('admin', 'moderator', 'user')),
  created_at timestamp with time zone not null default now()
);

-- RLS politikaları
alter table public.users_with_roles enable row level security;

-- Kullanıcılar sadece kendi satırlarını görebilir, adminler tümünü görebilir
create policy "Kullanıcılar kendi profil satırlarını görüntüleyebilir"
  on public.users_with_roles for select
  using (
    auth.uid() = id OR
    (select role from public.users_with_roles where id = auth.uid()) = 'admin'
  );

-- Yeni bir kullanıcı kaydolduğunda, onları varsayılan olarak 'user' rolü ile ekle
create function public.handle_new_user() 
returns trigger as $$
begin
  insert into public.users_with_roles (id, email, role)
  values (new.id, new.email, 'user');
  return new;
end;
$$ language plpgsql security definer;

-- Auth tablosundaki kullanıcı değişikliklerini dinle
create trigger on_auth_user_created
  after insert on auth.users
  for each row execute procedure public.handle_new_user();
```

### JWT İle Rol Bilgisini Almak

Supabase Auth, JWT'de kullanıcı rollerini taşımak için kullanılabilir (JWT claims).

1. Edge Function (daha önce Hooks) oluşturma:

```typescript
// /supabase/functions/jwt-claims/index.ts
import { serve } from 'https://deno.land/std@0.131.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.7.1'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // CORS kontrolü
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL')!,
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    )

    const { event, claims, user } = await req.json()

    if (event === 'SIGNIN') {
      // Kullanıcının rolünü veritabanından al
      const { data: userData, error: userError } = await supabase
        .from('users_with_roles')
        .select('role')
        .eq('id', user.id)
        .single()

      if (userError || !userData) {
        throw new Error(`Kullanıcı rolü bulunamadı veya hata oluştu: ${userError?.message}`)
      }

      // JWT'ye rolü ekle
      const newClaims = {
        ...claims,
        'user_role': userData.role
      }

      return new Response(
        JSON.stringify({ claims: newClaims }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        }
      )
    }

    return new Response(
      JSON.stringify({ message: 'Hook tetiklendi ama işlem yapılmadı' }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )
  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      }
    )
  }
})
```

2. JWT claims hook'unu Supabase Dashboard'da Authentication > Hooks bölümünde yapılandırın.

### Kullanıcı Rolünü Kontrol Etme

```tsx
// src/lib/rbac.ts
import { createSupabaseServerClient } from "@/lib/supabase/server"

// Rol kontrolü için yardımcı fonksiyon
export async function hasRole(requiredRole: 'admin' | 'moderator' | 'user') {
  const supabase = createSupabaseServerClient()
  const { data: { user } } = await supabase.auth.getUser()
  
  if (!session) return false
  
  // JWT claim'den rol bilgisini al - bu claims obje yapınıza göre değişebilir
  const userRole = session.user.user_metadata?.role || 'user'
  
  // Rol hiyerarşisi: admin > moderator > user
  if (requiredRole === 'user') {
    return ['admin', 'moderator', 'user'].includes(userRole)
  }
  
  if (requiredRole === 'moderator') {
    return ['admin', 'moderator'].includes(userRole)
  }
  
  if (requiredRole === 'admin') {
    return userRole === 'admin'
  }
  
  return false
}
```

Protected Route Component örneği:

```tsx
// src/components/AdminRoute.tsx
import { hasRole } from "@/lib/rbac"
import { redirect } from "next/navigation"

interface AdminRouteProps {
  children: React.ReactNode
}

export default async function AdminRoute({ children }: AdminRouteProps) {
  const isAdmin = await hasRole('admin')
  
  if (!isAdmin) {
    redirect('/unauthorized')
  }
  
  return <>{children}</>
}

// Kullanımı (layout.tsx veya page.tsx içinde):
// import AdminRoute from '@/components/AdminRoute'
// 
// export default function AdminPage() {
//   return (
//     <AdminRoute>
//       <h1>Admin Sayfası</h1>
//       {/* Sadece adminlerin göreceği içerik */}
//     </AdminRoute>
//   )
// }
```

## Özel RLS Politikaları

Aşağıda, farklı senaryolar için RLS politikası örnekleri verilmiştir:

### Blog Gönderi Tablosu Örneği

```sql
-- Blog gönderi tablosu
create table public.posts (
  id uuid default gen_random_uuid() primary key,
  title text not null,
  content text not null,
  author_id uuid references auth.users not null,
  is_published boolean default false,
  created_at timestamp with time zone default now()
);

-- RLS etkinleştir
alter table public.posts enable row level security;

-- Politika 1: Herkes yayınlanmış gönderileri okuyabilir
create policy "Herkes yayınlanmış gönderileri okuyabilir"
  on public.posts for select
  using (is_published = true);

-- Politika 2: Yazarlar kendi gönderilerini okuyabilir ve düzenleyebilir
create policy "Yazarlar kendi gönderilerini yönetebilir"
  on public.posts for all
  using (auth.uid() = author_id);

-- Politika 3: Sadece adminler her şeyi yapabilir
create policy "Adminler her şeyi yapabilir"
  on public.posts for all
  using ((select role from public.users_with_roles where id = auth.uid()) = 'admin');
```

## JWT Yönetimi

### JWT İçeriğini Görüntüleme

```tsx
'use client'

import { createSupabaseBrowserClient } from "@/lib/supabase/client"
import { useState, useEffect } from "react"

export default function JWTViewer() {
  const supabase = createSupabaseBrowserClient()
  const [session, setSession] = useState<any>(null)
  const [decodedJWT, setDecodedJWT] = useState<any>(null)
  
  useEffect(() => {
    async function getSession() {
      const { data } = await supabase.auth.getSession()
      setSession(data.session)
      
      if (data.session?.access_token) {
        const payload = parseJwt(data.session.access_token)
        setDecodedJWT(payload)
      }
    }
    
    getSession()
  }, [supabase])
  
  // JWT token'ı decode etme yardımcı fonksiyonu
  function parseJwt(token: string) {
    const base64Url = token.split('.')[1]
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/')
    const jsonPayload = decodeURIComponent(atob(base64).split('').map(
      c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2)
    ).join(''))
    return JSON.parse(jsonPayload)
  }
  
  if (!session) {
    return <p>Giriş yapılmadı veya session bulunamadı.</p>
  }
  
  return (
    <div>
      <h2>JWT Payload</h2>
      <pre>{JSON.stringify(decodedJWT, null, 2)}</pre>
      
      <h2>Session Bilgileri</h2>
      <pre>{JSON.stringify(session, null, 2)}</pre>
    </div>
  )
}
```

Bu rehber, Next.js ve Supabase kullanarak gelişmiş kimlik doğrulama özelliklerini nasıl uygulayacağınızı göstermektedir. MFA, sosyal giriş, rol tabanlı erişim kontrolü (RBAC) ve özel JWT talepleri (claims) gibi ileri düzey konular ele alınmıştır.

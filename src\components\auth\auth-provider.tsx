"use client";
import type React from "react";
import {
  createContext,
  useContext,
  useEffect,
  useState,
  useRef,
  useCallback,
} from "react";
import { useRouter, usePathname } from "next/navigation";
import type { User as authUser, Session } from "@supabase/supabase-js";
import type { Tables } from "@/lib/supabase/types";
import { createClient } from "@/utils/supabase/client";
import { toast } from "sonner";
import { useSearchParams } from "next/navigation";
import { z } from "zod";

// Kullanıcı rol türleri
type AuthContextType = {
  authUser: authUser | null;
  profile: Tables<"users"> | null;
  session: Session | null;
  isLoading: boolean;
  isManager: boolean;
  signUp: (formData: {
    email: string;
    password: string;
    name: string;
    surname: string;
  }) => Promise<{ error: any }>;
  signIn: (email: string, password: string) => Promise<{ error: any }>;
  signOut: () => Promise<void>;
  refreshProfile: () => Promise<void>;
  sendPasswordResetEmail: (email: string) => Promise<{ error: any }>;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Form doğrulama şemaları
const emailSchema = z
  .string()
  .email({ message: "Geçerli bir e-posta adresi giriniz" });
const passwordSchema = z
  .string()
  .min(8, { message: "Şifre en az 8 karakter olmalıdır" })
  .regex(/[a-z]/, { message: "Şifrede en az bir küçük harf olmalıdır" })
  .regex(/[A-Z]/, { message: "Şifrede en az bir büyük harf olmalıdır" })
  .regex(/[0-9]/, { message: "Şifrede en az bir rakam olmalıdır" });
const nameSchema = z
  .string()
  .min(2, { message: "İsim en az 2 karakter olmalıdır" });

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [authUser, setAuthUser] = useState<authUser | null>(null);
  const [profile, setProfile] = useState<Tables<"users"> | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isManager, setIsManager] = useState(false);
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const supabase = createClient();
  const isMounted = useRef(true);
  const [isLoadingProfile, setIsLoadingProfile] = useState(true);

  // Handle auth state changes and success messages
  useEffect(() => {
    // Check URL query parameters for success messages
    const checkForSuccessParams = () => {
      const success = searchParams.get("success");
      const type = searchParams.get("type");
      const error = searchParams.get("error");

      if (success === "true") {
        switch (type) {
          case "password_reset":
            toast.success("Şifre başarıyla sıfırlandı", {
              description: "Artık yeni şifrenizle giriş yapabilirsiniz.",
            });
            break;

          case "signin":
            toast.success("Giriş başarılı", {
              description: "Hesabınıza başarıyla giriş yaptınız.",
            });
            break;

          case "signup":
            toast.success("Kayıt başarılı", {
              description: "Hesabınız başarıyla oluşturuldu.",
            });
            break;
        }
      }

      // Hata mesajını göster
      if (error) {
        toast.error("Hata", {
          description: error,
        });
      }
    };

    checkForSuccessParams();

    // Cleanup function to set mounted state to false
    return () => {
      isMounted.current = false;
    };
  }, [pathname, router, searchParams]);

  // Separate useEffect for auth state
  useEffect(() => {
    const getAuthData = async () => {
      try {
        console.log("🔐 AuthProvider: Starting getAuthData");
        setIsLoading(true);

        // Güvenli kullanıcı bilgisini al
        const {
          data: { user },
        } = await supabase.auth.getUser();

        // Session bilgisi için getSession kullan (güvenlik uyarısı için buradan session bilgisini kullanmayacağız)
        const {
          data: { session },
        } = await supabase.auth.getSession();

        if (!isMounted.current) return;

        // Kullanıcı ve oturum durumunu güncelle
        console.log("🔐 AuthProvider: Setting user and session", {
          user: !!user,
          session: !!session,
        });
        setAuthUser(user); // Doğrulanmış kullanıcı (getUser'dan)
        setSession(session);

        // Kullanıcı varsa profil bilgilerini yükle
        if (user) {
          console.log("🔐 AuthProvider: Loading user profile for", user.id);
          await loadUserProfile(user.id);
        } else {
          console.log("🔐 AuthProvider: No user, setting profile to null");
          setProfile(null);
        }
      } catch (error) {
        console.error("Error getting auth data:", error);
      } finally {
        if (isMounted.current) {
          console.log("🔐 AuthProvider: Setting isLoading to false");
          setIsLoading(false);
        }
      }
    };

    getAuthData();

    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (_event, session) => {
      if (!isMounted.current) return;

      // Auth state değiştiğinde getUser() kullanarak güvenli bilgi al
      const {
        data: { user },
      } = await supabase.auth.getUser();

      setAuthUser(user); // Doğrulanmış kullanıcı (getUser'dan)
      setSession(session); // Auth state change event'inden gelen session

      if (user) {
        await loadUserProfile(user.id);
      } else {
        setProfile(null);
      }

      router.refresh();
    });

    return () => {
      subscription.unsubscribe();
    };
  }, [router, supabase]);

  // Oturum açmış kullanıcının profilini yükle
  const loadUserProfile = useCallback(
    async (userId: string) => {
      try {
        setIsLoadingProfile(true);

        // Kullanıcı bilgilerini getir
        const { data: profileData, error: profileError } = await supabase
          .from("users")
          .select(
            "id, email, name, surname, phone, gender, age, profile_picture_url, is_manager"
          )
          .eq("id", userId)
          .single();

        if (profileError) {
          throw profileError;
        }

        if (profileData) {
          setProfile(profileData as unknown as Tables<"users">);
          setIsManager(!!profileData.is_manager);
        }
      } catch (error) {
        console.error("Error loading user profile:", error);
      } finally {
        setIsLoadingProfile(false);
      }
    },
    [supabase]
  );

  const refreshProfile = async () => {
    if (authUser) {
      await loadUserProfile(authUser.id);
    }
  };

  // Kullanıcı kaydı - email/password
  const signUp = async ({
    email,
    password,
    name,
    surname,
  }: {
    email: string;
    password: string;
    name: string;
    surname: string;
  }) => {
    try {
      // Form validasyonu
      try {
        emailSchema.parse(email);
        passwordSchema.parse(password);
        nameSchema.parse(name);
        nameSchema.parse(surname);
      } catch (validationError) {
        if (validationError instanceof z.ZodError) {
          return { error: { message: validationError.errors[0].message } };
        }
        return { error: { message: "Form bilgileri geçersiz" } };
      }

      // Kayıt işlemi - Supabase auth tablosuna kayıt
      const { error: signUpError, data: signUpData } =
        await supabase.auth.signUp({
          email,
          password,
          options: {
            emailRedirectTo: `${window.location.origin}/auth/callback?redirectTo=/dashboard`,
            data: {
              name,
              surname,
            },
          },
        });

      if (signUpError) {
        console.error("Kayıt hatası:", signUpError);
        return { error: signUpError };
      }

      // Kullanıcı profili oluştur
      if (signUpData?.user) {
        // Otomatik olarak profile tablosuna da kaydet
        const { error: profileError } = await supabase.from("users").insert({
          id: signUpData.user.id,
          email,
          name,
          surname,
          created_at: new Date().toISOString(),
        });

        if (profileError) {
          console.error("Profil oluşturma hatası:", profileError);
          return { error: profileError };
        }

        // Başarılı kayıt, otomatik giriş yapma
        setAuthUser(signUpData.user);
        setSession(signUpData.session);
      }

      return { error: null };
    } catch (err) {
      console.error("Signup error:", err);
      return { error: err };
    }
  };

  // Kullanıcı girişi - email/password
  const signIn = async (email: string, password: string) => {
    try {
      // Form validasyonu
      try {
        emailSchema.parse(email);
      } catch (validationError) {
        if (validationError instanceof z.ZodError) {
          return { error: { message: validationError.errors[0].message } };
        }
      }

      if (!password) {
        return { error: { message: "Şifre boş olamaz" } };
      }

      // Giriş işlemi - E-posta doğrulama kontrolü yapılmayacak
      const { error, data } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      // Giriş hatası varsa dön
      if (error) {
        let userMessage = "Giriş yapılamadı";

        // Hata mesajlarını kullanıcı dostu hale getir
        if (error.message.includes("Invalid login credentials")) {
          userMessage = "Geçersiz e-posta veya şifre";
        } else if (error.message.includes("Email not confirmed")) {
          userMessage =
            "E-posta adresiniz henüz doğrulanmamış. Lütfen e-postanızı kontrol edin.";
        }

        return { error: { ...error, message: userMessage } };
      }

      // Başarılı giriş
      if (data.user) {
        // Context'e kullanıcı bilgilerini kaydet
        setAuthUser(data.user);
        setSession(data.session);

        // Kullanıcı profilini kontrol et
        await loadUserProfile(data.user.id);
      }

      return { error: null };
    } catch (err) {
      console.error("Login error:", err);
      return { error: err };
    }
  };

  const signOut = async () => {
    try {
      setIsLoading(true);
      await supabase.auth.signOut();
      setAuthUser(null);
      setProfile(null);
      setSession(null);
      router.push("/login");
    } catch (error) {
      console.error("Error signing out:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const sendPasswordResetEmail = async (email: string) => {
    try {
      // E-posta validasyonu
      try {
        emailSchema.parse(email);
      } catch (validationError) {
        if (validationError instanceof z.ZodError) {
          return { error: { message: validationError.errors[0].message } };
        }
      }

      const { error: resetError } = await supabase.auth.resetPasswordForEmail(
        email,
        {
          redirectTo: `${window.location.origin}/auth/callback?redirectTo=/login?success=true&type=password_reset`,
        }
      );

      if (resetError) {
        return { error: resetError };
      }

      return { error: null };
    } catch (err) {
      return { error: err };
    }
  };

  return (
    <AuthContext.Provider
      value={{
        authUser,
        profile,
        session,
        isLoading,
        isManager,
        signUp,
        signIn,
        signOut,
        refreshProfile,
        sendPasswordResetEmail,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

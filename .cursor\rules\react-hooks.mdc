---
description:
globs:
alwaysApply: false
---
# React Hooks Kullanım Rehberi

React Hook'ları, fonksiyonel bileşenlerde state ve yaşam döngüsü özellikleri gibi React özelliklerini kullanmanızı sağlayan özel fonksiyonlardır.

## `useState` Hook'u

State yönetimi için kullan<PERSON>l<PERSON>.

```jsx
import { useState } from 'react';

function Counter() {
  const [count, setCount] = useState(0); // [mevcut değer, güncelleme fonksiyonu]
  
  return (
    <div>
      <p>Sayı: {count}</p>
      <button onClick={() => setCount(count + 1)}>Artır</button>
      <button onClick={() => setCount(prevCount => prevCount - 1)}>Azalt</button>
    </div>
  );
}
```

- Fonksiyonel güncelleme (önceki değere dayalı): `setCount(prevCount => prevCount + 1)`
- Nesne state'leri <PERSON><PERSON><PERSON>: `setUser({...user, name: '<PERSON><PERSON>'})`

## `useEffect` Hook'u

Yan etkileri (API çağrıları, abonelikler, DOM manipülasyonları) yönetmek için kullanılır.

```jsx
import { useState, useEffect } from 'react';

function DataFetcher() {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    // Yan etki: Veri çekme
    fetch('https://api.example.com/data')
      .then(response => response.json())
      .then(result => {
        setData(result);
        setLoading(false);
      });
      
    // İsteğe bağlı temizlik fonksiyonu
    return () => {
      // Bileşen unmount edildiğinde çalışır
      console.log('Temizlik yapılıyor...');
    };
  }, []); // Boş dizi: Sadece ilk render'da çalışır
  
  // ...
}
```

- `useEffect(callback, dependencies)`: Dependencies değiştiğinde callback çalışır
- `[]`: Sadece ilk mount'ta çalışır
- `[prop, state]`: Belirtilen prop veya state değiştiğinde çalışır
- Bağımlılık dizisi olmadan: Her render sonrası çalışır

## `useContext` Hook'u

Context API ile birlikte kullanılarak, props drilling olmadan bileşenler arası veri paylaşımı sağlar.

```jsx
import { createContext, useContext } from 'react';

// Context oluştur
const ThemeContext = createContext('light');

// Provider ile değer sağla
function App() {
  return (
    <ThemeContext.Provider value="dark">
      <Toolbar />
    </ThemeContext.Provider>
  );
}

// useContext ile değere eriş
function Toolbar() {
  return <ThemedButton />;
}

function ThemedButton() {
  const theme = useContext(ThemeContext); // "dark" değerini alır
  
  return <button className={theme}>Temalı Buton</button>;
}
```

## `useReducer` Hook'u

Karmaşık state mantığı için `useState` alternatifidir. Redux benzeri bir yaklaşım sunar.

```jsx
import { useReducer } from 'react';

// Reducer fonksiyonu
function counterReducer(state, action) {
  switch (action.type) {
    case 'increment':
      return { count: state.count + 1 };
    case 'decrement':
      return { count: state.count - 1 };
    case 'reset':
      return { count: 0 };
    default:
      throw new Error('Bilinmeyen action tipi');
  }
}

function Counter() {
  const [state, dispatch] = useReducer(counterReducer, { count: 0 });
  
  return (
    <div>
      <p>Sayı: {state.count}</p>
      <button onClick={() => dispatch({ type: 'increment' })}>Artır</button>
      <button onClick={() => dispatch({ type: 'decrement' })}>Azalt</button>
      <button onClick={() => dispatch({ type: 'reset' })}>Sıfırla</button>
    </div>
  );
}
```

## `useCallback` ve `useMemo` Hook'ları

Performans optimizasyonu için kullanılır.

```jsx
import { useState, useCallback, useMemo } from 'react';

function ExpensiveComponent() {
  const [count, setCount] = useState(0);
  const [text, setText] = useState('');

  // count değiştiğinde yeniden oluşturulur, text değiştiğinde değil
  const expensiveFunction = useCallback(() => {
    console.log('Count ile hesaplama yapılıyor:', count);
    return count * 2;
  }, [count]); // Sadece count değiştiğinde yeniden oluşur
  
  // Maliyetli hesaplama - sadece count değiştiğinde yeniden hesaplanır
  const expensiveValue = useMemo(() => {
    console.log('Maliyetli hesaplama yapılıyor...');
    let result = 0;
    for (let i = 0; i < count * 1000; i++) {
      result += Math.random();
    }
    return result;
  }, [count]);
  
  return (
    <div>
      <p>Count: {count}</p>
      <p>Hesaplanan Değer: {expensiveValue}</p>
      <button onClick={() => setCount(count + 1)}>Artır</button>
      <input value={text} onChange={(e) => setText(e.target.value)} />
    </div>
  );
}
```

- `useCallback`: Fonksiyon referansını ezberleme (memoize)
- `useMemo`: Hesaplama sonuçlarını ezberleme

## `useRef` Hook'u

DOM elemanlarına erişmek veya render tetiklemeyen değişkenleri tutmak için kullanılır.

```jsx
import { useRef, useEffect } from 'react';

function FocusInput() {
  const inputRef = useRef(null);
  
  useEffect(() => {
    // DOM elemanına erişim
    inputRef.current.focus();
  }, []);
  
  return <input ref={inputRef} type="text" />;
}

// Render tetiklemeyen değişken için
function Timer() {
  const [count, setCount] = useState(0);
  const timerIdRef = useRef(null);
  
  const startTimer = () => {
    timerIdRef.current = setInterval(() => {
      setCount(c => c + 1);
    }, 1000);
  };
  
  const stopTimer = () => {
    clearInterval(timerIdRef.current);
    timerIdRef.current = null;
  };
  
  // ...
}
```

## Hook Kuralları

1. **Sadece En Üst Düzeyde Çağrın:** Hook'ları döngüler, koşullar veya iç fonksiyonlarda çağırmayın
2. **Sadece React Fonksiyonel Bileşenlerinde Çağrın:** Hook'ları ya React fonksiyonel bileşenleri içinde ya da özel Hook'lar içinde çağırın
3. **İsimler Daima "use" ile Başlamalıdır:** Özel Hook'lar tanımlarken, isim "use" ile başlamalıdır (örn: useWindowSize)

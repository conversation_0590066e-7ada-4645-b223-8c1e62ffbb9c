"use server"

import { createAction, validateFormData } from "@/lib/actions/core"
import { ApiResponse } from "@/lib/actions/types"
import { z } from "zod"
import { revalidatePath } from "next/cache"
import { createClient } from "@/lib/supabase/server"

// Validasyon şemaları
const checkInSchema = z.object({
  userId: z.string().uuid("Geçerli bir kullanıcı ID'si gereklidir"),
  gymId: z.string().uuid("Geçerli bir salon ID'si gereklidir"),
})

const checkOutSchema = z.object({
  attendanceId: z.string().uuid("Geçerli bir yoklama ID'si gereklidir"),
})

/**
 * Kullanıcı check-in işlemi yapar
 */
export async function checkIn(formData: FormData): Promise<ApiResponse> {
  const { data, error } = await validateFormData(formData, checkInSchema)
  
  if (error || !data) {
    return { success: false, error: error || "Geçersiz form verisi" }
  }
  
  const { userId, gymId } = data
  
  return await createAction(async (_, supabase) => {
    // Kullanıcının zaten aktif bir check-in'i var mı kontrol et
    const { data: activeAttendance } = await supabase
      .from("attendances")
      .select("*")
      .eq("user_id", userId)
      .eq("gym_id", gymId)
      .is("check_out_time", null)
      .maybeSingle()

    if (activeAttendance) {
      throw new Error("Zaten check-in yapmış durumdasınız")
    }

    // Yeni check-in kaydı oluştur
    const { data: attendance, error } = await supabase
      .from("attendances")
      .insert({
        user_id: userId,
        gym_id: gymId,
        check_in_time: new Date().toISOString(),
      })
      .select()
      .single()

    if (error) {
      throw new Error(`Check-in yapılırken hata: ${error.message}`)
    }

    return attendance
  }, { revalidatePaths: ["/dashboard/member"] })
}

/**
 * Kullanıcı check-out işlemi yapar
 */
export async function checkOut(formData: FormData): Promise<ApiResponse> {
  const { data, error } = await validateFormData(formData, checkOutSchema)
  
  if (error || !data) {
    return { success: false, error: error || "Geçersiz form verisi" }
  }
  
  const { attendanceId } = data
  
  return await createAction(async (_, supabase) => {
    // Check-in kaydını bul ve güncelle
    const { data: attendance, error } = await supabase
      .from("attendances")
      .update({
        check_out_time: new Date().toISOString(),
      })
      .eq("id", attendanceId)
      .select()
      .single()

    if (error) {
      throw new Error(`Check-out yapılırken hata: ${error.message}`)
    }

    return attendance
  }, { revalidatePaths: ["/dashboard/member"] })
}

/**
 * Yönetici tarafından manuel check-in
 */
export async function manualCheckIn(formData: FormData): Promise<ApiResponse> {
  const { data, error } = await validateFormData(formData, checkInSchema)
  
  if (error || !data) {
    return { success: false, error: error || "Geçersiz form verisi" }
  }
  
  const { userId, gymId } = data
  
  return await createAction(async (_, supabase) => {
    // Kullanıcının zaten aktif bir check-in'i var mı kontrol et
    const { data: activeAttendance } = await supabase
      .from("attendances")
      .select("*")
      .eq("user_id", userId)
      .eq("gym_id", gymId)
      .is("check_out_time", null)
      .maybeSingle()

    if (activeAttendance) {
      throw new Error("Bu üye zaten check-in yapmış durumda")
    }

    // Yeni check-in kaydı oluştur
    const { data: attendance, error } = await supabase
      .from("attendances")
      .insert({
        user_id: userId,
        gym_id: gymId,
        check_in_time: new Date().toISOString(),
        is_manual: true,
      })
      .select()
      .single()

    if (error) {
      throw new Error(`Manuel check-in yapılırken hata: ${error.message}`)
    }

    return attendance
  }, { revalidatePaths: ["/dashboard/manager/attendance"] })
}

/**
 * Yönetici tarafından manuel check-out
 */
export async function manualCheckOut(formData: FormData): Promise<ApiResponse> {
  const { data, error } = await validateFormData(formData, checkOutSchema)
  
  if (error || !data) {
    return { success: false, error: error || "Geçersiz form verisi" }
  }
  
  const { attendanceId } = data
  
  return await createAction(async (_, supabase) => {
    // Check-in kaydını bul ve güncelle
    const { data: attendance, error } = await supabase
      .from("attendances")
      .update({
        check_out_time: new Date().toISOString(),
        is_manual: true,
      })
      .eq("id", attendanceId)
      .select()
      .single()

    if (error) {
      throw new Error(`Manuel check-out yapılırken hata: ${error.message}`)
    }

    return attendance
  }, { revalidatePaths: ["/dashboard/manager/attendance"] })
}

/**
 * Belirli bir salonun katılım kayıtlarını getirir
 * @param gymId Salon ID'si
 * @returns Katılım kayıtları veya hata
 */
export async function getAttendanceRecords(gymId: string): Promise<ApiResponse<any[]>> {
  return await createAction(async (_, supabase, authUserId) => {
    // İlk olarak kullanıcının bu salonu yönetme yetkisi var mı kontrol et
    const { data: gym, error: gymError } = await supabase
      .from("gyms")
      .select("manager_id")
      .eq("id", gymId)
      .single();

    if (gymError) {
      throw new Error(`Salon bulunamadı: ${gymError.message}`);
    }

    if (gym.manager_id !== authUserId) {
      throw new Error("Bu salona ait katılım kayıtlarını görüntüleme yetkiniz bulunmuyor");
    }

    // Katılım kayıtlarını ve kullanıcı bilgilerini getir
    const { data: records, error: recordsError } = await supabase
      .from("attendance_records")
      .select(`
        *,
        user:user_id(
          id,
          name,
          surname,
          email,
          profile_picture_url
        )
      `)
      .eq("gym_id", gymId)
      .order("check_in_time", { ascending: false });

    if (recordsError) {
      throw new Error(`Katılım kayıtları getirilirken hata: ${recordsError.message}`);
    }

    // Kullanıcı bazında gruplayarak ziyaret sayılarını hesapla
    const userVisits = records.reduce((acc: any, record: any) => {
      const userId = record.user_id;
      if (!acc[userId]) {
        acc[userId] = {
          ...record,
          visit_count: 1
        };
      } else {
        acc[userId].visit_count += 1;
      }
      return acc;
    }, {});

    // Son ziyarete göre sıralayarak sonuçları döndür
    const attendanceData = Object.values(userVisits);
    return attendanceData;
  }, { requireAuth: true });
}

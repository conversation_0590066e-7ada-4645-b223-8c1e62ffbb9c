"use server"

import { createAction, validateFormData } from "@/lib/actions/core"
import { ApiResponse } from "@/lib/actions/types"
import { createNotification } from "@/lib/actions/notifications"
import { z } from "zod"

// Validasyon şemaları
const sendMessageSchema = z.object({
  senderId: z.string().uuid("Geçerli bir gönderici ID'si gereklidir"),
  recipientId: z.string().uuid("Geçerli bir alıcı ID'si gereklidir"),
  content: z.string().min(1, "Mesaj içeriği gereklidir"),
})

/**
 * Yeni mesaj gönderir
 */
export async function sendMessage(formData: FormData): Promise<ApiResponse> {
  const { data, error } = await validateFormData(formData, sendMessageSchema)
  
  if (error || !data) {
    return { success: false, error: error || "Geçersiz form verisi" }
  }
  
  const { senderId, recipientId, content } = data
  
  return await createAction(async (_, supabase) => {
    // Mesajı veritabanına ekle
    const { data: message, error } = await supabase
      .from("messages")
      .insert({
        sender_id: senderId,
        recipient_id: recipientId,
        content,
        is_read: false,
        created_at: new Date().toISOString(),
      })
      .select()
      .single()

    if (error) {
      throw new Error(`Mesaj gönderilirken hata: ${error.message}`)
    }
    
    // Alıcıya bildirim gönder
    await createNotification({
      userId: recipientId,
      title: "Yeni Mesaj",
      message: "Size yeni bir mesaj gönderildi",
      type: "message_received",
      relatedEntityType: "messages",
      relatedEntityId: message.id
    })

    return message
  }, { revalidatePaths: [`/dashboard/messages/${recipientId}`] })
}

/**
 * Mesajları okundu olarak işaretler
 */
export async function markAsRead(senderId: string, recipientId: string): Promise<ApiResponse> {
  if (!senderId || !recipientId) {
    return { success: false, error: "Gerekli parametreler eksik" }
  }
  
  return await createAction(async (_, supabase) => {
    // Belirli gönderici ile alıcı arasındaki tüm okunmamış mesajları güncelle
    const { error } = await supabase
      .from("messages")
      .update({ is_read: true })
      .eq("sender_id", senderId)
      .eq("recipient_id", recipientId)
      .eq("is_read", false)
    
    if (error) {
      throw new Error(`Mesajlar okundu olarak işaretlenirken hata: ${error.message}`)
    }

    return { updated: true }
  }, { revalidatePaths: ["/dashboard/messages", `/dashboard/messages/${senderId}`] })
}

import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  /* config options here */
  images: {
    domains: ["rpyfhrvnhwzeopdygprw.supabase.co"],
  },
  async redirects() {
    return [
      // Legacy auth URLs redirects
      {
        source: "/auth",
        destination: "/login",
        permanent: true,
      },
      {
        source: "/auth/:path*",
        destination: "/login",
        permanent: true,
      },
    ];
  },
};

export default nextConfig;

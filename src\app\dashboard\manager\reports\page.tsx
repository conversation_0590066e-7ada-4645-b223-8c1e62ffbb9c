"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/components/auth/auth-provider";
import {
  getManagerReports,
  ReportData,
  ReportFilters,
} from "@/app/actions/manager-actions";
import Link from "next/link";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  Users,
  DollarSign,
  Building,
  Calendar,
  Download,
  Filter,
  Loader2,
  AlertTriangle,
} from "lucide-react";
import { cn } from "@/lib/utils";

// ReportData interface artık manager-actions.ts'den import ediliyor

export default function ReportsPage() {
  const { authUser } = useAuth();
  const [reportData, setReportData] = useState<ReportData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedPeriod, setSelectedPeriod] = useState<
    "month" | "quarter" | "year"
  >("month");

  useEffect(() => {
    const loadReportData = async () => {
      if (!authUser) {
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        const filters: ReportFilters = {
          period: selectedPeriod,
        };

        const result = await getManagerReports(filters);

        if (result.success && result.data) {
          setReportData(result.data);
        } else {
          setError(
            result.error || "Rapor verileri yüklenirken bir hata oluştu."
          );
        }
      } catch (err) {
        console.error("Error loading report data:", err);
        setError("Rapor verileri yüklenirken bir hata oluştu.");
      } finally {
        setIsLoading(false);
      }
    };

    loadReportData();
  }, [authUser, selectedPeriod]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("tr-TR", {
      style: "currency",
      currency: "TRY",
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value > 0 ? "+" : ""}${value.toFixed(1)}%`;
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="flex items-center gap-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Rapor verileri yükleniyor...</span>
        </div>
      </div>
    );
  }

  if (!authUser) {
    return (
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          Raporları görüntülemek için giriş yapmanız gerekiyor.
        </AlertDescription>
      </Alert>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Raporlar</h1>
          <p className="text-muted-foreground">
            Tüm salonlarınızın performans analizi ve istatistikleri
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" className="gap-2">
            <Filter className="h-4 w-4" />
            Filtrele
          </Button>
          <Button className="gap-2">
            <Download className="h-4 w-4" />
            Rapor İndir
          </Button>
        </div>
      </div>

      {/* Period Selection */}
      <div className="flex gap-2">
        {[
          { key: "month", label: "Bu Ay" },
          { key: "quarter", label: "Bu Çeyrek" },
          { key: "year", label: "Bu Yıl" },
        ].map((period) => (
          <Button
            key={period.key}
            variant={selectedPeriod === period.key ? "default" : "outline"}
            size="sm"
            onClick={() => setSelectedPeriod(period.key as any)}
          >
            {period.label}
          </Button>
        ))}
      </div>

      {reportData && (
        <>
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">
                  Toplam Gelir
                </CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatCurrency(reportData.totalRevenue)}
                </div>
                <div className="flex items-center gap-1 text-xs">
                  {reportData.revenueGrowth > 0 ? (
                    <TrendingUp className="h-3 w-3 text-green-500" />
                  ) : (
                    <TrendingDown className="h-3 w-3 text-red-500" />
                  )}
                  <span
                    className={cn(
                      reportData.revenueGrowth > 0
                        ? "text-green-500"
                        : "text-red-500"
                    )}
                  >
                    {formatPercentage(reportData.revenueGrowth)}
                  </span>
                  <span className="text-muted-foreground">
                    önceki döneme göre
                  </span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">
                  Toplam Üye
                </CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {reportData.totalMembers}
                </div>
                <div className="flex items-center gap-1 text-xs">
                  {reportData.memberGrowth > 0 ? (
                    <TrendingUp className="h-3 w-3 text-green-500" />
                  ) : (
                    <TrendingDown className="h-3 w-3 text-red-500" />
                  )}
                  <span
                    className={cn(
                      reportData.memberGrowth > 0
                        ? "text-green-500"
                        : "text-red-500"
                    )}
                  >
                    {formatPercentage(reportData.memberGrowth)}
                  </span>
                  <span className="text-muted-foreground">
                    önceki döneme göre
                  </span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">
                  Aktif Salon
                </CardTitle>
                <Building className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{reportData.totalGyms}</div>
                <p className="text-xs text-muted-foreground">
                  Tüm salonlar aktif
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">
                  Aylık Büyüme
                </CardTitle>
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatPercentage(reportData.monthlyGrowth)}
                </div>
                <p className="text-xs text-muted-foreground">
                  Genel performans artışı
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Top Performing Gyms */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building className="h-5 w-5" />
                En Performanslı Salonlar
              </CardTitle>
              <CardDescription>
                Gelir ve üye sayısına göre sıralanmış salonlarınız
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {reportData.topPerformingGyms.map((gym, index) => (
                  <Link
                    key={gym.id}
                    href={`/dashboard/manager/gym/${gym.slug}`}
                    className="block"
                  >
                    <div className="flex items-center justify-between p-4 rounded-lg border hover:bg-muted/50 transition-colors cursor-pointer">
                      <div className="flex items-center gap-3">
                        <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                          <span className="text-sm font-semibold">
                            #{index + 1}
                          </span>
                        </div>
                        <div>
                          <p className="font-medium">{gym.name}</p>
                          <p className="text-sm text-muted-foreground">
                            {gym.members} üye
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold">
                          {formatCurrency(gym.revenue)}
                        </p>
                        <div className="flex items-center gap-1">
                          {gym.growth > 0 ? (
                            <TrendingUp className="h-3 w-3 text-green-500" />
                          ) : (
                            <TrendingDown className="h-3 w-3 text-red-500" />
                          )}
                          <span
                            className={cn(
                              "text-sm",
                              gym.growth > 0 ? "text-green-500" : "text-red-500"
                            )}
                          >
                            {formatPercentage(gym.growth)}
                          </span>
                        </div>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
}

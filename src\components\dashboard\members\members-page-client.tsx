"use client";

import { useState } from "react";
import { Tables, Memberships, Users } from "@/lib/supabase/types";

import { MemberDialogs } from "./member-dialogs";
import { MemberStats } from "./member-stats";
import { MemberFilters } from "./member-filters";
import { MemberTable } from "./member-table";

export type MemberWithDetails = {
  membership: Memberships;
  user: Users;
};

interface MembersPageClientProps {
  gym: Tables<"gyms">;
  initialMembers: MemberWithDetails[];
}

export function MembersPageClient({
  gym,
  initialMembers,
}: MembersPageClientProps) {
  const [members, setMembers] = useState<MemberWithDetails[]>(initialMembers);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedTab, setSelectedTab] = useState("all");
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Filter members based on search and tab
  const filteredMembers = members.filter((member) => {
    // Tab filter
    if (selectedTab !== "all" && member.membership.status !== selectedTab) {
      return false;
    }

    // Search filter
    if (searchTerm) {
      const fullName = `${member.user?.name || ""} ${
        member.user?.surname || ""
      }`.toLowerCase();
      const email = member.user?.email?.toLowerCase() || "";
      const search = searchTerm.toLowerCase();

      return fullName.includes(search) || email.includes(search);
    }

    return true;
  });

  // Group members by status
  const membersByStatus = {
    all: members,
    active: members.filter((m) => m.membership.status === "active"),
    passive: members.filter((m) => m.membership.status === "passive"),
  };

  const refreshMembers = async () => {
    setIsRefreshing(true);
    try {
      // Re-fetch members data
      const { getMembershipsByGymId } = await import(
        "@/app/actions/membership-actions"
      );
      const response = await getMembershipsByGymId(gym.id);

      if (response.success && response.data) {
        setMembers(response.data);
      }
    } catch (error) {
      console.error("Error refreshing members:", error);
    } finally {
      setIsRefreshing(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Üye Yönetimi</h1>
          <p className="text-muted-foreground">
            {gym.name} salonunuzun üyelerini yönetin
          </p>
        </div>
      </div>

      {/* Stats */}
      <MemberStats membersByStatus={membersByStatus} />

      {/* Filters */}
      <MemberFilters
        searchTerm={searchTerm}
        onSearchChange={setSearchTerm}
        selectedTab={selectedTab}
        onTabChange={setSelectedTab}
        membersByStatus={membersByStatus}
        onAddMember={() => setIsAddDialogOpen(true)}
      />

      {/* Table */}
      <MemberTable
        members={filteredMembers}
        isLoading={isRefreshing}
        onRefresh={refreshMembers}
      />

      {/* Dialogs */}
      <MemberDialogs
        gymId={gym.id}
        isAddDialogOpen={isAddDialogOpen}
        onAddDialogChange={setIsAddDialogOpen}
        onMembersUpdate={refreshMembers}
      />
    </div>
  );
}

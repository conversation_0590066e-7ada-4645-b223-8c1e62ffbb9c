'use client';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  Card<PERSON>itle,
  CardFooter,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Shield } from "lucide-react";
import Link from "next/link";

interface ErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export default function SettingsError({
  error,
  reset,
}: ErrorProps) {
  return (
    <div className="container py-12 mx-auto max-w-3xl text-center">
      <Card className="border-destructive/20 shadow-md overflow-hidden">
        <div className="bg-destructive/10 p-2"></div>
        <CardHeader>
          <div className="flex items-center gap-2 justify-center">
            <Shield className="h-6 w-6 text-destructive" />
            <CardTitle className="text-2xl">Bir Hata Oluştu</CardTitle>
          </div>
          <CardDescription className="mt-2">
            <PERSON><PERSON><PERSON> say<PERSON><PERSON><PERSON> yükle<PERSON> bir sorun<PERSON> ka<PERSON>şılaşıldı.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="bg-muted/50 rounded-lg p-4 border text-sm text-destructive">
            <p>{error.message || 'Beklenmeyen bir hata oluştu.'}</p>
            <p className="mt-2 text-xs">Lütfen daha sonra tekrar deneyin veya destek ile iletişime geçin.</p>
          </div>
        </CardContent>
        <CardFooter className="bg-muted/30 flex justify-center gap-2 border-t pt-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/dashboard">Dashboard'a Dön</Link>
          </Button>
          <Button variant="default" size="sm" onClick={reset}>
            Tekrar Dene
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
} 
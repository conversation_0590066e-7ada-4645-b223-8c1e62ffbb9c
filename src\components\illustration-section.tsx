"use client"

import { useEffect, useState } from "react"
import { motion } from "framer-motion"

export function IllustrationSection() {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) return null

  return (
    <div className="relative hidden bg-gradient-to-br from-violet-500 to-purple-700 lg:block lg:w-1/2">
      <div className="absolute inset-0 bg-[url('/placeholder.svg?height=800&width=800')] bg-cover bg-center opacity-10 mix-blend-overlay"></div>
      <div className="flex h-full flex-col items-center justify-center px-8 py-12 text-white">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="mb-8 text-center"
        >
          <h2 className="mb-4 text-4xl font-bold">Welcome to Our Platform</h2>
          <p className="text-lg opacity-90">Join thousands of users and start your journey today</p>
        </motion.div>

        <div className="relative mx-auto w-full max-w-md">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="overflow-hidden rounded-lg bg-white/10 p-1 backdrop-blur-sm"
          >
            <div className="rounded-md bg-gradient-to-br from-white/5 to-white/20 p-6 shadow-xl">
              <div className="mb-4 h-12 w-12 rounded-full bg-purple-600 p-2">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  className="h-full w-full"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 11c0 3.517-1.009 6.799-2.753 9.571m-3.44-2.04l.054-.09A13.916 13.916 0 008 11a4 4 0 118 0c0 1.017-.07 2.019-.203 3m-2.118 6.844A21.88 21.88 0 0015.171 17m3.839 1.132c.645-2.266.99-4.659.99-7.132A8 8 0 008 4.07M3 15.364c.64-1.319 1-2.8 1-4.364 0-1.457.39-2.823 1.07-4"
                  />
                </svg>
              </div>
              <h3 className="mb-2 text-xl font-semibold">Secure Authentication</h3>
              <p className="text-sm opacity-80">
                Our platform uses the latest security measures to keep your account safe
              </p>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="mt-4 overflow-hidden rounded-lg bg-white/10 p-1 backdrop-blur-sm"
          >
            <div className="rounded-md bg-gradient-to-br from-white/5 to-white/20 p-6 shadow-xl">
              <div className="mb-4 h-12 w-12 rounded-full bg-purple-600 p-2">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  className="h-full w-full"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="mb-2 text-xl font-semibold">Lightning Fast</h3>
              <p className="text-sm opacity-80">Experience blazing fast performance with our optimized platform</p>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.8 }}
            className="mt-4 overflow-hidden rounded-lg bg-white/10 p-1 backdrop-blur-sm"
          >
            <div className="rounded-md bg-gradient-to-br from-white/5 to-white/20 p-6 shadow-xl">
              <div className="mb-4 h-12 w-12 rounded-full bg-purple-600 p-2">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  className="h-full w-full"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                  />
                </svg>
              </div>
              <h3 className="mb-2 text-xl font-semibold">Privacy First</h3>
              <p className="text-sm opacity-80">Your data is yours. We prioritize privacy and data protection</p>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  )
}

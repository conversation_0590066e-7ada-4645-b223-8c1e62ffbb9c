"use server";

import { createAction } from "@/lib/actions/core";
import { ApiResponse } from "@/lib/actions/types";
import type { Tables } from "@/lib/supabase/types";

export type GymStats = {
  activeMembers: number;
  totalPackages: number;
  totalRevenue: number;
};

/**
 * Salon istatistiklerini getirir
 */
export async function getGymStats(
  gymId: string
): Promise<ApiResponse<GymStats>> {
  if (!gymId) {
    return { success: false, error: "Salon ID'si gereklidir." };
  }

  return await createAction(
    async (_, supabase, userId) => {
      if (!userId) {
        throw new Error("Bu işlem için giriş yapmanız gerekiyor.");
      }

      // Kullanıcının manager kaydını bulalım
      const { data: managerData, error: managerError } = await supabase
        .from("managers")
        .select("user_id")
        .eq("user_id", userId)
        .single();

      if (managerError || !managerData) {
        throw new Error("Yönetici kaydınız bulunamadı.");
      }

      // Yetki kontrolü - salon ID'sinden salon bilgilerini al
      const { data: gym } = await supabase
        .from("gyms")
        .select("id, manager_user_id")
        .eq("id", gymId) // gymId gerçekten gym ID'si
        .single();

      if (!gym) {
        throw new Error("Salon bulunamadı.");
      }

      // Yönetici değilse erişimi engelle
      if (managerData.user_id !== gym.manager_user_id) {
        throw new Error("Bu salona erişim yetkiniz yok.");
      }

      // Aktif üyeleri say
      const { count: activeMembersCount, error: membersError } = await supabase
        .from("memberships")
        .select("*", { count: "exact" })
        .eq("gym_id", gymId)
        .eq("status", "active");

      if (membersError) {
        throw new Error(`Üye sayısı alınamadı: ${membersError.message}`);
      }

      // Paketleri say
      const { data: packages, error: packagesError } = await supabase
        .from("gym_packages")
        .select("*")
        .eq("gym_id", gymId);

      if (packagesError) {
        throw new Error(`Paket bilgileri alınamadı: ${packagesError.message}`);
      }

      // Toplam geliri hesapla
      let totalRevenue = 0;

      if (packages && packages.length > 0) {
        const packageIds = packages.map(
          (pkg: Tables<"gym_packages">) => pkg.id
        );
        const { data: filteredSubscriptions, error } = await supabase
          .from("subscriptions")
          .select("purchase_price")
          .in("gym_package_id", packageIds);

        if (error) {
          throw new Error(`Abonelik bilgileri alınamadı: ${error.message}`);
        }

        totalRevenue =
          filteredSubscriptions?.reduce(
            (sum: number, sub: { purchase_price: number | null }) =>
              sum + (sub.purchase_price || 0),
            0
          ) || 0;
      }

      // İstatistikleri döndür
      return {
        activeMembers: activeMembersCount || 0,
        totalPackages: packages?.length || 0,
        totalRevenue,
      };
    },
    { requireAuth: true }
  );
}

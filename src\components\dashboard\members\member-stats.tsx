import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Users, UserCheck, UserX, TrendingUp } from "lucide-react";
import { MemberWithDetails } from "./members-page-client";

interface MemberStatsProps {
  membersByStatus: {
    all: MemberWithDetails[];
    active: MemberWithDetails[];
    passive: MemberWithDetails[];
  };
}

export function MemberStats({ membersByStatus }: MemberStatsProps) {
  const totalMembers = membersByStatus.all.length;
  const activeMembers = membersByStatus.active.length;
  const passiveMembers = membersByStatus.passive.length;

  // Calculate percentage of active members
  const activePercentage =
    totalMembers > 0 ? Math.round((activeMembers / totalMembers) * 100) : 0;

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <CardTitle className="text-sm font-medium">Toplam Üye</CardTitle>
          <Users className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{totalMembers}</div>
          <p className="text-xs text-muted-foreground">Kayıtlı üye sayısı</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <CardTitle className="text-sm font-medium">Aktif Üye</CardTitle>
          <UserCheck className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{activeMembers}</div>
          <p className="text-xs text-muted-foreground">Aktif üyelik durumu</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <CardTitle className="text-sm font-medium">Pasif Üye</CardTitle>
          <UserX className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{passiveMembers}</div>
          <p className="text-xs text-muted-foreground">Pasif üyelik durumu</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <CardTitle className="text-sm font-medium">Aktiflik Oranı</CardTitle>
          <TrendingUp className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">%{activePercentage}</div>
          <p className="text-xs text-muted-foreground">Aktif üye oranı</p>
        </CardContent>
      </Card>
    </div>
  );
}

"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Check, X } from "lucide-react"
import { useAuth } from "@/components/auth/auth-provider"
import { 
  approveMembershipRequest, 
  rejectMembershipRequest, 
  getAllMembershipRequestsByManagerId 
} from "@/app/actions/membership-actions"
import type { Tables } from "@/lib/supabase/types"
import { toast } from "sonner"

export function MemberRequestsTable() {
  const { session } = useAuth()
  const user = session?.user
  const [isLoading, setIsLoading] = useState(true)
  const [requests, setRequests] = useState<any[]>([])

  useEffect(() => {
    const fetchRequests = async () => {
      if (!user) return

      setIsLoading(true)
      
      try {
        // Server Action kullanarak tüm üyelik isteklerini getir
        const response = await getAllMembershipRequestsByManagerId(user.id);
        
        if (response.success && response.data) {
          setRequests(response.data)
        } else {
          console.error("İstekler getirilirken hata:", response.error);
          toast.error("Üyelik istekleri yüklenirken bir hata oluştu")
        }
      } catch (error) {
        console.error("Üyelik istekleri yüklenirken hata:", error);
        toast.error("Üyelik istekleri yüklenirken bir hata oluştu")
      } finally {
        setIsLoading(false)
      }
    }

    fetchRequests()
  }, [user])

 

  const handleApprove = async (requestId: string) => {
    try {
      const result = await approveMembershipRequest(requestId)

      if (result.success) {
        toast.success("Başarılı", {
          description: result.message,
        })

        // Remove the request from the list
        setRequests((prev) => prev.filter((request) => request.id !== requestId))
      } else {
        toast.error(result.message)
      }
    } catch (error) {
      console.error("Error approving request:", error)
      toast.error("İstek onaylanırken bir hata oluştu. Lütfen tekrar deneyin.")
    }
  }

  const handleReject = async (requestId: string) => {
    try {
      const result = await rejectMembershipRequest(requestId)

      if (result.success) {
        toast.success("Başarılı", {
          description: result.message,
        })

        // Remove the request from the list
        setRequests((prev) => prev.filter((request) => request.id !== requestId))
      } else {
        toast.error(result.message)
      }
    } catch (error) {
      console.error("Error rejecting request:", error)
      toast.error("İstek reddedilirken bir hata oluştu. Lütfen tekrar deneyin.")
    }
  }

  if (isLoading) {
    return (
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Üye</TableHead>
              <TableHead>Salon</TableHead>
              <TableHead>Tarih</TableHead>
              <TableHead>Durum</TableHead>
              <TableHead className="text-right">İşlemler</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <TableRow>
              <TableCell colSpan={5} className="h-24 text-center">
                <div className="flex justify-center">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                </div>
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </div>
    )
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Üye</TableHead>
            <TableHead>Salon</TableHead>
            <TableHead>Tarih</TableHead>
            <TableHead>Durum</TableHead>
            <TableHead className="text-right">İşlemler</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {requests.length > 0 ? (
            requests.map((request: any) => (
              <TableRow key={request.id}>
                <TableCell>
                  <div className="flex items-center gap-3">
                    <Avatar>
                      <AvatarImage
                        src={request.user.profile_picture_url || "/placeholder.svg?height=40&width=40"}
                        alt={`${request.user.name || ""} ${request.user.surname || ""}`}
                      />
                      <AvatarFallback>
                        {request.user.name && request.user.surname
                          ? `${request.user.name.charAt(0)}${request.user.surname.charAt(0)}`
                          : "??"}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="font-medium">
                        {request.user.name && request.user.surname
                          ? `${request.user.name} ${request.user.surname}`
                          : "İsimsiz Üye"}
                      </div>
                      <div className="text-sm text-muted-foreground">{request.user.email}</div>
                    </div>
                  </div>
                </TableCell>
                <TableCell>{request.gym.name}</TableCell>
                <TableCell>{new Date(request.request_date).toLocaleDateString("tr-TR")}</TableCell>
                <TableCell>
                  <div className="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold">
                    Bekliyor
                  </div>
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex justify-end gap-2">
                    <Button size="icon" variant="outline" className="h-8 w-8" onClick={() => handleApprove(request.id)}>
                      <Check className="h-4 w-4" />
                      <span className="sr-only">Onayla</span>
                    </Button>
                    <Button size="icon" variant="outline" className="h-8 w-8" onClick={() => handleReject(request.id)}>
                      <X className="h-4 w-4" />
                      <span className="sr-only">Reddet</span>
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={5} className="h-24 text-center">
                Bekleyen üyelik isteği bulunmuyor.
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  )
}

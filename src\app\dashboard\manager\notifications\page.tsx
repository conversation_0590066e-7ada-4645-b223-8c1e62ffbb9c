"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/components/auth/auth-provider";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Bell,
  CheckCircle,
  AlertTriangle,
  Info,
  CreditCard,
  Users,
  Package,
  Settings,
  Trash2,
  Eye,
  Filter,
  Loader2,
  Building,
} from "lucide-react";
import { cn } from "@/lib/utils";

interface Notification {
  id: string;
  title: string;
  message: string;
  type:
    | "info"
    | "warning"
    | "success"
    | "error"
    | "payment"
    | "member"
    | "package"
    | "system";
  isRead: boolean;
  createdAt: string;
  gymName?: string;
  actionUrl?: string;
}

export default function NotificationsPage() {
  const { authUser } = useAuth();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState<"all" | "unread" | "read">("all");

  useEffect(() => {
    const loadNotifications = async () => {
      if (!authUser) {
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        // Mock data - gerçek implementasyonda API'den gelecek
        const mockNotifications: Notification[] = [
          {
            id: "1",
            title: "Yeni Üye Kaydı",
            message: "Merkez Spor Salonu'na yeni bir üye kaydoldu.",
            type: "member",
            isRead: false,
            createdAt: "2024-01-15T10:30:00Z",
            gymName: "Merkez Spor Salonu",
            actionUrl: "/dashboard/manager/gym/merkez-spor-salonu/members",
          },
          {
            id: "2",
            title: "Ödeme Alındı",
            message: "Ahmet Yılmaz'dan 299₺ ödeme alındı.",
            type: "payment",
            isRead: false,
            createdAt: "2024-01-15T09:15:00Z",
            gymName: "Şube 1 Fitness",
            actionUrl: "/dashboard/manager/gym/sube-1-fitness/subscriptions",
          },
          {
            id: "3",
            title: "Paket Süresi Dolacak",
            message: "5 üyenin paket süresi 7 gün içinde dolacak.",
            type: "warning",
            isRead: true,
            createdAt: "2024-01-14T16:45:00Z",
            gymName: "Şube 2 Gym",
          },
          {
            id: "4",
            title: "Sistem Güncellemesi",
            message: "Platform yeni özelliklerle güncellendi.",
            type: "system",
            isRead: true,
            createdAt: "2024-01-14T08:00:00Z",
          },
          {
            id: "5",
            title: "Yeni Paket Oluşturuldu",
            message: "Premium Yıllık paketi başarıyla oluşturuldu.",
            type: "package",
            isRead: false,
            createdAt: "2024-01-13T14:20:00Z",
            gymName: "Merkez Spor Salonu",
          },
        ];

        // Simulated API delay
        await new Promise((resolve) => setTimeout(resolve, 800));
        setNotifications(mockNotifications);
      } catch (err) {
        console.error("Error loading notifications:", err);
        setError("Bildirimler yüklenirken bir hata oluştu.");
      } finally {
        setIsLoading(false);
      }
    };

    loadNotifications();
  }, [authUser]);

  const getNotificationIcon = (type: Notification["type"]) => {
    switch (type) {
      case "success":
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case "warning":
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
      case "error":
        return <AlertTriangle className="h-5 w-5 text-red-500" />;
      case "payment":
        return <CreditCard className="h-5 w-5 text-blue-500" />;
      case "member":
        return <Users className="h-5 w-5 text-purple-500" />;
      case "package":
        return <Package className="h-5 w-5 text-orange-500" />;
      case "system":
        return <Settings className="h-5 w-5 text-gray-500" />;
      default:
        return <Info className="h-5 w-5 text-blue-500" />;
    }
  };

  const getNotificationBadgeColor = (type: Notification["type"]) => {
    switch (type) {
      case "success":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
      case "warning":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";
      case "error":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";
      case "payment":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";
      case "member":
        return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200";
      case "package":
        return "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200";
      case "system":
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200";
      default:
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("tr-TR", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const markAsRead = (id: string) => {
    setNotifications((prev) =>
      prev.map((notification) =>
        notification.id === id
          ? { ...notification, isRead: true }
          : notification
      )
    );
  };

  const markAsUnread = (id: string) => {
    setNotifications((prev) =>
      prev.map((notification) =>
        notification.id === id
          ? { ...notification, isRead: false }
          : notification
      )
    );
  };

  const deleteNotification = (id: string) => {
    setNotifications((prev) =>
      prev.filter((notification) => notification.id !== id)
    );
  };

  const markAllAsRead = () => {
    setNotifications((prev) =>
      prev.map((notification) => ({ ...notification, isRead: true }))
    );
  };

  const filteredNotifications = notifications.filter((notification) => {
    if (filter === "unread") return !notification.isRead;
    if (filter === "read") return notification.isRead;
    return true;
  });

  const unreadCount = notifications.filter((n) => !n.isRead).length;

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="flex items-center gap-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Bildirimler yükleniyor...</span>
        </div>
      </div>
    );
  }

  if (!authUser) {
    return (
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          Bildirimleri görüntülemek için giriş yapmanız gerekiyor.
        </AlertDescription>
      </Alert>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <Bell className="h-8 w-8" />
            Bildirimler
            {unreadCount > 0 && (
              <Badge variant="destructive" className="ml-2">
                {unreadCount}
              </Badge>
            )}
          </h1>
          <p className="text-muted-foreground">
            Tüm salonlarınızdan gelen bildirimler ve sistem mesajları
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={markAllAsRead}
            disabled={unreadCount === 0}
          >
            Tümünü Okundu İşaretle
          </Button>
        </div>
      </div>

      {/* Filter Tabs */}
      <div className="flex gap-2">
        {[
          { key: "all", label: `Tümü (${notifications.length})` },
          { key: "unread", label: `Okunmamış (${unreadCount})` },
          {
            key: "read",
            label: `Okunmuş (${notifications.length - unreadCount})`,
          },
        ].map((filterOption) => (
          <Button
            key={filterOption.key}
            variant={filter === filterOption.key ? "default" : "outline"}
            size="sm"
            onClick={() => setFilter(filterOption.key as any)}
          >
            {filterOption.label}
          </Button>
        ))}
      </div>

      {/* Notifications List */}
      {filteredNotifications.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Bell className="h-12 w-12 text-muted-foreground mb-4" />
            <p className="text-lg font-medium text-muted-foreground">
              {filter === "unread"
                ? "Okunmamış bildirim yok"
                : filter === "read"
                ? "Okunmuş bildirim yok"
                : "Henüz bildirim yok"}
            </p>
            <p className="text-sm text-muted-foreground">
              Yeni bildirimler burada görünecek
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-3">
          {filteredNotifications.map((notification) => (
            <Card
              key={notification.id}
              className={cn(
                "transition-all duration-200 hover:shadow-md",
                !notification.isRead &&
                  "border-l-4 border-l-blue-500 bg-blue-50/50 dark:bg-blue-950/20"
              )}
            >
              <CardContent className="p-4">
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-3 flex-1">
                    {getNotificationIcon(notification.type)}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <h3
                          className={cn(
                            "font-medium",
                            !notification.isRead && "font-semibold"
                          )}
                        >
                          {notification.title}
                        </h3>
                        <Badge
                          className={getNotificationBadgeColor(
                            notification.type
                          )}
                        >
                          {notification.type}
                        </Badge>
                        {!notification.isRead && (
                          <div className="h-2 w-2 rounded-full bg-blue-500" />
                        )}
                      </div>
                      <p className="text-sm text-muted-foreground mb-2">
                        {notification.message}
                      </p>
                      <div className="flex items-center gap-4 text-xs text-muted-foreground">
                        <span>{formatDate(notification.createdAt)}</span>
                        {notification.gymName && (
                          <span className="flex items-center gap-1">
                            <Building className="h-3 w-3" />
                            {notification.gymName}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-1 ml-4">
                    {notification.isRead ? (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => markAsUnread(notification.id)}
                        className="h-8 w-8 p-0"
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                    ) : (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => markAsRead(notification.id)}
                        className="h-8 w-8 p-0"
                      >
                        <CheckCircle className="h-4 w-4" />
                      </Button>
                    )}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => deleteNotification(notification.id)}
                      className="h-8 w-8 p-0 text-red-500 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}

import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"

export function HeroSection() {
  return (
    <section className="w-full flex flex-col items-center justify-centerpy-12 md:py-24 lg:py-32 bg-gradient-to-b from-white to-gray-100 dark:from-gray-900 dark:to-gray-950">
      <div className="container px-4 md:px-6">
        <div className="grid gap-6 lg:grid-cols-2 lg:gap-12 xl:gap-16">
          <div className="flex flex-col justify-center space-y-4">
            <div className="space-y-2">
              <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl lg:text-6xl/none">
                Spor Salonları ve Üyeler İçin Tek Platform
              </h1>
              <p className="max-w-[600px] text-gray-500 md:text-xl dark:text-gray-400">
                Sportiva ile salon yönetimini dijitalleştirin, üyelerinizle etkileşimi artırın ve işletmenizi büyütün.
              </p>
            </div>
            <div className="flex flex-col gap-2 min-[400px]:flex-row">
              <Link href="/register">
                <Button size="lg" className="px-8">
                  Hemen Başla
                </Button>
              </Link>
              <Link href="/findGym">
                <Button size="lg" variant="outline" className="px-8">
                  Salon Bul
                </Button>
              </Link>
            </div>
          </div>
          <div className="flex items-center justify-center">
            <img
              alt="Sportiva Platform"
              className="aspect-video overflow-hidden rounded-xl object-cover object-center"
              src="/placeholder.svg?height=550&width=750"
            />
          </div>
        </div>
      </div>
    </section>
  )
}

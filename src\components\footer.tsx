"use client"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { Button } from "./ui/button"
import { Sun, Moon } from "lucide-react"
import { useTheme } from "next-themes"

function Footer() {
  const { theme, setTheme } = useTheme()

  return (
    <footer className="w-full flex flex-col items-center justify-center border-t bg-background">
      <div className="container flex flex-col items-center justify-between gap-4 py-10 md:h-24 md:flex-row md:py-0">
        <div className="flex flex-col items-center gap-4 px-8 md:flex-row md:gap-2 md:px-0">
          <Link href="/" className="flex items-center gap-2 font-bold">
            <span className="text-primary">Sportiva</span>
          </Link>
          <p className="text-center text-sm leading-loose text-muted-foreground md:text-left">
            &copy; {new Date().getFullYear()} Sportiva. Tüm hakları saklıdır.
          </p>
        </div>
        <div className="flex gap-4">
          <Link href="/terms" className="text-sm text-muted-foreground hover:underline">
            Kullanım Şartları
          </Link>
          <Link href="/privacy" className="text-sm text-muted-foreground hover:underline">
            Gizlilik Politikası
          </Link>
          <Link href="/contact" className="text-sm text-muted-foreground hover:underline">
            İletişim
          </Link>
          {/* Theme toggle button */}
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
            aria-label="Toggle theme"
          >
            <Sun className="h-5 w-5 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
            <Moon className="absolute h-5 w-5 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
            <span className="sr-only">Toggle theme</span>
          </Button>
        </div>
      </div>
    </footer>
  )
}

export default function ConditionalFooter() {
  const pathname = usePathname()

  // Don't show footer on dashboard pages
  if (pathname.startsWith("/dashboard")) {
    return null
  }

  return <Footer />
}

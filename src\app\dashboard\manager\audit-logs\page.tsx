"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Calendar, Filter, Download, Eye, AlertTriangle, CheckCircle, XCircle } from "lucide-react";
import { getGymAuditLogs } from "@/app/actions/manager-actions";
import { toast } from "sonner";

interface AuditLog {
  id: string;
  action: string;
  resource_type: string;
  description: string;
  user_name: string;
  user_email: string;
  status: 'success' | 'failed' | 'warning';
  created_at: string;
  metadata?: any;
  old_values?: any;
  new_values?: any;
}

export default function AuditLogsPage() {
  const [logs, setLogs] = useState<AuditLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState({
    action: '',
    resourceType: '',
    status: '',
    startDate: '',
    endDate: '',
    limit: 50
  });

  // Gym ID'sini localStorage'dan al (gerçek uygulamada context'ten gelecek)
  const [gymId, setGymId] = useState<string>('');

  useEffect(() => {
    const storedGymId = localStorage.getItem('selectedGymId');
    if (storedGymId) {
      setGymId(storedGymId);
    }
  }, []);

  useEffect(() => {
    if (gymId) {
      loadAuditLogs();
    }
  }, [gymId]);

  const loadAuditLogs = async () => {
    if (!gymId) return;
    
    setLoading(true);
    try {
      const result = await getGymAuditLogs(gymId, filters);
      
      if (result.success && result.data) {
        setLogs(result.data);
      } else {
        toast.error(result.error || 'Audit logları yüklenirken hata oluştu');
      }
    } catch (error) {
      toast.error('Audit logları yüklenirken hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  const applyFilters = () => {
    loadAuditLogs();
  };

  const clearFilters = () => {
    setFilters({
      action: '',
      resourceType: '',
      status: '',
      startDate: '',
      endDate: '',
      limit: 50
    });
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      default:
        return null;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      success: 'bg-green-100 text-green-800',
      failed: 'bg-red-100 text-red-800',
      warning: 'bg-yellow-100 text-yellow-800'
    };

    return (
      <Badge className={variants[status as keyof typeof variants] || 'bg-gray-100 text-gray-800'}>
        {status === 'success' ? 'Başarılı' : status === 'failed' ? 'Başarısız' : 'Uyarı'}
      </Badge>
    );
  };

  const getActionText = (action: string) => {
    const actionTexts: Record<string, string> = {
      'create_member': 'Üye Oluşturma',
      'invite_member': 'Üye Davet',
      'update_gym_settings': 'Salon Ayarları Güncelleme',
      'search_users': 'Kullanıcı Arama',
      'view_audit_logs': 'Audit Log Görüntüleme',
      'update_member': 'Üye Güncelleme',
      'delete_member': 'Üye Silme',
      'approve_membership': 'Üyelik Onaylama',
      'reject_membership': 'Üyelik Reddetme'
    };
    
    return actionTexts[action] || action;
  };

  const getResourceTypeText = (resourceType: string) => {
    const resourceTexts: Record<string, string> = {
      'gym': 'Salon',
      'member': 'Üye',
      'membership': 'Üyelik',
      'user': 'Kullanıcı',
      'system': 'Sistem'
    };
    
    return resourceTexts[resourceType] || resourceType;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('tr-TR');
  };

  if (!gymId) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardContent className="p-6">
            <p className="text-center text-muted-foreground">
              Audit logları görüntülemek için bir salon seçin.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Audit Logları</h1>
          <p className="text-muted-foreground">
            Salon işlemlerinin güvenlik ve denetim kayıtları
          </p>
        </div>
        <Button variant="outline" className="gap-2">
          <Download className="h-4 w-4" />
          Dışa Aktar
        </Button>
      </div>

      {/* Filtreler */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filtreler
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
            <Select value={filters.action} onValueChange={(value) => setFilters({...filters, action: value})}>
              <SelectTrigger>
                <SelectValue placeholder="İşlem Türü" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Tümü</SelectItem>
                <SelectItem value="create_member">Üye Oluşturma</SelectItem>
                <SelectItem value="invite_member">Üye Davet</SelectItem>
                <SelectItem value="update_gym_settings">Salon Ayarları</SelectItem>
                <SelectItem value="search_users">Kullanıcı Arama</SelectItem>
              </SelectContent>
            </Select>

            <Select value={filters.resourceType} onValueChange={(value) => setFilters({...filters, resourceType: value})}>
              <SelectTrigger>
                <SelectValue placeholder="Kaynak Türü" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Tümü</SelectItem>
                <SelectItem value="gym">Salon</SelectItem>
                <SelectItem value="member">Üye</SelectItem>
                <SelectItem value="membership">Üyelik</SelectItem>
                <SelectItem value="user">Kullanıcı</SelectItem>
              </SelectContent>
            </Select>

            <Select value={filters.status} onValueChange={(value) => setFilters({...filters, status: value})}>
              <SelectTrigger>
                <SelectValue placeholder="Durum" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Tümü</SelectItem>
                <SelectItem value="success">Başarılı</SelectItem>
                <SelectItem value="failed">Başarısız</SelectItem>
                <SelectItem value="warning">Uyarı</SelectItem>
              </SelectContent>
            </Select>

            <Input
              type="date"
              placeholder="Başlangıç Tarihi"
              value={filters.startDate}
              onChange={(e) => setFilters({...filters, startDate: e.target.value})}
            />

            <Input
              type="date"
              placeholder="Bitiş Tarihi"
              value={filters.endDate}
              onChange={(e) => setFilters({...filters, endDate: e.target.value})}
            />

            <div className="flex gap-2">
              <Button onClick={applyFilters} className="flex-1">
                Filtrele
              </Button>
              <Button variant="outline" onClick={clearFilters}>
                Temizle
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Audit Logları */}
      <Card>
        <CardHeader>
          <CardTitle>İşlem Kayıtları ({logs.length})</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">Yükleniyor...</p>
            </div>
          ) : logs.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">Henüz audit log kaydı bulunmuyor.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {logs.map((log) => (
                <div key={log.id} className="border rounded-lg p-4 space-y-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-3">
                      {getStatusIcon(log.status)}
                      <div>
                        <h3 className="font-medium">{log.description}</h3>
                        <p className="text-sm text-muted-foreground">
                          {getActionText(log.action)} • {getResourceTypeText(log.resource_type)}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {getStatusBadge(log.status)}
                      <span className="text-sm text-muted-foreground">
                        {formatDate(log.created_at)}
                      </span>
                    </div>
                  </div>
                  
                  {log.user_name && (
                    <div className="text-sm text-muted-foreground">
                      <strong>Kullanıcı:</strong> {log.user_name} ({log.user_email})
                    </div>
                  )}
                  
                  {log.metadata && (
                    <details className="text-sm">
                      <summary className="cursor-pointer text-muted-foreground hover:text-foreground">
                        Detayları Görüntüle
                      </summary>
                      <pre className="mt-2 p-2 bg-muted rounded text-xs overflow-auto">
                        {JSON.stringify(log.metadata, null, 2)}
                      </pre>
                    </details>
                  )}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

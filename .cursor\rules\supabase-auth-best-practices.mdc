---
description:
globs:
alwaysApply: false
---
# Next.js ve Supabase Auth Güvenlik En İyi Uygulamaları

Bu kural, Next.js ve Supabase ile kimlik doğrulama sistemlerinde güvenlik ve performans için en iyi uygulamaları açıklar.

## Genel Güvenlik İlkeleri

### 1. Her Zaman HTTPS Kullanın

- Geliştirme ortamında bile HTTPS kullanmaya çalışın.
- Production ortamında kesinlikle HTTPS kullanın.
- Auth cookie'leri için `secure` flagini etkinleştirin.

```typescript
// src/lib/supabase/server.ts içinde cookie ayarlarında:
cookies: {
  // ... diğer ayarlar
  set(name: string, value: string, options) {
    cookieStore.set({
      name,
      value,
      ...options,
      // Production ortamında secure flag'i etkinleştir
      secure: process.env.NODE_ENV === 'production',
      httpOnly: true,
      sameSite: 'lax'
    })
  }
}
```

### 2. Güçlü Şifre Politikaları

```typescript
// Kullanıcı kayıt formunda şifre doğrulama
import { z } from 'zod'

const SignUpSchema = z.object({
  email: z.string().email({ message: 'Geçerli bir e-posta adresi girin.' }),
  password: z.string()
    .min(8, { message: 'Şifre en az 8 karakter olmalıdır.' })
    .regex(/[a-z]/, { message: 'Şifrede en az bir küçük harf olmalıdır.' })
    .regex(/[A-Z]/, { message: 'Şifrede en az bir büyük harf olmalıdır.' })
    .regex(/[0-9]/, { message: 'Şifrede en az bir rakam olmalıdır.' })
    .regex(/[^a-zA-Z0-9]/, { message: 'Şifrede en az bir özel karakter olmalıdır.' })
})
```

Supabase Dashboard'da da şifre politikalarını yapılandırın:
Authentication > Policies > Password Strength bölümünde minimum şifre uzunluğu ve karmaşıklık kurallarını ayarlayın.

### 3. E-posta Doğrulamayı Zorunlu Tutun

Supabase Dashboard'da Authentication > Email Templates > Confirm signup bölümünde e-posta doğrulama ayarlarını yapılandırın ve bunu zorunlu tutun.

```typescript
// Supabase Dashboard'da yapılandırdıktan sonra, kullanıcı kayıt işlemlerinde e-posta doğrulama durumunu kontrol edin:
'use client'

import { useEffect, useState } from 'react'
import { createSupabaseBrowserClient } from '@/lib/supabase/client'

export default function ConfirmEmailMessage() {
  const supabase = createSupabaseBrowserClient()
  const [user, setUser] = useState<any>(null)
  
  useEffect(() => {
    const getUser = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      setUser(user)
    }
    
    getUser()
  }, [supabase])
  
  if (!user) return null
  
  // E-posta doğrulanmamış kullanıcılara uyarı göster
  if (user && !user.email_confirmed_at) {
    return (
      <div className="p-4 bg-yellow-100 border border-yellow-400 text-yellow-800 rounded">
        <p>Lütfen e-posta adresinizi doğrulayın. Doğrulama bağlantısını içeren bir e-posta gönderildi.</p>
        <button
          className="mt-2 text-sm underline"
          onClick={async () => {
            await supabase.auth.resetPasswordForEmail(user.email, {
              redirectTo: `${window.location.origin}/auth/callback`,
            })
          }}
        >
          Doğrulama e-postasını tekrar gönder
        </button>
      </div>
    )
  }
  
  return null
}
```

### 4. Rate Limiting ve Brute Force Koruması

Supabase'de otomatik olarak temel korumalar bulunur, ancak ek önlemler alabilirsiniz:

1. **Supabase Dashboard'da Auth Rate Limits Ayarları**: Authentication > Rate Limits kısmındaki koruma ayarlarını yapılandırın.
2. **API İsteklerini Kısıtlama**: Edge fonksiyonları veya middleware ile ek koruma ekleyin.

```typescript
// src/middleware.ts içinde login sayfası rate limiting örneği
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { Redis } from '@upstash/redis' // Örnek için bir redis kütüphanesi

// Upstash Redis gibi bir servis kullanabilirsiniz
const redis = Redis.fromEnv()

export async function middleware(req: NextRequest) {
  if (req.nextUrl.pathname === '/auth/login' && req.method === 'POST') {
    const ip = req.ip || req.headers.get('x-forwarded-for') || '127.0.0.1'
    const key = `login_attempts:${ip}`
    
    // Son 10 dakikadaki giriş denemeleri
    const attempts = await redis.incr(key)
    await redis.expire(key, 60 * 10) // 10 dakika
    
    // 5 dakikada 5'ten fazla başarısız girişim
    if (attempts > 5) {
      return new NextResponse(
        JSON.stringify({ error: 'Çok fazla giriş denemesi. Lütfen 10 dakika sonra tekrar deneyin.' }),
        { status: 429, headers: { 'Content-Type': 'application/json' } }
      )
    }
  }
  
  return NextResponse.next()
}
```

### 5. JWT Güvenliği ve Token Yönetimi

JWT'lerin güvenli kullanımı için:

1. **Kısa Süreli Access Token, Uzun Süreli Refresh Token**: Supabase Dashboard'da JWT sürelerini yapılandırın. Access token için 15-60 dakika, refresh token için 1-2 hafta.

2. **JWT Claims Kontrolü**: JWT claim'leri kullanıcı rollerini belirlemek için güvenli bir şekilde kullanın.

```typescript
// src/lib/auth.ts
import { createSupabaseServerClient } from '@/lib/supabase/server'
import { jwtVerify } from 'jose' // veya başka bir JWT kütüphanesi

export async function validateAndGetClaims() {
  const supabase = createSupabaseServerClient()
  const { data: { user } } = await supabase.auth.getUser()
  
  if (!session?.access_token) {
    return null
  }
  
  try {
    // JWT'yi doğrula (Supabase bunu zaten yapıyor, bu sadece ek bir kontrol)
    // Gerçek uygulamada PUBLIC_KEY kullanmalısınız
    const { payload } = await jwtVerify(
      session.access_token, 
      new TextEncoder().encode(process.env.JWT_SECRET || 'your-jwt-secret')
    )
    
    return {
      sub: payload.sub,
      email: payload.email,
      role: payload.user_role,
      // Diğer claim'ler
    }
  } catch (err) {
    console.error('JWT doğrulama hatası:', err)
    return null
  }
}
```

### 6. Row Level Security (RLS) Politikaları

Veritabanı güvenliği için düzgün RLS politikaları oluşturun:

```sql
-- Kullanıcı profili tablosu için RLS örneği
create table public.profiles (
  id uuid references auth.users primary key,
  username text unique not null,
  full_name text,
  avatar_url text,
  updated_at timestamp with time zone
);

-- RLS etkinleştirme
alter table public.profiles enable row level security;

-- Sadece kendi profilini okuyabilme
create policy "Kullanıcılar kendi profillerini okuyabilir" on public.profiles
  for select using (auth.uid() = id);

-- Sadece kendi profilini düzenleyebilme
create policy "Kullanıcılar kendi profillerini düzenleyebilir" on public.profiles
  for update using (auth.uid() = id);

-- Admin kullanıcılar tüm profillere erişebilir
create policy "Admin kullanıcılar tüm profillere erişebilir" on public.profiles
  for all using ((select role from user_roles where id = auth.uid()) = 'admin');
```

### 7. XSS ve CSRF Koruması

**XSS (Cross-Site Scripting) için:**

```tsx
// Kullanıcı girdisi işlerken her zaman sanitize edin
import DOMPurify from 'dompurify'

function UserContent({ content }) {
  const sanitizedContent = DOMPurify.sanitize(content)
  return <div dangerouslySetInnerHTML={{ __html: sanitizedContent }} />
}
```

**CSRF (Cross-Site Request Forgery) için:**

Next.js ve Supabase'in modern ayarları zaten CSRF koruması sağlar, ancak Server Actions kullanırken:

```typescript
// src/app/actions.ts
'use server'

import { cookies } from 'next/headers'

export async function protectedAction(formData: FormData) {
  // Server Actions zaten Next.js tarafından CSRF korumasına sahiptir
  // Next.js form'lardan gelen istekleri doğrular
  
  // Ek bir CSRF token kontrolü de uygulayabilirsiniz:
  const csrfToken = cookies().get('csrf_token')?.value
  const formToken = formData.get('csrf_token') as string
  
  if (!csrfToken || !formToken || csrfToken !== formToken) {
    throw new Error('CSRF doğrulama hatası')
  }
  
  // ... güvenli işlemler ...
}
```

### 8. Bağımlılıkları Güncel Tutun

Güvenlik güncellemeleri için düzenli olarak bağımlılıkları güncelleyin:

```bash
# npm ile güncelleme
npm update @supabase/supabase-js @supabase/ssr

# Güvenlik açıkları kontrolü
npm audit
```

### 9. Hata Yönetimi ve Loglama

Güvenlikle ilgili olayları loglayın, ancak hassas bilgileri göstermeyin:

```typescript
// src/lib/logger.ts
type LogLevel = 'info' | 'warn' | 'error'

interface LogParams {
  action: string
  userId?: string
  error?: any
  details?: Record<string, any>
}

export function logAuthEvent({ action, userId, error, details }: LogParams, level: LogLevel = 'info') {
  // Hassas bilgileri ayıkla
  const safeDetails = { ...details }
  delete safeDetails.password
  delete safeDetails.token
  delete safeDetails.secret
  
  // Log formatını oluştur
  const logData = {
    timestamp: new Date().toISOString(),
    action,
    userId,
    ...(error && { errorMessage: error.message }),
    ...(safeDetails && { details: safeDetails })
  }
  
  // Log seviyesine göre davran
  if (level === 'error') {
    console.error(`AUTH ERROR: ${action}`, logData)
    // Hata izleme servisine de gönderebilirsiniz
  } else if (level === 'warn') {
    console.warn(`AUTH WARNING: ${action}`, logData)
  } else {
    console.log(`AUTH INFO: ${action}`, logData)
  }
  
  // Önemli olayları veritabanına veya harici log servisine kaydedebilirsiniz
}

// Kullanım:
// import { logAuthEvent } from '@/lib/logger'
// 
// try {
//   // Auth işlemi
// } catch (error) {
//   logAuthEvent({
//     action: 'login_failed',
//     userId: email,
//     error,
//     details: { attemptCount }
//   }, 'error')
// }
```

### 10. Güvenli Çerez Yönetimi

```typescript
// src/lib/supabase/server.ts içinde çerez ayarları
cookies: {
  // ... diğer metodlar
  set(name: string, value: string, options) {
    cookieStore.set({
      name,
      value,
      // Şu özellikleri ekleyin:
      path: '/',
      sameSite: 'lax', // CSRF koruması için
      secure: process.env.NODE_ENV === 'production', // Sadece HTTPS üzerinden
      httpOnly: true, // JavaScript ile erişilemez
      // Max-age veya expires gerekmez, Supabase kendi token yönetimini yapar
      ...options
    })
  }
}
```

### 11. Multi-faktör Doğrulama (MFA) Kullanımı

```tsx
// src/app/settings/security/page.tsx
'use client'

import { useState } from 'react'
import { createSupabaseBrowserClient } from '@/lib/supabase/client'
import QRCode from 'qrcode.react'

export default function MFASetupPage() {
  const supabase = createSupabaseBrowserClient()
  const [factorId, setFactorId] = useState<string | null>(null)
  const [qrCode, setQrCode] = useState<string | null>(null)
  const [verifyCode, setVerifyCode] = useState('')
  const [status, setStatus] = useState<'idle' | 'enrolling' | 'verifying' | 'success'>('idle')
  
  const setupMFA = async () => {
    setStatus('enrolling')
    const { data, error } = await supabase.auth.mfa.enroll({
      factorType: 'totp'
    })
    
    if (error) {
      console.error('MFA setup error:', error)
      setStatus('idle')
      return
    }
    
    setFactorId(data.id)
    setQrCode(data.totp.qr_code)
    setStatus('verifying')
  }
  
  const verifyMFA = async () => {
    if (!factorId) return
    
    try {
      // Challenge oluştur
      const { error: challengeError } = await supabase.auth.mfa.challenge({ factorId })
      if (challengeError) throw challengeError
      
      // AAL seviyesini al
      const aal = supabase.auth.mfa.getAuthenticatorAssuranceLevel()
      
      // Doğrulama kodunu doğrula
      const { error: verifyError } = await supabase.auth.mfa.verify({
        factorId,
        challengeId: aal.currentChallenge?.id || '',
        code: verifyCode
      })
      
      if (verifyError) throw verifyError
      
      setStatus('success')
    } catch (error) {
      console.error('MFA verification error:', error)
      setStatus('verifying') // Tekrar denemeye izin ver
    }
  }
  
  return (
    <div>
      <h1>İki Faktörlü Kimlik Doğrulama</h1>
      
      {status === 'idle' && (
        <button onClick={setupMFA}>MFA Kurulumunu Başlat</button>
      )}
      
      {status === 'enrolling' && <p>Kurulum hazırlanıyor...</p>}
      
      {status === 'verifying' && qrCode && (
        <>
          <p>Kimlik doğrulama uygulamanızdan bu QR kodu tarayın:</p>
          <QRCode value={qrCode} size={200} />
          
          <div className="mt-4">
            <input 
              type="text" 
              placeholder="6 haneli kod" 
              value={verifyCode}
              onChange={e => setVerifyCode(e.target.value)}
              maxLength={6}
            />
            <button onClick={verifyMFA}>Doğrula</button>
          </div>
        </>
      )}
      
      {status === 'success' && (
        <div className="success-message">
          <p>İki faktörlü kimlik doğrulama başarıyla etkinleştirildi!</p>
          <p>Şimdi her girişte kimlik doğrulama kodunuz sorulacak.</p>
        </div>
      )}
    </div>
  )
}
```

## Özet: En İyi Uygulamalar Kontrol Listesi

✅ HTTPS Kullanın  
✅ Güçlü Şifre Politikaları Uygulayın  
✅ E-posta Doğrulamayı Zorunlu Kılın  
✅ Rate Limiting ve Brute Force Koruması Ekleyin  
✅ JWT Güvenliği ve Token Yönetimini Doğru Yapın  
✅ Row Level Security (RLS) Politikaları Tanımlayın  
✅ XSS ve CSRF Koruması Sağlayın  
✅ Bağımlılıkları Güncel Tutun  
✅ Hata Yönetimi ve Loglama Uygulayın  
✅ Güvenli Çerez Yönetimi Yapın  
✅ Mümkünse Multi-faktör Doğrulama (MFA) Kullanın  

Bu kurallar ve en iyi uygulamalar, Next.js ve Supabase ile oluşturduğunuz kimlik doğrulama sistemlerinin güvenli ve sağlam olmasını sağlayacaktır.

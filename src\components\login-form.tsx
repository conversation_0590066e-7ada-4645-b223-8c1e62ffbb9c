"use client"

import { useEffect, useState } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { useActionState } from "react"
import { loginUser, sendPasswordResetEmail } from "@/app/actions/auth-actions"
import { ApiResponse } from "@/lib/actions/types"
import { z } from "zod"

import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle, Loader2, Mail, Lock, ArrowRight, CheckCircle } from "lucide-react"
import { toast } from "sonner"
import { useFormStatus } from "react-dom"

// Validation Schema
const loginSchema = z.object({
  email: z.string().email({ message: "<PERSON>e<PERSON><PERSON><PERSON> bir e-posta adresi giri<PERSON>" }),
  password: z.string().min(1, { message: "<PERSON><PERSON><PERSON> bo<PERSON> o<PERSON>az" }),
})

const initialState: ApiResponse = {
  success: false,
  error: undefined,
  message: undefined,
  data: undefined,
}

// Gönder Butonu Bileşeni
function SubmitButton() {
  const { pending } = useFormStatus()
  
  return (
    <Button type="submit" className="w-full" disabled={pending}>
      {pending ? (
        <>
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          Giriş Yapılıyor...
        </>
      ) : (
        <>
          Giriş Yap
          <ArrowRight className="ml-2 h-4 w-4" />
        </>
      )}
    </Button>
  )
}

// Şifre Sıfırlama Butonu Bileşeni
function ResetPasswordButton({ email, disabled }: { email: string; disabled: boolean }) {
  const [isResetting, setIsResetting] = useState(false)
  
  const handlePasswordReset = async (e: React.MouseEvent) => {
    e.preventDefault()
    
    if (!email) {
      toast.error("E-posta adresi gerekli", {
        description: "Şifre sıfırlama için e-posta adresinizi girin.",
      })
      return
    }

    try {
      setIsResetting(true)
      const response = await sendPasswordResetEmail(email)

      if (response.success) {
        toast.success("Şifre sıfırlama e-postası gönderildi", {
          description: `${email} adresine şifre sıfırlama talimatları gönderildi. Lütfen e-postanızı kontrol edin.`,
        })
      } else {
        toast.error("Şifre sıfırlama hatası", {
          description: response.error,
        })
      }
    } catch (error: any) {
      toast.error("Şifre sıfırlama başarısız", {
        description: error.message || "Beklenmeyen bir hata oluştu.",
      })
    } finally {
      setIsResetting(false)
    }
  }
  
  return (
    <button
      type="button"
      onClick={handlePasswordReset}
      className="text-sm text-primary hover:underline focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-1 rounded-sm"
      disabled={isResetting || !email || disabled}
    >
      {isResetting ? (
        <>
          <Loader2 className="mr-1 h-3 w-3 inline animate-spin" />
          İşleniyor...
        </>
      ) : (
        "Şifremi Unuttum"
      )}
    </button>
  )
}

export function LoginForm() {
  const router = useRouter()
  const [email, setEmail] = useState("")

  // Server Action ile form durumunu yönet
  const [state, action, isPending] = useActionState(
    async (prevState: ApiResponse, formData: FormData) => {
      // Form verilerini doğrula
      const validationResult = loginSchema.safeParse({
        email: formData.get("email"),
        password: formData.get("password"),
      })

      if (!validationResult.success) {
        const errors = validationResult.error.flatten().fieldErrors
        const firstError = Object.values(errors)[0]?.[0] || "Geçersiz form verisi"
        return {
          success: false,
          error: firstError,
          data: {
            email: formData.get("email") as string
          }
        }
      }

      // Server Action'ı çağır
      return loginUser(prevState, formData)
    },
    initialState
  )

  // Form durumundaki e-postayı güncelle
  useEffect(() => {
    if (state?.data?.email && !email) {
      setEmail(state.data.email)
    }
  }, [state?.data?.email, email])

  // Başarılı giriş sonrası yönlendirme
  useEffect(() => {
    if (state?.success) {
      toast.success(state.message || "Giriş başarılı")

      // Role göre yönlendirme
      if (
        state.data?.user?.user_metadata?.role === "manager" ||
        state.data?.user?.app_metadata?.role === "manager"
      ) {
        router.push("/dashboard/manager")
      } else {
        router.push("/dashboard/member")
      }
    } else if (state?.error && state.error !== initialState.error) {
      toast.error("Giriş yapılamadı", {
        description: state.error,
      })
    }
  }, [state, router])

  return (
    <Card className="w-full border-none shadow-lg">
      <CardHeader>
        <CardTitle>Giriş Yap</CardTitle>
        <CardDescription>Hesabınıza giriş yaparak devam edin.</CardDescription>
      </CardHeader>
      <CardContent className="p-6">
        {state?.error && (
          <Alert variant="destructive" className="mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Hata</AlertTitle>
            <AlertDescription>{state.error}</AlertDescription>
          </Alert>
        )}
        
        {state?.success && (
          <Alert variant="default" className="mb-6 bg-green-50 border-green-400">
            <CheckCircle className="h-4 w-4 text-green-500" />
            <AlertTitle>Başarılı</AlertTitle>
            <AlertDescription>{state.message || "Giriş başarılı."}</AlertDescription>
          </Alert>
        )}
        
        <form action={action} className="space-y-4" autoComplete="on" noValidate>
          <div className="space-y-2">
            <Label htmlFor="email" className="text-sm font-medium">
              E-posta Adresi
            </Label>
            <div className="relative">
              <Mail className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                id="email"
                name="email"
                type="email"
                placeholder="<EMAIL>"
                required
                autoComplete="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="pl-10"
                aria-describedby="email-error"
              />
            </div>
          </div>
          
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="password" className="text-sm font-medium">
                Şifre
              </Label>
              <ResetPasswordButton email={email} disabled={isPending} />
            </div>
            <div className="relative">
              <Lock className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                id="password"
                name="password"
                type="password"
                required
                autoComplete="current-password"
                className="pl-10"
                aria-describedby="password-error"
              />
            </div>
          </div>
          
          <div className="pt-2">
            <SubmitButton />
          </div>
        </form>
      </CardContent>
    </Card>
  )
}

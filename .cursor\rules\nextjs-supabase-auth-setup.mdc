---
description:
globs:
alwaysApply: false
---
# Next.js ve Supabase Auth Kurulum Rehberi

Bu kural, Next.js (App Router) ve Supabase ile kimlik doğrulama sistemi kurmak için gerekli adımları açıklar.

## Proje <PERSON>

### Next.js Pro<PERSON><PERSON>

```bash
npx create-next-app@latest my-auth-app --typescript --tailwind --eslint
cd my-auth-app
```

### Gerekli Paketleri Yükleme

```bash
npm install @supabase/supabase-js @supabase/ssr
```

### Çevresel Değişkenleri Yapılandırma

`.env.local` dosyasında şu değişkenleri tanımlayın:

```env
NEXT_PUBLIC_SUPABASE_URL=YOUR_SUPABASE_PROJECT_URL
NEXT_PUBLIC_SUPABASE_ANON_KEY=YOUR_SUPABASE_ANON_KEY
```

## Supabase İstemcileri Oluşturma

### <PERSON><PERSON><PERSON><PERSON><PERSON> (Client Components için)

```typescript
// src/lib/supabase/client.ts
import { createBrowserClient } from '@supabase/ssr'
import type { Database } from './database.types' // İsteğe bağlı

export function createSupabaseBrowserClient() {
  return createBrowserClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  )
}
```

### Sunucu İstemcisi (Server Components için)

```typescript
// src/lib/supabase/server.ts
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import type { Database } from './database.types' // İsteğe bağlı

export function createSupabaseServerClient() {
  const cookieStore = cookies()
  return createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value
        },
        set(name: string, value: string, options) {
          try {
            cookieStore.set({ name, value, ...options })
          } catch (error) {
            // Sunucu eylemleri/route handler'lar için cookie ayarlama işlemi
          }
        },
        remove(name: string, options) {
          try {
            cookieStore.set({ name, value: '', ...options })
          } catch (error) {
            // Sunucu eylemleri/route handler'lar için cookie silme işlemi
          }
        },
      },
    }
  )
}

// Kullanıcı bilgilerini almak için yardımcı fonksiyon
export async function getUser() {
  const supabase = createSupabaseServerClient()
  try {
    const { data: { user } } = await supabase.auth.getUser()
    return user
  } catch (error) {
    console.error('Error getting user:', error)
    return null
  }
}
```

### Middleware İstemcisi

```typescript
// src/lib/supabase/middleware.ts
import { createServerClient } from '@supabase/ssr'
import type { NextRequest, NextResponse } from 'next/server'

export async function createSupabaseMiddlewareClient(
  req: NextRequest,
  res: NextResponse
) {
  let response = NextResponse.next({ request: { headers: req.headers } })

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return req.cookies.get(name)?.value
        },
        set(name: string, value: string, options) {
          req.cookies.set({
            name,
            value,
            ...options,
          })
          // Middleware'de cookie'leri response'a ayarlama
          // Yeni string value'yu response header'larına ayarla
        },
        remove(name: string, options) {
          req.cookies.set({
            name,
            value: '',
            ...options,
          })
          // Middleware'de cookie'leri silme
        },
      },
    }
  )

  return { supabase, response }
}
```

## Middleware Yapılandırması

```typescript
// src/middleware.ts
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { createSupabaseMiddlewareClient } from '@/lib/supabase/middleware'

export async function middleware(req: NextRequest) {
  const { supabase, response } = await createSupabaseMiddlewareClient(req, NextResponse.next())
  
  await supabase.auth.getSession()
  
  const { pathname } = req.nextUrl

  // Korumalı rotalar
  const protectedRoutes = ['/dashboard', '/profile', '/settings']
  // Public rotalar
  const publicRoutes = ['/auth/login', '/auth/signup', '/auth/forgot-password', '/']
  const isAuthRoute = pathname.startsWith('/auth')
  
  const { data: { user } } = await supabase.auth.getUser()

  if (protectedRoutes.some(route => pathname.startsWith(route)) && !session) {
    // Kullanıcı korumalı sayfaya erişmeye çalışıyor ama giriş yapmamış
    const loginUrl = new URL('/auth/login', req.url)
    loginUrl.searchParams.set('redirectedFrom', pathname)
    return NextResponse.redirect(loginUrl)
  }

  if (session && isAuthRoute) {
    // Kullanıcı giriş yapmış ve auth sayfalarına erişmeye çalışıyor
    return NextResponse.redirect(new URL('/dashboard', req.url))
  }
  
  return response
}

export const config = {
  matcher: [
    // API rotaları, static dosyalar vs. dışındaki tüm yollar
    '/((?!api|_next/static|_next/image|favicon.ico|.*\\.[^.]+$).*)',
  ],
}
```

## Auth Callback Handler

Kimlik doğrulama sonrası yönlendirmeler için bir callback handler oluşturun:

```typescript
// src/app/auth/callback/route.ts
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { NextResponse, type NextRequest } from 'next/server'

export async function GET(req: NextRequest) {
  const requestUrl = new URL(req.url)
  const code = requestUrl.searchParams.get('code')
  
  if (code) {
    const cookieStore = cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    await supabase.auth.exchangeCodeForSession(code)
  }

  // Kullanıcıyı ana sayfaya veya dashboard'a yönlendirin
  return NextResponse.redirect(new URL('/dashboard', req.url))
}
```

## Supabase Ayarları

Supabase Dashboard'da şunları yapılandırdığınızdan emin olun:

1. **Site URL**: Uygulamanızın temel URL'si
2. **Redirect URL'leri**: Auth callback handler için URL'leri ekleyin (örn: `http://localhost:3000/auth/callback`)
3. **Email Auth**: E-posta onayı, şifre sıfırlama gibi ayarları yapılandırın

Bu temel kurulum adımları, Next.js ve Supabase ile kimlik doğrulama sisteminin temellerini atar.

"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { toast } from "sonner"
import { useAuth } from "@/components/auth/auth-provider"
import { ArrowDown, ArrowUp, Weight, Ruler } from "lucide-react"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { 
  getProgressRecords, 
  getBodyMeasurements, 
  getWorkoutProgress, 
  addWeightRecord, 
  addBodyMeasurement, 
  addWorkoutProgress 
} from "@/app/actions/progress-actions"

export function ProgressTracking() {
  const { authUser } = useAuth()
  const [isLoading, setIsLoading] = useState(true)
  const [progressData, setProgressData] = useState<any[]>([])
  const [weightData, setWeightData] = useState<any[]>([])
  const [bodyMeasurements, setBodyMeasurements] = useState<any[]>([])
  const [workoutData, setWorkoutData] = useState<any[]>([])
  const [isAddProgressDialogOpen, setIsAddProgressDialogOpen] = useState(false)
  const [newProgressType, setNewProgressType] = useState("weight")
  const [newProgressValue, setNewProgressValue] = useState("")
  const [newProgressDate, setNewProgressDate] = useState(new Date().toISOString().split("T")[0])
  const [newMeasurementType, setNewMeasurementType] = useState("chest")
  const [newWorkoutType, setNewWorkoutType] = useState("")
  const [newWorkoutValue, setNewWorkoutValue] = useState("")
  const [newWorkoutUnit, setNewWorkoutUnit] = useState("kg")
  const [newWorkoutReps, setNewWorkoutReps] = useState("")
  const [newWorkoutSets, setNewWorkoutSets] = useState("")
  const [newProgressNotes, setNewProgressNotes] = useState("")

  useEffect(() => {
    const fetchData = async () => {
      if (!authUser) return

      setIsLoading(true)
      try {
        // Server Actions kullanarak verileri getir
        const [weightResponse, measurementsResponse, workoutsResponse] = await Promise.all([
          getProgressRecords(authUser.id),
          getBodyMeasurements(authUser.id),
          getWorkoutProgress(authUser.id)
        ]);

        // Kilo verileri
        if (weightResponse.success && weightResponse.data) {
          const weightRecords = weightResponse.data.filter((record: any) => record.weight_kg);
          setWeightData(weightRecords.map((record: any) => ({
            id: record.id,
            date: record.record_date,
            value: record.weight_kg,
            notes: record.notes
          })));
        }

        // Vücut ölçüleri
        if (measurementsResponse.success && measurementsResponse.data) {
          setBodyMeasurements(measurementsResponse.data.map((measurement: any) => ({
            id: measurement.id,
            date: measurement.record_date,
            type: measurement.measurement_type,
            value: measurement.value_cm
          })));
        }

        // Egzersiz verileri
        if (workoutsResponse.success && workoutsResponse.data) {
          setWorkoutData(workoutsResponse.data.map((workout: any) => ({
            id: workout.id,
            date: workout.record_date,
            type: workout.exercise_name,
            value: workout.weight,
            unit: workout.weight ? 'kg' : '',
            reps: workout.reps,
            sets: workout.sets,
            notes: workout.notes
          })));
        }

        // Tüm ilerleme verilerini birleştir
        const allProgressData = [
          ...(weightResponse.success && weightResponse.data ? weightResponse.data : []),
          ...(measurementsResponse.success && measurementsResponse.data ? measurementsResponse.data : []),
          ...(workoutsResponse.success && workoutsResponse.data ? workoutsResponse.data : [])
        ];

        setProgressData(allProgressData);
      } catch (error) {
        console.error("Error fetching progress data:", error)
        toast.error("İlerleme verileri yüklenirken bir hata oluştu.")
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [authUser])

  const handleAddProgress = async () => {
    if (!authUser || !newProgressValue || !newProgressDate) return

    try {
      // Server Action kullanarak ilerleme ekle
      const formData = new FormData();
      formData.append("userId", authUser.id);
      formData.append("recordDate", newProgressDate);
      
      let response;
      
      if (newProgressType === "weight") {
        formData.append("weight", newProgressValue);
        formData.append("notes", newProgressNotes || "");
        response = await addWeightRecord(formData);
      } else if (newProgressType === "measurement") {
        formData.append("measurementType", newMeasurementType);
        formData.append("value", newProgressValue);
        response = await addBodyMeasurement(formData);
      } else if (newProgressType === "workout") {
        formData.append("exerciseName", newWorkoutType);
        formData.append("weight", newWorkoutValue);
        formData.append("reps", newWorkoutReps || "0");
        formData.append("sets", newWorkoutSets || "0");
        formData.append("notes", newProgressNotes || "");
        response = await addWorkoutProgress(formData);
      }

      if (response && response.success) {
        toast.success("İlerleme kaydedildi");
        
        // Verileri yeniden yükle
        if (authUser) {
          const [weightResponse, measurementsResponse, workoutsResponse] = await Promise.all([
            getProgressRecords(authUser.id),
            getBodyMeasurements(authUser.id),
            getWorkoutProgress(authUser.id)
          ]);
          
          if (weightResponse.success && weightResponse.data) {
            const weightRecords = weightResponse.data.filter((record: any) => record.weight_kg);
            setWeightData(weightRecords.map((record: any) => ({
              id: record.id,
              date: record.record_date,
              value: record.weight_kg,
              notes: record.notes
            })));
          }
          
          if (measurementsResponse.success && measurementsResponse.data) {
            setBodyMeasurements(measurementsResponse.data.map((measurement: any) => ({
              id: measurement.id,
              date: measurement.record_date,
              type: measurement.measurement_type,
              value: measurement.value_cm
            })));
          }
          
          if (workoutsResponse.success && workoutsResponse.data) {
            setWorkoutData(workoutsResponse.data.map((workout: any) => ({
              id: workout.id,
              date: workout.record_date,
              type: workout.exercise_name,
              value: workout.weight,
              unit: workout.weight ? 'kg' : '',
              reps: workout.reps,
              sets: workout.sets,
              notes: workout.notes
            })));
          }
        }
      } else {
        toast.error(response?.error || "İlerleme kaydedilirken bir hata oluştu");
      }

      // Formları sıfırla
      resetForm();
    } catch (error) {
      console.error("Error adding progress:", error)
      toast.error("İlerleme kaydedilirken bir hata oluştu.")
    } finally {
      setIsAddProgressDialogOpen(false)
    }
  }

  const resetForm = () => {
    setNewProgressValue("")
    setNewProgressDate(new Date().toISOString().split("T")[0])
    setNewProgressNotes("")
    setNewWorkoutReps("")
    setNewWorkoutSets("")
  }

  const formatDate = (dateString: string) => {
    if (!dateString) return ""

    const date = new Date(dateString)
    return date.toLocaleDateString("tr-TR", {
      day: "numeric",
      month: "long",
      year: "numeric",
    })
  }

  const getLatestValue = (data: any[], type: string | null = null) => {
    if (!data || data.length === 0) return null

    let filteredData = data
    if (type) {
      filteredData = data.filter((item) => item.type === type)
    }

    if (filteredData.length === 0) return null

    // Sort by date descending
    const sorted = [...filteredData].sort((a: any, b: any) => new Date(b.date).getTime() - new Date(a.date).getTime())
    return sorted[0]
  }

  const getChange = (data: any[], type: string | null = null) => {
    if (!data || data.length < 2) return null

    let filteredData = data
    if (type) {
      filteredData = data.filter((item) => item.type === type)
    }

    if (filteredData.length < 2) return null

    // Sort by date
    const sorted = [...filteredData].sort((a: any, b: any) => new Date(a.date).getTime() - new Date(b.date).getTime())
    const oldest = sorted[0]
    const latest = sorted[sorted.length - 1]

    const change = Number(latest.value) - Number(oldest.value)
    const percentChange = (change / Number(oldest.value)) * 100

    return {
      value: change,
      percent: percentChange,
    }
  }

  const getMeasurementName = (type: string) => {
    const names = {
      chest: "Göğüs",
      waist: "Bel",
      hips: "Kalça",
      biceps: "Kol",
      thigh: "Bacak",
      calf: "Baldır",
      neck: "Boyun",
      shoulder: "Omuz",
    }
    return names[type as keyof typeof names] || type
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    )
  }

  // Get latest weight and change
  const latestWeight = getLatestValue(weightData)
  const weightChange = getChange(weightData)

  // Group workout data by type
  const workoutsByType = workoutData.reduce((acc: any, workout: any ) => {
    if (!acc[workout.type]) {
      acc[workout.type] = []
    }
    acc[workout.type].push(workout)
    return acc
  }, {})

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">İlerleme Takibi</h2>
        <Button onClick={() => setIsAddProgressDialogOpen(true)}>Yeni İlerleme Ekle</Button>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Mevcut Kilo</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Weight className="mr-2 h-4 w-4 text-muted-foreground" />
                <div className="text-2xl font-bold">{latestWeight ? `${latestWeight.value} kg` : "Veri yok"}</div>
              </div>
              {weightChange && (
                <div
                  className={`flex items-center text-xs ${weightChange.value < 0 ? "text-green-500" : "text-red-500"}`}
                >
                  {weightChange.value < 0 ? (
                    <ArrowDown className="mr-1 h-3 w-3" />
                  ) : (
                    <ArrowUp className="mr-1 h-3 w-3" />
                  )}
                  {Math.abs(weightChange.value).toFixed(1)} kg ({Math.abs(weightChange.percent).toFixed(1)}%)
                </div>
              )}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              {latestWeight ? `Son ölçüm: ${formatDate(latestWeight.date)}` : ""}
            </p>
          </CardContent>
        </Card>

        {["chest", "waist", "hips"].map((measurementType: string) => {
          const latestMeasurement = getLatestValue(bodyMeasurements, measurementType)
          const measurementChange = getChange(bodyMeasurements, measurementType)

          return (
            <Card key={measurementType}>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  {getMeasurementName(measurementType)}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Ruler className="mr-2 h-4 w-4 text-muted-foreground" />
                    <div className="text-2xl font-bold">
                      {latestMeasurement ? `${latestMeasurement.value} cm` : "Veri yok"}
                    </div>
                  </div>
                  {measurementChange && (
                    <div
                      className={`flex items-center text-xs ${measurementChange.value < 0 ? "text-green-500" : "text-red-500"}`}
                    >
                      {measurementChange.value < 0 ? (
                        <ArrowDown className="mr-1 h-3 w-3" />
                      ) : (
                        <ArrowUp className="mr-1 h-3 w-3" />
                      )}
                      {Math.abs(measurementChange.value).toFixed(1)} cm (
                      {Math.abs(measurementChange.percent).toFixed(1)}%)
                    </div>
                  )}
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  {latestMeasurement ? `Son ölçüm: ${formatDate(latestMeasurement.date)}` : ""}
                </p>
              </CardContent>
            </Card>
          )
        })}
      </div>

      <Tabs defaultValue="weight">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="weight">Kilo Takibi</TabsTrigger>
          <TabsTrigger value="measurements">Vücut Ölçüleri</TabsTrigger>
          <TabsTrigger value="workouts">Antrenman İlerlemesi</TabsTrigger>
        </TabsList>

        <TabsContent value="weight" className="space-y-4 pt-4">
          {weightData.length > 0 ? (
            <Card>
              <CardHeader>
                <CardTitle>Kilo Geçmişi</CardTitle>
                <CardDescription>Zaman içindeki kilo değişiminiz</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] relative">
                  {/* In a real implementation, this would be a chart */}
                  <div className="absolute inset-0 flex items-center justify-center">
                    <p className="text-muted-foreground">Kilo grafiği burada görüntülenecek</p>
                  </div>
                </div>
                <div className="mt-4">
                  <div className="rounded-md border">
                    <div className="grid grid-cols-3 gap-4 p-4 font-medium border-b">
                      <div>Tarih</div>
                      <div>Kilo (kg)</div>
                      <div>Değişim</div>
                    </div>
                    {weightData
                      .sort((a: any, b: any) => new Date(b.date).getTime() - new Date(a.date).getTime())
                      .map((entry: any, index: number, array: any[]) => {
                        const prevEntry = index < array.length - 1 ? array[index + 1] : null
                        const change = prevEntry ? Number(entry.value) - Number(prevEntry.value) : 0

                        return (
                          <div key={entry.id} className="grid grid-cols-3 gap-4 p-4 border-b last:border-0">
                            <div>{formatDate(entry.date)}</div>
                            <div>{entry.value}</div>
                            <div
                              className={`flex items-center ${change < 0 ? "text-green-500" : change > 0 ? "text-red-500" : "text-muted-foreground"}`}
                            >
                              {change !== 0 && (
                                <>
                                  {change < 0 ? (
                                    <ArrowDown className="mr-1 h-3 w-3" />
                                  ) : (
                                    <ArrowUp className="mr-1 h-3 w-3" />
                                  )}
                                  {Math.abs(change).toFixed(1)} kg
                                </>
                              )}
                              {change === 0 && "-"}
                            </div>
                          </div>
                        )
                      })}
                  </div>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="text-center py-12">
              <p className="text-muted-foreground">Henüz kilo verisi bulunmuyor.</p>
              <p className="text-sm text-muted-foreground mt-1">İlerlemenizi takip etmek için kilo verisi ekleyin.</p>
            </div>
          )}
        </TabsContent>

        <TabsContent value="measurements" className="space-y-4 pt-4">
          {bodyMeasurements.length > 0 ? (
            <Card>
              <CardHeader>
                <CardTitle>Vücut Ölçüleri</CardTitle>
                <CardDescription>Zaman içindeki vücut ölçüleriniz</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] relative">
                  {/* In a real implementation, this would be a chart */}
                  <div className="absolute inset-0 flex items-center justify-center">
                    <p className="text-muted-foreground">Vücut ölçüleri grafiği burada görüntülenecek</p>
                  </div>
                </div>
                <div className="mt-4">
                  <div className="rounded-md border">
                    <div className="grid grid-cols-4 gap-4 p-4 font-medium border-b">
                      <div>Tarih</div>
                      <div>Ölçü Tipi</div>
                      <div>Değer (cm)</div>
                      <div>Değişim</div>
                    </div>
                    {bodyMeasurements
                      .sort((a: any, b: any) => {
                        // Sort by date (descending) and then by type
                        const dateCompare = new Date(b.date).getTime() - new Date(a.date).getTime()
                        if (dateCompare !== 0) return dateCompare
                        return a.type.localeCompare(b.type)
                      })
                      .map((entry: any, index: number, array: any[]) => {
                        // Find previous entry of the same type
                        const prevEntry = array.find(
                          (e: any) => e.type === entry.type && new Date(e.date).getTime() < new Date(entry.date).getTime()
                        )
                        const change = prevEntry ? Number(entry.value) - Number(prevEntry.value) : 0

                        return (
                          <div key={entry.id} className="grid grid-cols-4 gap-4 p-4 border-b last:border-0">
                            <div>{formatDate(entry.date)}</div>
                            <div>{getMeasurementName(entry.type)}</div>
                            <div>{entry.value}</div>
                            <div
                              className={`flex items-center ${change < 0 ? "text-green-500" : change > 0 ? "text-red-500" : "text-muted-foreground"}`}
                            >
                              {change !== 0 && (
                                <>
                                  {change < 0 ? (
                                    <ArrowDown className="mr-1 h-3 w-3" />
                                  ) : (
                                    <ArrowUp className="mr-1 h-3 w-3" />
                                  )}
                                  {Math.abs(change).toFixed(1)} cm
                                </>
                              )}
                              {change === 0 && "-"}
                            </div>
                          </div>
                        )
                      })}
                  </div>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="text-center py-12">
              <p className="text-muted-foreground">Henüz vücut ölçüsü verisi bulunmuyor.</p>
              <p className="text-sm text-muted-foreground mt-1">
                İlerlemenizi takip etmek için vücut ölçüsü verisi ekleyin.
              </p>
            </div>
          )}
        </TabsContent>

        <TabsContent value="workouts" className="space-y-4 pt-4">
          {Object.keys(workoutsByType).length > 0 ? (
            <div className="space-y-6">
              {Object.entries(workoutsByType).map(([type, entriesArray]: [string, any]) => {
                // Sort entries by date
                const entries = entriesArray as any[];
                const sortedEntries = [...entries].sort(
                  (a: any, b: any) => new Date(a.date).getTime() - new Date(b.date).getTime()
                );
                const firstEntry = sortedEntries[0]
                const lastEntry = sortedEntries[sortedEntries.length - 1]
                const change = Number(lastEntry.value) - Number(firstEntry.value)
                const percentChange = (change / Number(firstEntry.value)) * 100

                return (
                  <Card key={type}>
                    <CardHeader>
                      <div className="flex justify-between items-start">
                        <div>
                          <CardTitle>{type}</CardTitle>
                          <CardDescription>İlerleme geçmişi</CardDescription>
                        </div>
                        <div className={`flex items-center text-sm ${change > 0 ? "text-green-500" : "text-red-500"}`}>
                          {change > 0 ? <ArrowUp className="mr-1 h-4 w-4" /> : <ArrowDown className="mr-1 h-4 w-4" />}
                          {Math.abs(Number(change))} {lastEntry.unit} ({Math.abs(Number(percentChange)).toFixed(1)}%)
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="h-[200px] relative">
                        {/* In a real implementation, this would be a chart */}
                        <div className="absolute inset-0 flex items-center justify-center">
                          <p className="text-muted-foreground">Antrenman ilerleme grafiği burada görüntülenecek</p>
                        </div>
                      </div>
                      <div className="mt-4">
                        <div className="rounded-md border">
                          <div className="grid grid-cols-3 gap-4 p-4 font-medium border-b">
                            <div>Tarih</div>
                            <div>Değer ({lastEntry.unit})</div>
                            <div>Değişim</div>
                          </div>
                          {sortedEntries
                            .sort((a: any, b: any) => new Date(b.date).getTime() - new Date(a.date).getTime())
                            .map((entry: any, index: number, array: any[]) => {
                              const prevEntry = index < array.length - 1 ? array[index + 1] : null
                              const change = prevEntry ? Number(entry.value) - Number(prevEntry.value) : 0

                              return (
                                <div key={entry.id} className="grid grid-cols-3 gap-4 p-4 border-b last:border-0">
                                  <div>{formatDate(entry.date)}</div>
                                  <div>
                                    {entry.value} {entry.unit}
                                  </div>
                                  <div
                                    className={`flex items-center ${change > 0 ? "text-green-500" : change < 0 ? "text-red-500" : "text-muted-foreground"}`}
                                  >
                                    {change !== 0 && (
                                      <>
                                        {change > 0 ? (
                                          <ArrowUp className="mr-1 h-3 w-3" />
                                        ) : (
                                          <ArrowDown className="mr-1 h-3 w-3" />
                                        )}
                                        {Math.abs(Number(change))} {entry.unit}
                                      </>
                                    )}
                                    {change === 0 && "-"}
                                  </div>
                                </div>
                              )
                            })}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          ) : (
            <div className="text-center py-12">
              <p className="text-muted-foreground">Henüz antrenman verisi bulunmuyor.</p>
              <p className="text-sm text-muted-foreground mt-1">
                İlerlemenizi takip etmek için antrenman verisi ekleyin.
              </p>
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* Add Progress Dialog */}
      <Dialog open={isAddProgressDialogOpen} onOpenChange={setIsAddProgressDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Yeni İlerleme Ekle</DialogTitle>
            <DialogDescription>İlerlemenizi takip etmek için yeni bir veri ekleyin.</DialogDescription>
          </DialogHeader>
          <div className="py-4 space-y-4">
            <div className="space-y-2">
              <Label htmlFor="progress-type">İlerleme Tipi</Label>
              <Select value={newProgressType} onValueChange={setNewProgressType}>
                <SelectTrigger id="progress-type">
                  <SelectValue placeholder="İlerleme tipi seçin" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="weight">Kilo</SelectItem>
                  <SelectItem value="measurement">Vücut Ölçüsü</SelectItem>
                  <SelectItem value="workout">Antrenman</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {newProgressType === "measurement" && (
              <div className="space-y-2">
                <Label htmlFor="measurement-type">Ölçü Tipi</Label>
                <Select value={newMeasurementType} onValueChange={setNewMeasurementType}>
                  <SelectTrigger id="measurement-type">
                    <SelectValue placeholder="Ölçü tipi seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="chest">Göğüs</SelectItem>
                    <SelectItem value="waist">Bel</SelectItem>
                    <SelectItem value="hips">Kalça</SelectItem>
                    <SelectItem value="biceps">Kol</SelectItem>
                    <SelectItem value="thigh">Bacak</SelectItem>
                    <SelectItem value="calf">Baldır</SelectItem>
                    <SelectItem value="neck">Boyun</SelectItem>
                    <SelectItem value="shoulder">Omuz</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}

            {newProgressType === "workout" && (
              <div className="space-y-2">
                <Label htmlFor="workout-type">Egzersiz</Label>
                <Input
                  id="workout-type"
                  placeholder="Örn: Bench Press, Squat, Deadlift"
                  value={newWorkoutType}
                  onChange={(e) => setNewWorkoutType(e.target.value)}
                />
              </div>
            )}

            <div className="space-y-2">
              <Label htmlFor="progress-value">
                {newProgressType === "weight" ? "Kilo (kg)" : newProgressType === "measurement" ? "Ölçü (cm)" : "Değer"}
              </Label>
              <div className="flex gap-2">
                <Input
                  id="progress-value"
                  type="number"
                  step="0.1"
                  placeholder="Değer girin"
                  value={newProgressValue}
                  onChange={(e) => setNewProgressValue(e.target.value)}
                  className="flex-1"
                />
                {newProgressType === "workout" && (
                  <Select value={newWorkoutUnit} onValueChange={setNewWorkoutUnit}>
                    <SelectTrigger className="w-24">
                      <SelectValue placeholder="Birim" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="kg">kg</SelectItem>
                      <SelectItem value="lbs">lbs</SelectItem>
                      <SelectItem value="reps">tekrar</SelectItem>
                      <SelectItem value="min">dakika</SelectItem>
                      <SelectItem value="km">km</SelectItem>
                    </SelectContent>
                  </Select>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="progress-date">Tarih</Label>
              <Input
                id="progress-date"
                type="date"
                value={newProgressDate}
                onChange={(e) => setNewProgressDate(e.target.value)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddProgressDialogOpen(false)}>
              İptal
            </Button>
            <Button onClick={handleAddProgress}>Kaydet</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

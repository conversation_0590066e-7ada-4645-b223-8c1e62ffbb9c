"use server"

import { createAction, validateFormData } from "@/lib/actions/core"
import { ApiResponse } from "@/lib/actions/types"
import { z } from "zod"
import type { Tables } from "@/lib/supabase/types"

// Validasyon şemaları
const weightRecordSchema = z.object({
  userId: z.string().uuid("Geçerli bir kullanıcı ID'si gereklidir"),
  weight: z.coerce.number().positive("Kilo değeri pozitif olmalıdır"),
  recordDate: z.string().min(1, "Kayıt tarihi gereklidir"),
  notes: z.string().optional(),
})

const bodyMeasurementSchema = z.object({
  userId: z.string().uuid("Geçerli bir kullanıcı ID'si gereklidir"),
  measurementType: z.string().min(1, "Ölçüm türü gereklidir"),
  value: z.coerce.number().positive("Ölçüm değeri pozitif olmalıdır"),
  recordDate: z.string().min(1, "Kayıt tarihi gereklidir"),
})

const workoutProgressSchema = z.object({
  userId: z.string().uuid("Geçerli bir kullanıcı ID'si gereklidir"),
  exerciseName: z.string().min(1, "Egzersiz adı gereklidir"),
  weight: z.coerce.number().nullable().optional(),
  reps: z.coerce.number().int().nullable().optional(),
  sets: z.coerce.number().int().nullable().optional(),
  duration: z.coerce.number().int().nullable().optional(),
  distance: z.coerce.number().nullable().optional(),
  recordDate: z.string().min(1, "Kayıt tarihi gereklidir"),
  notes: z.string().optional(),
})

/**
 * Kullanıcının ilerleme (kilo) kayıtlarını getirir
 * @param userId Kullanıcı ID'si
 * @returns İlerleme kayıtları veya hata
 */
export async function getProgressRecords(userId: string): Promise<ApiResponse<any[]>> {
  if (!userId) {
    return { success: false, error: "Kullanıcı ID'si gereklidir." }
  }
  
  return await createAction(async (_, supabase, authUserId) => {
    // Yetki kontrolü - Kullanıcı sadece kendi verilerine erişebilir
    if (userId !== authUserId) {
      throw new Error("Bu verilere erişim yetkiniz bulunmuyor.")
    }
    
    const { data, error } = await supabase
      .from("progress_records")
      .select("*")
      .eq("user_id", userId)
      .order("record_date", { ascending: false })
    
    if (error) {
      throw new Error(`İlerleme kayıtları getirilirken hata: ${error.message}`)
    }
    
    return data || []
  }, { requireAuth: true })
}

/**
 * Kilo kaydı ekler
 */
export async function addWeightRecord(formData: FormData): Promise<ApiResponse> {
  const { data, error } = await validateFormData(formData, weightRecordSchema)
  
  if (error || !data) {
    return { success: false, error: error || "Geçersiz form verisi" }
  }
  
  const { userId, weight, recordDate, notes } = data
  
  return await createAction(async (_, supabase) => {
    const { data: record, error } = await supabase
      .from("progress_records")
      .insert({
        user_id: userId,
        weight_kg: weight,
        record_date: recordDate,
        notes: notes || null,
        created_at: new Date().toISOString(),
      })
      .select()
      .single()

    if (error) {
      throw new Error(`Kilo kaydı eklenirken hata: ${error.message}`)
    }

    return record
  }, { revalidatePaths: ["/dashboard/member/progress"] })
}

/**
 * İlerleme kaydını günceller
 * @param recordId Kayıt ID'si
 * @param formData Form verileri
 * @returns Güncellenmiş kayıt veya hata
 */
export async function updateProgressRecord(recordId: string, formData: FormData): Promise<ApiResponse> {
  const { data, error } = await validateFormData(formData, weightRecordSchema)
  
  if (error || !data) {
    return { success: false, error: error || "Geçersiz form verisi" }
  }
  
  const { userId, weight, recordDate, notes } = data
  
  return await createAction(async (_, supabase, authUserId) => {
    // Yetki kontrolü - Kaydın sahibi olup olmadığını kontrol et
    const { data: existingRecord } = await supabase
      .from("progress_records")
      .select("user_id")
      .eq("id", recordId)
      .single()
      
    if (!existingRecord) {
      throw new Error("Kayıt bulunamadı.")
    }
    
    if (existingRecord.user_id !== authUserId) {
      throw new Error("Bu kaydı düzenlemek için yetkiniz bulunmuyor.")
    }
    
    // Kaydı güncelle
    const { data: updatedRecord, error: updateError } = await supabase
      .from("progress_records")
      .update({
        weight_kg: weight,
        record_date: recordDate,
        notes: notes || null,
        updated_at: new Date().toISOString()
      })
      .eq("id", recordId)
      .select()
      .single()
    
    if (updateError) {
      throw new Error(`Kayıt güncellenirken hata: ${updateError.message}`)
    }
    
    return updatedRecord
  }, { requireAuth: true, revalidatePaths: ["/dashboard/member/progress"] })
}

/**
 * Vücut ölçüsü kaydı ekler
 */
export async function addBodyMeasurement(formData: FormData): Promise<ApiResponse> {
  const { data, error } = await validateFormData(formData, bodyMeasurementSchema)
  
  if (error || !data) {
    return { success: false, error: error || "Geçersiz form verisi" }
  }
  
  const { userId, measurementType, value, recordDate } = data
  
  return await createAction(async (_, supabase) => {
    const { data: measurement, error } = await supabase
      .from("body_measurements")
      .insert({
        user_id: userId,
        measurement_type: measurementType,
        value_cm: value,
        record_date: recordDate,
        created_at: new Date().toISOString(),
      })
      .select()
      .single()

    if (error) {
      throw new Error(`Vücut ölçüsü eklenirken hata: ${error.message}`)
    }

    return measurement
  }, { revalidatePaths: ["/dashboard/member/progress"] })
}

/**
 * Vücut ölçülerini getirir
 * @param userId Kullanıcı ID'si
 * @param measurementType (Opsiyonel) Ölçüm tipi
 * @returns Vücut ölçüleri veya hata
 */
export async function getBodyMeasurements(userId: string, measurementType?: string): Promise<ApiResponse<any[]>> {
  if (!userId) {
    return { success: false, error: "Kullanıcı ID'si gereklidir." }
  }
  
  return await createAction(async (_, supabase, authUserId) => {
    // Yetki kontrolü - Kullanıcı sadece kendi verilerine erişebilir
    if (userId !== authUserId) {
      throw new Error("Bu verilere erişim yetkiniz bulunmuyor.")
    }
    
    let query = supabase
      .from("body_measurements")
      .select("*")
      .eq("user_id", userId)
      .order("record_date", { ascending: false })
    
    if (measurementType) {
      query = query.eq("measurement_type", measurementType)
    }
    
    const { data, error } = await query
    
    if (error) {
      throw new Error(`Vücut ölçüleri getirilirken hata: ${error.message}`)
    }
    
    return data || []
  }, { requireAuth: true })
}

/**
 * Vücut ölçüsü kaydını günceller
 * @param measurementId Ölçüm ID'si
 * @param formData Form verileri
 * @returns Güncellenmiş ölçüm veya hata
 */
export async function updateBodyMeasurement(measurementId: string, formData: FormData): Promise<ApiResponse> {
  const { data, error } = await validateFormData(formData, bodyMeasurementSchema)
  
  if (error || !data) {
    return { success: false, error: error || "Geçersiz form verisi" }
  }
  
  const { userId, measurementType, value, recordDate } = data
  
  return await createAction(async (_, supabase, authUserId) => {
    // Yetki kontrolü - Kaydın sahibi olup olmadığını kontrol et
    const { data: existingMeasurement } = await supabase
      .from("body_measurements")
      .select("user_id")
      .eq("id", measurementId)
      .single()
      
    if (!existingMeasurement) {
      throw new Error("Kayıt bulunamadı.")
    }
    
    if (existingMeasurement.user_id !== authUserId) {
      throw new Error("Bu kaydı düzenlemek için yetkiniz bulunmuyor.")
    }
    
    // Kaydı güncelle
    const { data: updatedMeasurement, error: updateError } = await supabase
      .from("body_measurements")
      .update({
        measurement_type: measurementType,
        value_cm: value,
        record_date: recordDate,
        updated_at: new Date().toISOString()
      })
      .eq("id", measurementId)
      .select()
      .single()
    
    if (updateError) {
      throw new Error(`Kayıt güncellenirken hata: ${updateError.message}`)
    }
    
    return updatedMeasurement
  }, { requireAuth: true, revalidatePaths: ["/dashboard/member/progress"] })
}

/**
 * Antrenman ilerleme kaydı ekler
 */
export async function addWorkoutProgress(formData: FormData): Promise<ApiResponse> {
  const { data, error } = await validateFormData(formData, workoutProgressSchema)
  
  if (error || !data) {
    return { success: false, error: error || "Geçersiz form verisi" }
  }
  
  const { userId, exerciseName, weight, reps, sets, duration, distance, recordDate, notes } = data
  
  return await createAction(async (_, supabase) => {
    const { data: workout, error } = await supabase
      .from("workout_progress")
      .insert({
        user_id: userId,
        exercise_name: exerciseName,
        weight_kg: weight,
        reps,
        sets,
        duration_minutes: duration,
        distance_km: distance,
        record_date: recordDate,
        notes: notes || null,
        created_at: new Date().toISOString(),
      })
      .select()
      .single()

    if (error) {
      throw new Error(`Antrenman kaydı eklenirken hata: ${error.message}`)
    }

    return workout
  }, { revalidatePaths: ["/dashboard/member/progress"] })
}

/**
 * Antrenman ilerlemelerini getirir
 * @param userId Kullanıcı ID'si
 * @param exerciseName (Opsiyonel) Egzersiz adına göre filtreleme
 * @returns Antrenman ilerlemeleri veya hata
 */
export async function getWorkoutProgress(userId: string, exerciseName?: string): Promise<ApiResponse<any[]>> {
  if (!userId) {
    return { success: false, error: "Kullanıcı ID'si gereklidir." }
  }
  
  return await createAction(async (_, supabase, authUserId) => {
    // Yetki kontrolü - Kullanıcı sadece kendi verilerine erişebilir
    if (userId !== authUserId) {
      throw new Error("Bu verilere erişim yetkiniz bulunmuyor.")
    }
    
    let query = supabase
      .from("workout_progress")
      .select("*")
      .eq("user_id", userId)
      .order("record_date", { ascending: false })
    
    if (exerciseName) {
      query = query.eq("exercise_name", exerciseName)
    }
    
    const { data, error } = await query
    
    if (error) {
      throw new Error(`Antrenman ilerlemeleri getirilirken hata: ${error.message}`)
    }
    
    return data || []
  }, { requireAuth: true })
}

/**
 * Antrenman ilerleme kaydını günceller
 * @param workoutId Antrenman kaydı ID'si
 * @param formData Form verileri
 * @returns Güncellenmiş kayıt veya hata
 */
export async function updateWorkoutProgress(workoutId: string, formData: FormData): Promise<ApiResponse> {
  const { data, error } = await validateFormData(formData, workoutProgressSchema)
  
  if (error || !data) {
    return { success: false, error: error || "Geçersiz form verisi" }
  }
  
  const { userId, exerciseName, weight, reps, sets, duration, distance, recordDate, notes } = data
  
  return await createAction(async (_, supabase, authUserId) => {
    // Yetki kontrolü - Kaydın sahibi olup olmadığını kontrol et
    const { data: existingWorkout } = await supabase
      .from("workout_progress")
      .select("user_id")
      .eq("id", workoutId)
      .single()
      
    if (!existingWorkout) {
      throw new Error("Kayıt bulunamadı.")
    }
    
    if (existingWorkout.user_id !== authUserId) {
      throw new Error("Bu kaydı düzenlemek için yetkiniz bulunmuyor.")
    }
    
    // Kaydı güncelle
    const { data: updatedWorkout, error: updateError } = await supabase
      .from("workout_progress")
      .update({
        exercise_name: exerciseName,
        weight_kg: weight,
        reps,
        sets,
        duration_minutes: duration,
        distance_km: distance,
        record_date: recordDate,
        notes: notes || null,
        updated_at: new Date().toISOString()
      })
      .eq("id", workoutId)
      .select()
      .single()
    
    if (updateError) {
      throw new Error(`Kayıt güncellenirken hata: ${updateError.message}`)
    }
    
    return updatedWorkout
  }, { requireAuth: true, revalidatePaths: ["/dashboard/member/progress"] })
}

/**
 * İlerleme kaydını siler
 */
export async function deleteProgressRecord(
  recordId: string, 
  recordType: "weight" | "measurement" | "workout"
): Promise<ApiResponse> {
  if (!recordId || !recordType) {
    return { success: false, error: "Kayıt ID'si ve tür gereklidir" }
  }
  
  return await createAction(async (_, supabase) => {
    let tableName: string
    
    // Kayıt türüne göre tablo belirle
    switch (recordType) {
      case "weight":
        tableName = "progress_records"
        break
      case "measurement":
        tableName = "body_measurements"
        break
      case "workout":
        tableName = "workout_progress"
        break
      default:
        throw new Error("Geçersiz kayıt türü")
    }
    
    // Kaydı sil
    const { error } = await supabase
      .from(tableName)
      .delete()
      .eq("id", recordId)
      
    if (error) {
      throw new Error(`Kayıt silinirken hata: ${error.message}`)
    }
    
    return { deleted: true }
  }, { revalidatePaths: ["/dashboard/member/progress"] })
}

/**
 * Kullanıcının egzersizlerini getirir (tekrar edenleri filtreler)
 * @param userId Kullanıcı ID'si
 * @returns Egzersiz isimleri dizisi veya hata
 */
export async function getUserExercises(userId: string): Promise<ApiResponse<any>> {
  if (!userId) {
    return { success: false, error: "Kullanıcı ID'si gereklidir." }
  }
  
  return await createAction(async (_, supabase, authUserId) => {
    // Yetki kontrolü - Kullanıcı sadece kendi verilerine erişebilir
    if (userId !== authUserId) {
      throw new Error("Bu verilere erişim yetkiniz bulunmuyor.")
    }
    
    const { data, error } = await supabase
      .from("workout_progress")
      .select("exercise_name")
      .eq("user_id", userId)
      .order("exercise_name", { ascending: true })
      .limit(100)
    
    if (error) {
      throw new Error(`Kullanıcı egzersizleri getirilirken hata: ${error.message}`)
    }
    
    // Tekrar edenleri kaldır
    const uniqueExercises = [...new Set(data.map((item: { exercise_name: string }) => item.exercise_name))]
    return uniqueExercises
  }, { requireAuth: true })
}

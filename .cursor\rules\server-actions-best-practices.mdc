---
description: 
globs: 
alwaysApply: true
---
# Next.js Server Actions En İyi Pratikler

## Genel Bakış

Server Actions, Next.js 14+ ile birlikte varsayılan olarak kullanılabilen, sunucu tarafında çalışan ve istemci tarafından çağrılabilen fonksiyonlardır. Bu fonksiyonlar, API rotaları oluşturmaya gerek kalmadan veritabanı işlemleri, veri mutasyonları ve kimlik doğrulama gibi işlemleri gerçekleştirmenize olanak tanır.

## Temel Kullanım

### Tanımlama Yöntemleri

1. **Dosya Düzeyinde Tanımlama**:
   ```typescript
   'use server'
   
   // Bu dosyadaki tüm dışa aktarılan fonksiyonlar Server Action olur
   export async function createUser(data: { name: string; email: string }) {
     // Veritabanı işlemleri...
     return user;
   }
   ```

2. **Fonksiyon İçinde Tanımlama**:
   ```typescript
   export default function Page() {
     async function handleForm(formData: FormData) {
       'use server'
       // Sunucu tarafında çalışacak kod...
     }
     
     return <form action={handleForm}>...</form>;
   }
   ```

### Form ile Kullanım

```typescript
// app/actions.ts
'use server'

export async function createInvoice(formData: FormData) {
  const rawFormData = {
    customerId: formData.get('customerId'),
    amount: formData.get('amount'),
    status: formData.get('status'),
  }
  
  // Veri işlemleri ve validasyon...
}

// app/page.tsx
import { createInvoice } from './actions'

export default function Page() {
  return <form action={createInvoice}>...</form>
}
```

## En İyi Pratikler

### 1. Veri Doğrulama

Her zaman istemciden gelen verileri doğrulayın. Zod gibi validasyon kütüphaneleri kullanarak güvenli şema doğrulaması yapın:

```typescript
'use server'

import { z } from 'zod'

const schema = z.object({
  email: z.string({
    invalid_type_error: 'Geçersiz E-posta',
  }).email(),
  // Diğer alanlar...
})

export async function createUser(formData: FormData) {
  const validatedFields = schema.safeParse({
    email: formData.get('email'),
    // Diğer alanlar...
  })
  
  if (!validatedFields.success) {
    return {
      errors: validatedFields.error.flatten().fieldErrors,
    }
  }
  
  // Veri doğrulaması başarılı, işleme devam et
}
```

### 2. Hata Yönetimi

Server Action'lar istemciye hata bilgilerini döndürebilir. Exception fırlatmak yerine, hata durumlarını bir obje olarak döndürün:

```typescript
'use server'

export async function createUser(formData: FormData) {
  try {
    // Veritabanı işlemleri...
  } catch (error) {
    return { 
      success: false, 
      message: 'Kullanıcı oluşturulurken bir hata oluştu.' 
    }
  }
  
  return { success: true }
}
```

### 3. Yönlendirme ve Önbellek Yenileme

İşlem tamamlandıktan sonra önbelleği yenileyin ve gerekirse kullanıcıyı yönlendirin:

```typescript
'use server'

import { redirect } from 'next/navigation'
import { revalidatePath, revalidateTag } from 'next/cache'

export async function createPost(id: string) {
  try {
    // Veritabanı işlemleri...
  } catch (error) {
    return { error: 'Post oluşturulamadı' }
  }
  
  // Belirli bir yolu yeniden doğrulama
  revalidatePath('/posts')
  
  // veya belirli bir etiketle işaretlenmiş verileri yenileme
  revalidateTag('posts')
  
  // Kullanıcıyı yeni oluşturulan post'a yönlendirme
  redirect(`/post/${id}`)
}
```

### 4. Yetkisiz Erişimi Engelleme

Her Server Action'da yetkilendirme kontrolü yapın:

```typescript
'use server'

import { auth } from './lib/auth'
import { forbidden } from 'next/navigation'

export async function updateUserRole(formData: FormData) {
  const session = await auth()
  
  if (!session || session.user.role !== 'admin') {
    // Ya hata döndürün
    return { error: 'Bu işlemi yapmaya yetkiniz yok.' }
    
    // Ya da 403 durumunu tetikleyin
    forbidden()
  }
  
  // Yetkili kullanıcı için işleme devam et
}
```

### 5. Çerez (Cookie) Yönetimi

Server Action'larda çerez yönetimi için Next.js API'lerini kullanın:

```typescript
'use server'

import { cookies } from 'next/headers'

export async function userPreference(theme: string) {
  cookies().set('theme', theme)
  // veya
  cookies().delete('theme')
}
```

### 6. Performans Optimizasyonu

İstemci bileşenlerinde iyimser (optimistic) güncellemeler yapın:

```typescript
'use client'

import { useOptimistic } from 'react'
import { sendMessage } from './actions'

export function ChatForm({ messages }) {
  const [optimisticMessages, addOptimisticMessage] = useOptimistic(
    messages,
    (state, newMessage) => [...state, { message: newMessage }]
  )
  
  const formAction = async (formData) => {
    const message = formData.get('message')
    addOptimisticMessage(message) // Hemen UI'da göster
    await sendMessage(message)    // Sonra sunucuya gönder
  }
  
  return (
    <form action={formAction}>
      <input name="message" />
      <button type="submit">Gönder</button>
    </form>
  )
}
```

### 7. Action Durumu Yönetimi

React'ın `useActionState` hook'unu kullanarak action durumunu yönetin:

```typescript
'use client'

import { useActionState } from 'react'
import { createUser } from '@/app/actions'

export function SignupForm() {
  const [state, formAction, pending] = useActionState(createUser, {
    message: '',
  })
  
  return (
    <form action={formAction}>
      <input name="email" type="email" required />
      {state?.message && <p>{state.message}</p>}
      <button disabled={pending} type="submit">
        {pending ? 'Kaydediliyor...' : 'Kaydol'}
      </button>
    </form>
  )
}
```

### 8. Form Durumu İzleme

Form gönderim durumunu izlemek için `useFormStatus` hook'unu kullanın:

```typescript
'use client'

import { useFormStatus } from 'react-dom'

export function SubmitButton() {
  const { pending } = useFormStatus()
  
  return (
    <button disabled={pending} type="submit">
      {pending ? 'Gönderiliyor...' : 'Gönder'}
    </button>
  )
}
```

### 9. Closure'ları Kullanma

Server Component'lerde, Server Action'lar kapanışlar (closures) kullanabilir:

```typescript
export default async function Page() {
  const publishVersion = await getLatestVersion();

  async function publish() {
    "use server";
    
    // Kapanıştan değer alma
    if (publishVersion !== await getLatestVersion()) {
      throw new Error('Yayın düğmesine bastıktan sonra sürüm değişti');
    }
    
    // İşleme devam et...
  }

  return (
    <form>
      <button formAction={publish}>Yayınla</button>
    </form>
  );
}
```

### 10. Güvenlik Önlemleri

İzin verilen kaynak domain'leri (origins) yapılandırarak CSRF saldırılarını önleyin:

```javascript
// next.config.js
/** @type {import('next').NextConfig} */
module.exports = {
  experimental: {
    serverActions: {
      allowedOrigins: ['ana-domain.com', '*.guvenli-subdomain.com'],
      bodySizeLimit: '2mb', // İsteğe bağlı boyut sınırı (varsayılan: 1mb)
    },
  },
}
```

## Anti-Patterns

1. **Hataları Yutmak**: Hataları sessizce yok saymayın, ya işleyin ya da istemciye raporlayın.
2. **Yetkilendirme Eksikliği**: Her server action'da kimlik doğrulama ve yetkilendirme kontrolü yapın.
3. **Büyük Veri Transferi**: Gereksiz büyük veri objelerini istemci ve sunucu arasında transfer etmekten kaçının.
4. **Gereksiz Durum Güncellemeleri**: Önbellek yenileme işlemlerini sadece gerektiğinde kullanın.
5. **Yetersiz Veri Doğrulama**: Tüm kullanıcı girdilerini her zaman doğrulayın.

## Server Actions Kullanım Senaryoları

- Form gönderimlerini işleme
- Veritabanı işlemleri (CRUD)
- Kimlik doğrulama ve oturum yönetimi
- Dosya yükleme ve işleme
- E-posta gönderme
- Ödeme işlemleri
- API entegrasyonları

## Özet

Server Actions, sunucu tarafı işlemleri doğrudan React bileşenlerinden gerçekleştirmek için güçlü bir mekanizma sunar. Doğru şekilde kullanıldığında, API rotalarına olan ihtiyacı azaltır, kod organizasyonunu iyileştirir ve geliştirici deneyimini artırır. Güvenlik, hata yönetimi ve performans optimizasyonu için yukarıdaki en iyi pratikleri izleyin.


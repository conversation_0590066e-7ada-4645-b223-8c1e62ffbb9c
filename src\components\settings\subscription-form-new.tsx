"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { cn } from "@/lib/utils";
import { createManager } from "@/app/actions/user-actions";
import { createGym } from "@/app/actions/gym-actions";
import { toast } from "sonner";
import {
  AlertCircle,
  CheckCircle,
  Dumbbell,
  CreditCard,
  Check,
  Crown,
  Zap,
  Star,
} from "lucide-react";

interface SubscriptionFormProps {
  userId: string;
  isManager: boolean;
}

type SubscriptionPlan = "monthly" | "quarterly" | "yearly";
type SubscriptionTier = "starter" | "professional" | "enterprise";
type SubscriptionStep = "tier-select" | "plan-select" | "payment" | "gym-setup";

// Paket fiyatları
const PACKAGE_PRICES = {
  starter: { monthly: 199, quarterly: 539, yearly: 1799 },
  professional: { monthly: 299, quarterly: 809, yearly: 2699 },
  enterprise: { monthly: 499, quarterly: 1349, yearly: 4999 },
};

// Paket özellikleri
const PACKAGE_FEATURES = {
  starter: [
    "1 salon",
    "100 üyeye kadar",
    "Temel raporlar",
    "Email destek",
    "Temel paket yönetimi",
  ],
  professional: [
    "3 salona kadar",
    "500 üyeye kadar",
    "Gelişmiş raporlar",
    "Ödeme entegrasyonu",
    "SMS bildirimleri",
    "Öncelikli destek",
  ],
  enterprise: [
    "Sınırsız salon",
    "Sınırsız üye",
    "AI destekli analizler",
    "API erişimi",
    "Özel entegrasyonlar",
    "7/24 telefon desteği",
  ],
};

export function SubscriptionFormNew({
  userId,
  isManager,
}: SubscriptionFormProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [currentStep, setCurrentStep] =
    useState<SubscriptionStep>("tier-select");
  const [selectedTier, setSelectedTier] = useState<SubscriptionTier | null>(
    null
  );
  const [selectedPlan, setSelectedPlan] = useState<SubscriptionPlan | null>(
    null
  );

  // Gym setup form state
  const [gymFormData, setGymFormData] = useState({
    name: "",
    description: "",
    address: "",
    city: "",
    district: "",
    phone: "",
    email: "",
    gym_type: "",
  });

  const handleTierSelect = (tier: SubscriptionTier) => {
    setSelectedTier(tier);
    setCurrentStep("plan-select");
  };

  const handlePlanSelect = (plan: SubscriptionPlan) => {
    setSelectedPlan(plan);
  };

  const handleGymFormChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setGymFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleGymTypeChange = (value: string) => {
    setGymFormData((prev) => ({ ...prev, gym_type: value }));
  };

  const handlePaymentSimulation = async () => {
    if (!selectedTier || !selectedPlan) return;

    setIsLoading(true);
    setError(null);

    // Ödeme simülasyonu
    setTimeout(async () => {
      try {
        const subscription = await createManager(
          userId,
          selectedPlan,
          selectedTier
        );

        if (subscription.error) {
          setError(subscription.error);
          setIsLoading(false);
          return;
        }

        setIsLoading(false);
        setCurrentStep("gym-setup");
        toast.success("Ödeme başarıyla tamamlandı!");
      } catch (error: any) {
        setError(error.message);
        setIsLoading(false);
      }
    }, 1500);
  };

  const handleGymSetupSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      const result = await createGym(gymFormData);

      if (result.error) {
        setError(result.error);
        return;
      }

      setSuccess("Salon başarıyla oluşturuldu!");
      toast.success("Salon kurulumu tamamlandı!");

      setTimeout(() => {
        router.push("/dashboard/manager");
      }, 2000);
    } catch (error: any) {
      setError(error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const getTierIcon = (tier: SubscriptionTier) => {
    switch (tier) {
      case "starter":
        return <Zap className="h-6 w-6" />;
      case "professional":
        return <Star className="h-6 w-6" />;
      case "enterprise":
        return <Crown className="h-6 w-6" />;
    }
  };

  const getTierColor = (tier: SubscriptionTier) => {
    switch (tier) {
      case "starter":
        return "text-blue-600";
      case "professional":
        return "text-green-600";
      case "enterprise":
        return "text-purple-600";
    }
  };

  const getTierBadgeColor = (tier: SubscriptionTier) => {
    switch (tier) {
      case "starter":
        return "bg-blue-100 text-blue-800";
      case "professional":
        return "bg-green-100 text-green-800";
      case "enterprise":
        return "bg-purple-100 text-purple-800";
    }
  };

  const getPrice = () => {
    if (!selectedTier || !selectedPlan) return 0;
    return PACKAGE_PRICES[selectedTier][selectedPlan];
  };

  const getPlanText = () => {
    switch (selectedPlan) {
      case "monthly":
        return "Aylık Plan";
      case "quarterly":
        return "3 Aylık Plan";
      case "yearly":
        return "Yıllık Plan";
      default:
        return "";
    }
  };

  const getTierText = () => {
    switch (selectedTier) {
      case "starter":
        return "Starter";
      case "professional":
        return "Professional";
      case "enterprise":
        return "Enterprise";
      default:
        return "";
    }
  };

  // Tier seçim ekranı
  if (currentStep === "tier-select") {
    return (
      <div className="space-y-6">
        <Alert variant="default">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Size en uygun paketi seçin. İhtiyaçlarınıza göre istediğiniz zaman
            paket değiştirebilirsiniz.
          </AlertDescription>
        </Alert>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Starter Tier */}
          <Card
            className={cn(
              "cursor-pointer hover:border-primary transition-all duration-200 hover:shadow-lg",
              selectedTier === "starter"
                ? "border-2 border-blue-500 shadow-lg"
                : ""
            )}
            onClick={() => handleTierSelect("starter")}
          >
            <CardHeader className="text-center">
              <div className={cn("mx-auto mb-2", getTierColor("starter"))}>
                {getTierIcon("starter")}
              </div>
              <CardTitle className="text-xl">Starter</CardTitle>
              <CardDescription>Yeni başlayanlar için</CardDescription>
              <div
                className={cn(
                  "inline-block px-3 py-1 rounded-full text-xs font-medium",
                  getTierBadgeColor("starter")
                )}
              >
                En Popüler
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-center mb-4">
                <div className="text-2xl font-bold">₺199</div>
                <div className="text-sm text-muted-foreground">
                  aylık başlangıç
                </div>
              </div>
              <ul className="space-y-2">
                {PACKAGE_FEATURES.starter.map((feature, index) => (
                  <li key={index} className="flex items-center text-sm">
                    <Check className="h-4 w-4 mr-2 text-green-500 flex-shrink-0" />
                    <span>{feature}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>

          {/* Professional Tier */}
          <Card
            className={cn(
              "cursor-pointer hover:border-primary transition-all duration-200 hover:shadow-lg relative",
              selectedTier === "professional"
                ? "border-2 border-green-500 shadow-lg"
                : ""
            )}
            onClick={() => handleTierSelect("professional")}
          >
            <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
              <span className="bg-green-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                Önerilen
              </span>
            </div>
            <CardHeader className="text-center">
              <div className={cn("mx-auto mb-2", getTierColor("professional"))}>
                {getTierIcon("professional")}
              </div>
              <CardTitle className="text-xl">Professional</CardTitle>
              <CardDescription>Büyüyen salonlar için</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center mb-4">
                <div className="text-2xl font-bold">₺299</div>
                <div className="text-sm text-muted-foreground">
                  aylık başlangıç
                </div>
              </div>
              <ul className="space-y-2">
                {PACKAGE_FEATURES.professional.map((feature, index) => (
                  <li key={index} className="flex items-center text-sm">
                    <Check className="h-4 w-4 mr-2 text-green-500 flex-shrink-0" />
                    <span>{feature}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>

          {/* Enterprise Tier */}
          <Card
            className={cn(
              "cursor-pointer hover:border-primary transition-all duration-200 hover:shadow-lg",
              selectedTier === "enterprise"
                ? "border-2 border-purple-500 shadow-lg"
                : ""
            )}
            onClick={() => handleTierSelect("enterprise")}
          >
            <CardHeader className="text-center">
              <div className={cn("mx-auto mb-2", getTierColor("enterprise"))}>
                {getTierIcon("enterprise")}
              </div>
              <CardTitle className="text-xl">Enterprise</CardTitle>
              <CardDescription>Salon zincirleri için</CardDescription>
              <div
                className={cn(
                  "inline-block px-3 py-1 rounded-full text-xs font-medium",
                  getTierBadgeColor("enterprise")
                )}
              >
                Gelişmiş
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-center mb-4">
                <div className="text-2xl font-bold">₺499</div>
                <div className="text-sm text-muted-foreground">
                  aylık başlangıç
                </div>
              </div>
              <ul className="space-y-2">
                {PACKAGE_FEATURES.enterprise.map((feature, index) => (
                  <li key={index} className="flex items-center text-sm">
                    <Check className="h-4 w-4 mr-2 text-green-500 flex-shrink-0" />
                    <span>{feature}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  // Plan seçim ekranı
  if (currentStep === "plan-select" && selectedTier) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold">Ödeme Planı Seçin</h2>
            <p className="text-muted-foreground">
              {getTierText()} paketiniz için ödeme planını seçin
            </p>
          </div>
          <Button
            variant="outline"
            onClick={() => setCurrentStep("tier-select")}
          >
            Geri
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Aylık Plan */}
          <Card
            className={cn(
              "cursor-pointer hover:border-primary transition-colors",
              selectedPlan === "monthly" ? "border-2 border-primary" : ""
            )}
            onClick={() => handlePlanSelect("monthly")}
          >
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Aylık</CardTitle>
              <CardDescription>Aylık ödeme ile esneklik</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold mb-2">
                ₺{PACKAGE_PRICES[selectedTier].monthly}
                <span className="text-sm font-normal text-muted-foreground">
                  /ay
                </span>
              </div>
            </CardContent>
            <CardFooter>
              {selectedPlan === "monthly" && (
                <div className="w-full bg-primary/10 text-primary text-center py-1 rounded-md text-sm">
                  Seçildi
                </div>
              )}
            </CardFooter>
          </Card>

          {/* 3 Aylık Plan */}
          <Card
            className={cn(
              "cursor-pointer hover:border-primary transition-colors",
              selectedPlan === "quarterly" ? "border-2 border-primary" : ""
            )}
            onClick={() => handlePlanSelect("quarterly")}
          >
            <CardHeader className="pb-2">
              <div className="flex justify-between items-center">
                <CardTitle className="text-lg">3 Aylık</CardTitle>
                <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                  %10 İndirim
                </span>
              </div>
              <CardDescription>3 aylık ödeme ile tasarruf</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold mb-2">
                ₺{PACKAGE_PRICES[selectedTier].quarterly}
                <span className="text-sm font-normal text-muted-foreground">
                  /3 ay
                </span>
              </div>
              <div className="text-sm text-muted-foreground">
                ₺{Math.round(PACKAGE_PRICES[selectedTier].quarterly / 3)}/ay
                olarak
              </div>
            </CardContent>
            <CardFooter>
              {selectedPlan === "quarterly" && (
                <div className="w-full bg-primary/10 text-primary text-center py-1 rounded-md text-sm">
                  Seçildi
                </div>
              )}
            </CardFooter>
          </Card>

          {/* Yıllık Plan */}
          <Card
            className={cn(
              "cursor-pointer hover:border-primary transition-colors",
              selectedPlan === "yearly" ? "border-2 border-primary" : ""
            )}
            onClick={() => handlePlanSelect("yearly")}
          >
            <CardHeader className="pb-2">
              <div className="flex justify-between items-center">
                <CardTitle className="text-lg">Yıllık</CardTitle>
                <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                  En Ekonomik
                </span>
              </div>
              <CardDescription>
                Yıllık ödeme ile maksimum tasarruf
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold mb-2">
                ₺{PACKAGE_PRICES[selectedTier].yearly}
                <span className="text-sm font-normal text-muted-foreground">
                  /yıl
                </span>
              </div>
              <div className="text-sm text-muted-foreground">
                ₺{Math.round(PACKAGE_PRICES[selectedTier].yearly / 12)}/ay
                olarak
              </div>
            </CardContent>
            <CardFooter>
              {selectedPlan === "yearly" && (
                <div className="w-full bg-primary/10 text-primary text-center py-1 rounded-md text-sm">
                  Seçildi
                </div>
              )}
            </CardFooter>
          </Card>
        </div>

        <div className="flex justify-end">
          <Button
            onClick={() => setCurrentStep("payment")}
            disabled={!selectedPlan}
            size="lg"
          >
            Ödemeye Geç
          </Button>
        </div>
      </div>
    );
  }

  // Ödeme ekranı
  if (currentStep === "payment" && selectedTier && selectedPlan) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <div className="flex items-center space-x-2">
              <CreditCard className="h-5 w-5 text-primary" />
              <CardTitle>Ödeme Bilgileri</CardTitle>
            </div>
            <CardDescription>
              {getTierText()} {getPlanText()} için ödeme bilgilerinizi girin
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <div className="bg-muted/50 p-4 rounded-lg mb-4">
              <h3 className="font-medium mb-2">Sipariş Özeti</h3>
              <div className="flex justify-between mb-2">
                <span>Paket:</span>
                <span className="font-medium">{getTierText()}</span>
              </div>
              <div className="flex justify-between mb-2">
                <span>Plan:</span>
                <span className="font-medium">{getPlanText()}</span>
              </div>
              <div className="flex justify-between mb-2">
                <span>Fiyat:</span>
                <span className="font-medium">₺{getPrice()}</span>
              </div>
              <div className="border-t pt-2 mt-2 flex justify-between font-medium">
                <span>Toplam:</span>
                <span className="text-primary">₺{getPrice()}</span>
              </div>
            </div>

            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="cardName">Kart Üzerindeki İsim</Label>
                <Input id="cardName" placeholder="Ad Soyad" />
              </div>

              <div className="space-y-2">
                <Label htmlFor="cardNumber">Kart Numarası</Label>
                <Input id="cardNumber" placeholder="1234 5678 9012 3456" />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="expiryDate">Son Kullanma Tarihi</Label>
                  <Input id="expiryDate" placeholder="MM/YY" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="cvv">CVV</Label>
                  <Input id="cvv" placeholder="123" />
                </div>
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button
              variant="outline"
              onClick={() => setCurrentStep("plan-select")}
              disabled={isLoading}
            >
              Geri
            </Button>
            <Button onClick={handlePaymentSimulation} disabled={isLoading}>
              {isLoading ? "İşleniyor..." : "Ödemeyi Tamamla"}
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  // Salon kurulum ekranı
  if (currentStep === "gym-setup") {
    return (
      <div className="space-y-6">
        <Alert className="bg-green-50 text-green-800 border-green-200">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription>
            Ödemeniz başarıyla alındı! Şimdi salonunuzun bilgilerini girerek
            kurulumu tamamlayın.
          </AlertDescription>
        </Alert>

        <Card>
          <CardHeader>
            <CardTitle>Salon Bilgileri</CardTitle>
            <CardDescription>
              Salonunuzun temel bilgilerini girin
            </CardDescription>
          </CardHeader>
          <form onSubmit={handleGymSetupSubmit}>
            <CardContent className="space-y-4">
              {error && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              {success && (
                <Alert className="bg-green-50 text-green-800 border-green-200">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <AlertDescription>{success}</AlertDescription>
                </Alert>
              )}

              <div className="space-y-2">
                <Label htmlFor="name">Salon Adı</Label>
                <Input
                  id="name"
                  name="name"
                  value={gymFormData.name}
                  onChange={handleGymFormChange}
                  placeholder="Örn: Fitness Plus"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Salon Açıklaması</Label>
                <Textarea
                  id="description"
                  name="description"
                  value={gymFormData.description}
                  onChange={handleGymFormChange}
                  placeholder="Salonunuzu kısaca tanıtın"
                  rows={3}
                  required
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="phone">Telefon</Label>
                  <Input
                    id="phone"
                    name="phone"
                    value={gymFormData.phone}
                    onChange={handleGymFormChange}
                    placeholder="Örn: 0212 123 4567"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email">E-posta</Label>
                  <Input
                    id="email"
                    name="email"
                    value={gymFormData.email}
                    onChange={handleGymFormChange}
                    placeholder="Örn: <EMAIL>"
                    type="email"
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="address">Adres</Label>
                <Input
                  id="address"
                  name="address"
                  value={gymFormData.address}
                  onChange={handleGymFormChange}
                  placeholder="Sokak, Mahalle, No"
                  required
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="city">Şehir</Label>
                  <Input
                    id="city"
                    name="city"
                    value={gymFormData.city}
                    onChange={handleGymFormChange}
                    placeholder="Örn: İstanbul"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="district">İlçe</Label>
                  <Input
                    id="district"
                    name="district"
                    value={gymFormData.district}
                    onChange={handleGymFormChange}
                    placeholder="Örn: Kadıköy"
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="gym_type">Salon Türü</Label>
                <Select
                  value={gymFormData.gym_type}
                  onValueChange={handleGymTypeChange}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Salon türünü seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="fitness">Fitness Salonu</SelectItem>
                    <SelectItem value="crossfit">CrossFit</SelectItem>
                    <SelectItem value="yoga">Yoga Stüdyosu</SelectItem>
                    <SelectItem value="pilates">Pilates Stüdyosu</SelectItem>
                    <SelectItem value="martial_arts">
                      Dövüş Sanatları
                    </SelectItem>
                    <SelectItem value="swimming">Yüzme</SelectItem>
                    <SelectItem value="multi">
                      Çok Amaçlı Spor Merkezi
                    </SelectItem>
                    <SelectItem value="other">Diğer</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button
                type="button"
                variant="outline"
                onClick={() => setCurrentStep("payment")}
                disabled={isLoading}
              >
                Geri
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? "İşleniyor..." : "Salonu Oluştur ve Tamamla"}
              </Button>
            </CardFooter>
          </form>
        </Card>
      </div>
    );
  }

  return null;
}

"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { useAuth } from "@/components/auth/auth-provider"
import { Download, Search, CreditCard, CheckCircle, XCircle, AlertCircle } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { toast } from "sonner"
import { createClient } from "@/utils/supabase/client"

export function PaymentHistory({ isManager = false }: { isManager?: boolean }) {
  const { authUser } = useAuth()
  const [isLoading, setIsLoading] = useState(true)
  const [payments, setPayments] = useState<any[]>([])
  const [filteredPayments, setFilteredPayments] = useState<any[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [selectedPayment, setSelectedPayment] = useState<any | null>(null)
  const [isReceiptDialogOpen, setIsReceiptDialogOpen] = useState(false)

  useEffect(() => {
    const fetchPayments = async () => {
      if (!authUser) return

      setIsLoading(true)
      try {
        const supabase = createClient()

        // In a real implementation, these would be actual database queries
        // For now, we'll use mock data

        // Simulate API call delay
        await new Promise((resolve) => setTimeout(resolve, 1000))

        // Mock payments data
        const mockPayments = [
          {
            id: "1",
            date: "2023-05-01T10:15:30Z",
            amount: 350,
            currency: "TRY",
            status: "successful",
            payment_method: "credit_card",
            card_last4: "4242",
            description: "Aylık Üyelik - Mayıs 2023",
            gym_name: "FitLife Gym",
            user_name: "Ahmet Yılmaz",
            user_email: "<EMAIL>",
            invoice_number: "INV-2023-001",
          },
          {
            id: "2",
            date: "2023-04-01T09:30:15Z",
            amount: 350,
            currency: "TRY",
            status: "successful",
            payment_method: "credit_card",
            card_last4: "4242",
            description: "Aylık Üyelik - Nisan 2023",
            gym_name: "FitLife Gym",
            user_name: "Ahmet Yılmaz",
            user_email: "<EMAIL>",
            invoice_number: "INV-2023-002",
          },
          {
            id: "3",
            date: "2023-03-01T11:20:45Z",
            amount: 350,
            currency: "TRY",
            status: "successful",
            payment_method: "credit_card",
            card_last4: "4242",
            description: "Aylık Üyelik - Mart 2023",
            gym_name: "FitLife Gym",
            user_name: "Ahmet Yılmaz",
            user_email: "<EMAIL>",
            invoice_number: "INV-2023-003",
          },
          {
            id: "4",
            date: "2023-05-05T14:25:10Z",
            amount: 150,
            currency: "TRY",
            status: "successful",
            payment_method: "credit_card",
            card_last4: "5678",
            description: "10 Seans PT Paketi",
            gym_name: "FitLife Gym",
            user_name: "Ahmet Yılmaz",
            user_email: "<EMAIL>",
            invoice_number: "INV-2023-004",
          },
          {
            id: "5",
            date: "2023-05-10T16:40:20Z",
            amount: 900,
            currency: "TRY",
            status: "failed",
            payment_method: "credit_card",
            card_last4: "9876",
            description: "3 Aylık Üyelik",
            gym_name: "FitLife Gym",
            user_name: "Ahmet Yılmaz",
            user_email: "<EMAIL>",
            invoice_number: "INV-2023-005",
          },
          {
            id: "6",
            date: "2023-05-11T08:15:30Z",
            amount: 900,
            currency: "TRY",
            status: "pending",
            payment_method: "bank_transfer",
            card_last4: null,
            description: "3 Aylık Üyelik",
            gym_name: "FitLife Gym",
            user_name: "Ahmet Yılmaz",
            user_email: "<EMAIL>",
            invoice_number: "INV-2023-006",
          },
          {
            id: "7",
            date: "2023-05-12T13:10:45Z",
            amount: 900,
            currency: "TRY",
            status: "successful",
            payment_method: "credit_card",
            card_last4: "4242",
            description: "3 Aylık Üyelik",
            gym_name: "FitLife Gym",
            user_name: "Ahmet Yılmaz",
            user_email: "<EMAIL>",
            invoice_number: "INV-2023-007",
          },
        ]

        setPayments(mockPayments)
        setFilteredPayments(mockPayments)
      } catch (error) {
        console.error("Error fetching payments:", error)
        toast.error("Ödeme geçmişi yüklenirken bir hata oluştu.")
      } finally {
        setIsLoading(false)
      }
    }

    fetchPayments()
  }, [authUser])

  useEffect(() => {
    // Filter payments based on search term and status filter
    let filtered = payments as any[]

    if (searchTerm) {
      filtered = filtered.filter(
        (payment: any) =>
          payment.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
          payment.invoice_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
          (isManager && payment.user_name.toLowerCase().includes(searchTerm.toLowerCase())) ||
          (isManager && payment.user_email.toLowerCase().includes(searchTerm.toLowerCase())),
      )
    }

    if (statusFilter !== "all") {
      filtered = filtered.filter((payment: any) => payment.status === statusFilter)
    }

    setFilteredPayments(filtered)
  }, [searchTerm, statusFilter, payments, isManager])

  const handleDownloadReceipt = async (payment: any) => {
    try {
      // In a real implementation, this would generate and download a PDF receipt
      // For now, we'll just show a success message

      toast.success("Makbuz indirildi", {
        description: `${payment.invoice_number} numaralı makbuz başarıyla indirildi.`,
      })
    } catch (error) {
      console.error("Error downloading receipt:", error)
      toast.error("Makbuz indirilirken bir hata oluştu.")
    }
  }

  const formatDate = (dateString: string) => {
    if (!dateString) return ""

    const date = new Date(dateString)
    return date.toLocaleDateString("tr-TR", {
      day: "numeric",
      month: "long",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat("tr-TR", {
      style: "currency",
      currency: currency || "TRY",
      minimumFractionDigits: 2,
    }).format(amount)
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "successful":
        return (
          <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
            <CheckCircle className="h-3 w-3 mr-1" />
            Başarılı
          </Badge>
        )
      case "failed":
        return (
          <Badge variant="destructive">
            <XCircle className="h-3 w-3 mr-1" />
            Başarısız
          </Badge>
        )
      case "pending":
        return (
          <Badge variant="outline" className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">
            <AlertCircle className="h-3 w-3 mr-1" />
            Beklemede
          </Badge>
        )
      case "refunded":
        return (
          <Badge variant="outline" className="bg-blue-100 text-blue-800 hover:bg-blue-100">
            <CreditCard className="h-3 w-3 mr-1" />
            İade Edildi
          </Badge>
        )
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getPaymentMethodName = (method: string) => {
    const methods = {
      credit_card: "Kredi Kartı",
      bank_transfer: "Banka Havalesi",
      cash: "Nakit",
    }
    return methods[method as keyof typeof methods] || method
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Ödeme Geçmişi</CardTitle>
          <CardDescription>
            {isManager
              ? "Salonunuza yapılan tüm ödemeleri görüntüleyin ve yönetin"
              : "Üyelik ve paket ödemelerinizi görüntüleyin ve makbuzları indirin"}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder={
                    isManager ? "İsim, e-posta veya makbuz numarası ara..." : "Açıklama veya makbuz numarası ara..."
                  }
                  className="pl-8"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Durum filtresi" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tüm Ödemeler</SelectItem>
                  <SelectItem value="successful">Başarılı</SelectItem>
                  <SelectItem value="pending">Beklemede</SelectItem>
                  <SelectItem value="failed">Başarısız</SelectItem>
                  <SelectItem value="refunded">İade Edildi</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="rounded-md border">
              <div className="grid grid-cols-12 gap-4 p-4 font-medium border-b">
                <div className="col-span-3">Tarih</div>
                <div className="col-span-3">Açıklama</div>
                {isManager && <div className="col-span-2">Üye</div>}
                <div className={`col-span-${isManager ? 1 : 2}`}>Tutar</div>
                <div className="col-span-2">Durum</div>
                <div className="col-span-1 text-right">İşlem</div>
              </div>
              {filteredPayments.length > 0 ? (
                filteredPayments.map((payment: any) => (
                  <div key={payment.id} className="grid grid-cols-12 gap-4 p-4 border-b last:border-0 items-center">
                    <div className="col-span-3 text-sm">{formatDate(payment.date)}</div>
                    <div className="col-span-3 text-sm">{payment.description}</div>
                    {isManager && (
                      <div className="col-span-2 text-sm">
                        <div>{payment.user_name}</div>
                        <div className="text-xs text-muted-foreground">{payment.user_email}</div>
                      </div>
                    )}
                    <div className={`col-span-${isManager ? 1 : 2} font-medium`}>
                      {formatCurrency(payment.amount, payment.currency)}
                    </div>
                    <div className="col-span-2">{getStatusBadge(payment.status)}</div>
                    <div className="col-span-1 flex justify-end">
                      {payment.status === "successful" && (
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => {
                            setSelectedPayment(payment)
                            setIsReceiptDialogOpen(true)
                          }}
                          title="Makbuzu Görüntüle"
                        >
                          <Download className="h-4 w-4" />
                          <span className="sr-only">Makbuzu Görüntüle</span>
                        </Button>
                      )}
                    </div>
                  </div>
                ))
              ) : (
                <div className="p-4 text-center text-muted-foreground">
                  {searchTerm || statusFilter !== "all"
                    ? "Arama kriterlerine uygun ödeme bulunamadı."
                    : "Henüz ödeme kaydı bulunmuyor."}
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Receipt Dialog */}
      <Dialog open={isReceiptDialogOpen} onOpenChange={setIsReceiptDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Ödeme Makbuzu</DialogTitle>
            <DialogDescription>Ödeme detayları ve makbuz</DialogDescription>
          </DialogHeader>
          {selectedPayment && (
            <div className="py-4">
              <div className="border rounded-lg p-6 space-y-6">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="font-bold text-lg">{selectedPayment.gym_name}</h3>
                    <p className="text-sm text-muted-foreground">Spor Salonu</p>
                  </div>
                  <div className="text-right">
                    <h4 className="font-medium">Makbuz #{selectedPayment.invoice_number}</h4>
                    <p className="text-sm text-muted-foreground">{formatDate(selectedPayment.date)}</p>
                  </div>
                </div>

                <div className="border-t border-b py-4 space-y-2">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Müşteri</span>
                    <span className="font-medium">{selectedPayment.user_name}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">E-posta</span>
                    <span>{selectedPayment.user_email}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Ödeme Yöntemi</span>
                    <span>
                      {getPaymentMethodName(selectedPayment.payment_method)}
                      {selectedPayment.card_last4 && ` (**** **** **** ${selectedPayment.card_last4})`}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Durum</span>
                    <span>{getStatusBadge(selectedPayment.status)}</span>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between font-medium">
                    <span>Açıklama</span>
                    <span>Tutar</span>
                  </div>
                  <div className="flex justify-between">
                    <span>{selectedPayment.description}</span>
                    <span>{formatCurrency(selectedPayment.amount, selectedPayment.currency)}</span>
                  </div>
                  <div className="border-t pt-2 mt-4 flex justify-between font-bold">
                    <span>Toplam</span>
                    <span>{formatCurrency(selectedPayment.amount, selectedPayment.currency)}</span>
                  </div>
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsReceiptDialogOpen(false)}>
              Kapat
            </Button>
            <Button onClick={() => handleDownloadReceipt(selectedPayment)}>
              <Download className="h-4 w-4 mr-2" />
              PDF İndir
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

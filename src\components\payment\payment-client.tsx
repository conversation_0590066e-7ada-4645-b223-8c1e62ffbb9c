"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  ArrowLeft,
  CreditCard,
  Check,
  AlertCircle,
  Crown,
  Zap,
  Star,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { createManager } from "@/app/actions/user-actions";
import { useAuth } from "@/components/auth/auth-provider";

type SubscriptionPlan = "monthly" | "quarterly" | "yearly";
type SubscriptionTier = "starter" | "professional" | "enterprise";

// Paket fiyatları
const PACKAGE_PRICES = {
  starter: { monthly: 199, quarterly: 539, yearly: 1799 },
  professional: { monthly: 299, quarterly: 809, yearly: 2699 },
  enterprise: { monthly: 499, quarterly: 1349, yearly: 4999 },
};

// Paket özellikleri
const PACKAGE_FEATURES = {
  starter: [
    "1 salon",
    "100 üyeye kadar",
    "Temel raporlar",
    "Email destek",
    "Temel paket yönetimi",
  ],
  professional: [
    "3 salona kadar",
    "500 üyeye kadar",
    "Gelişmiş raporlar",
    "Ödeme entegrasyonu",
    "SMS bildirimleri",
    "Öncelikli destek",
  ],
  enterprise: [
    "Sınırsız salon",
    "Sınırsız üye",
    "AI destekli analizler",
    "API erişimi",
    "Özel entegrasyonlar",
    "7/24 telefon desteği",
  ],
};

export function PaymentClient() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { authUser } = useAuth();

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // URL'den tier ve plan bilgilerini al
  const tier = searchParams.get("tier") as SubscriptionTier;
  const plan = searchParams.get("plan") as SubscriptionPlan;

  useEffect(() => {
    // Geçersiz parametreler varsa pricing sayfasına yönlendir
    if (
      !tier ||
      !plan ||
      !PACKAGE_PRICES[tier] ||
      !PACKAGE_PRICES[tier][plan]
    ) {
      router.push("/pricing");
    }
  }, [tier, plan, router]);

  if (!tier || !plan) {
    return null; // Loading state, redirect edilecek
  }

  const getTierIcon = (tier: SubscriptionTier) => {
    switch (tier) {
      case "starter":
        return <Zap className="h-6 w-6" />;
      case "professional":
        return <Star className="h-6 w-6" />;
      case "enterprise":
        return <Crown className="h-6 w-6" />;
    }
  };

  const getTierColor = (tier: SubscriptionTier) => {
    switch (tier) {
      case "starter":
        return "text-blue-600";
      case "professional":
        return "text-green-600";
      case "enterprise":
        return "text-purple-600";
    }
  };

  const getTierName = (tier: SubscriptionTier) => {
    switch (tier) {
      case "starter":
        return "Starter";
      case "professional":
        return "Professional";
      case "enterprise":
        return "Enterprise";
    }
  };

  const getPlanName = (plan: SubscriptionPlan) => {
    switch (plan) {
      case "monthly":
        return "Aylık Plan";
      case "quarterly":
        return "3 Aylık Plan";
      case "yearly":
        return "Yıllık Plan";
    }
  };

  const getPrice = () => {
    return PACKAGE_PRICES[tier][plan];
  };

  const handlePayment = async () => {
    if (!authUser) {
      router.push("/login");
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Ödeme simülasyonu
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // Manager oluştur
      const result = await createManager(authUser.id, plan, tier);

      if (result.error) {
        setError(result.error);
        return;
      }

      // Başarılı ödeme sonrası gym-setup sayfasına yönlendir
      router.push("/gym-setup?success=true");
    } catch (error: any) {
      setError(error.message || "Ödeme işlemi sırasında bir hata oluştu.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center mb-8">
          <Button
            variant="ghost"
            onClick={() => router.push("/pricing")}
            className="mr-4"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Geri
          </Button>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Ödeme
          </h1>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
          {/* Paket Özeti */}
          <Card className="bg-white dark:bg-slate-800 border-slate-200 dark:border-slate-700">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-gray-900 dark:text-white">
                <div className={cn(getTierColor(tier))}>
                  {getTierIcon(tier)}
                </div>
                Paket Özeti
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="font-medium text-gray-700 dark:text-gray-300">
                  {getTierName(tier)} Paketi
                </span>
                <Badge
                  variant="secondary"
                  className="bg-slate-100 dark:bg-slate-700 text-slate-800 dark:text-slate-200"
                >
                  {getPlanName(plan)}
                </Badge>
              </div>

              <Separator className="bg-slate-200 dark:bg-slate-600" />

              <div className="space-y-2">
                <h4 className="font-medium text-gray-900 dark:text-white">
                  Paket Özellikleri:
                </h4>
                <ul className="space-y-2">
                  {PACKAGE_FEATURES[tier].map((feature, index) => (
                    <li
                      key={index}
                      className="flex items-center text-sm text-gray-700 dark:text-gray-300"
                    >
                      <Check className="h-4 w-4 mr-2 text-green-500 dark:text-green-400 flex-shrink-0" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>

              <Separator className="bg-slate-200 dark:bg-slate-600" />

              <div className="flex justify-between items-center text-lg font-bold text-gray-900 dark:text-white">
                <span>Toplam Tutar:</span>
                <span>₺{getPrice()}</span>
              </div>
            </CardContent>
          </Card>

          {/* Ödeme Formu */}
          <Card className="bg-white dark:bg-slate-800 border-slate-200 dark:border-slate-700">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-gray-900 dark:text-white">
                <CreditCard className="h-6 w-6" />
                Ödeme Bilgileri
              </CardTitle>
              <CardDescription className="text-gray-600 dark:text-gray-300">
                Güvenli ödeme ile paketinizi satın alın
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {error && (
                <Alert
                  variant="destructive"
                  className="bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800"
                >
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription className="text-red-800 dark:text-red-200">
                    {error}
                  </AlertDescription>
                </Alert>
              )}

              <Alert className="bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800">
                <AlertCircle className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                <AlertDescription className="text-blue-800 dark:text-blue-200">
                  Bu bir demo uygulamasıdır. Gerçek bir ödeme işlemi
                  yapılmayacaktır. Ödeme simülasyonu 2 saniye sürecektir.
                </AlertDescription>
              </Alert>

              <div className="space-y-4">
                <div className="p-4 border rounded-lg bg-slate-50 dark:bg-slate-700/50 border-slate-200 dark:border-slate-600">
                  <h4 className="font-medium mb-2 text-gray-900 dark:text-white">
                    Demo Ödeme Bilgileri
                  </h4>
                  <div className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                    <p>Kart Numarası: **** **** **** 1234</p>
                    <p>Son Kullanma: 12/25</p>
                    <p>CVV: ***</p>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button
                className="w-full"
                onClick={handlePayment}
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                    Ödeme İşleniyor...
                  </>
                ) : (
                  <>
                    <CreditCard className="h-4 w-4 mr-2" />₺{getPrice()} Öde
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>
        </div>
      </div>
    </div>
  );
}

---
description:
globs:
alwaysApply: false
---
# JSX Sözdizimi ve En İyi Pratikler

JSX, JavaScript XML anlamına gelir ve React ile UI geliştirmeyi sezgisel hale getiren bir sözdizimi uzantısıdır. HTML'e benzeyen bir yapıda JavaScript kodu içinde UI elemanları tanımlamanızı sağlar.

## JSX Temel Kuralları

### 1. <PERSON>k Kök Eleman

Her JSX ifadesi veya bileşen tek bir kök eleman döndürmelidir.

```jsx
// Yanlış
// return (
//   <h1>Başlık</h1>
//   <p>Paragraf</p>
// );

// Doğru - div ile sarmalama
return (
  <div>
    <h1>Başlık</h1>
    <p>Paragraf</p>
  </div>
);

// Doğru - Fragment kullanımı (tercih edilen)
return (
  <>
    <h1>Başlık</h1>
    <p>Paragraf</p>
  </>
);
```

### 2. Etiketlerin Kapatılması

HTML'deki bazı kendiliğinden kapanan etiketler dahil tüm etiketler JSX'te kapatılmalıdır.

```jsx
// Yanlış
// <img src="resim.jpg">
// <br>

// Doğru
<img src="resim.jpg" />
<br />
```

### 3. JavaScript İfadeleri

JSX içinde JavaScript ifadelerini süslü parantezler `{}` içine alarak kullanabilirsiniz.

```jsx
const name = 'Dünya';
const sayHello = <h1>Merhaba, {name}!</h1>;

// İfadelerde hesaplama yapılabilir
const sum = <p>Toplam: {1 + 2 + 3}</p>;

// Fonksiyon çağrıları kullanılabilir
function formatName(user) {
  return user.firstName + ' ' + user.lastName;
}
const user = { firstName: 'Ayşe', lastName: 'Yılmaz' };
const greeting = <h1>Merhaba, {formatName(user)}!</h1>;
```

### 4. Koşullu İfadeler

JSX içinde koşullu renderlamalar için üçlü operatör veya mantıksal operatörler kullanılır.

```jsx
// Üçlü operatör
return (
  <div>
    {isLoggedIn ? <UserGreeting /> : <GuestGreeting />}
  </div>
);

// Mantıksal AND operatörü
return (
  <div>
    {unreadMessages.length > 0 && 
      <p>Okunmamış {unreadMessages.length} mesajınız var.</p>
    }
  </div>
);
```

### 5. Döngüler ve Listeler

Dizileri JSX elemanlarına dönüştürmek için `map()` metodu kullanılır. Her eleman için benzersiz bir `key` prop'u verilmelidir.

```jsx
const numbers = [1, 2, 3, 4, 5];
return (
  <ul>
    {numbers.map((number) => (
      <li key={number.toString()}>Sayı: {number}</li>
    ))}
  </ul>
);
```

### 6. Nitelikler (Attributes)

HTML niteliklerine benzer ancak camelCase olarak yazılır. `class` yerine `className`, `for` yerine `htmlFor` kullanılır.

```jsx
// HTML
// <div class="container" tabindex="0">...</div>

// JSX
<div className="container" tabIndex={0}>...</div>
```

### 7. Stil Verme

Inline stiller bir JavaScript nesnesi olarak geçilir ve özellik isimleri camelCase olarak yazılır.

```jsx
const divStyle = {
  backgroundColor: 'lightblue',
  fontSize: '16px',
  padding: '10px',
  borderRadius: '5px'
};

return <div style={divStyle}>Stilli div</div>;

// Doğrudan stil vermek de mümkündür
return <span style={{ color: 'red', fontWeight: 'bold' }}>Önemli!</span>;
```

## JSX En İyi Pratikleri

### 1. Bileşen Yapısı

Bileşen tanımlamaları temiz ve anlaşılır olmalıdır.

```jsx
// Fonksiyonel bileşen
function UserProfile({ name, email, avatar }) {
  return (
    <div className="user-profile">
      <img src={avatar} alt={`${name}'ın profil fotoğrafı`} />
      <h2>{name}</h2>
      <p>{email}</p>
    </div>
  );
}
```

### 2. Çocuk Bileşenler (Children)

`props.children` ile kapsayıcı bileşenler oluşturun.

```jsx
function Card({ title, children }) {
  return (
    <div className="card">
      <h2>{title}</h2>
      <div className="card-content">
        {children}
      </div>
    </div>
  );
}

// Kullanım
function App() {
  return (
    <Card title="Hoş geldiniz">
      <p>Bu bir kart içeriğidir.</p>
      <button>Tıkla</button>
    </Card>
  );
}
```

### 3. Koşullu Class'lar

Koşullu sınıf atamaları için template literals kullanın.

```jsx
<button className={`btn ${isActive ? 'btn-active' : ''}`}>
  Düğme
</button>

// Birden çok koşul için
<div className={`
  message
  ${isError ? 'message-error' : ''}
  ${isWarning ? 'message-warning' : ''}
`}>
  Mesaj içeriği
</div>
```

### 4. JSX'te Mantıksal Gruplandırma

Karmaşık koşullu render mantığını ayırın.

```jsx
// Karmaşık koşullu render için ayrı fonksiyonlar kullanın
function renderContent() {
  if (isLoading) {
    return <LoadingSpinner />;
  }
  
  if (error) {
    return <ErrorMessage message={error.message} />;
  }
  
  if (data.length === 0) {
    return <EmptyState />;
  }
  
  return <DataList items={data} />;
}

function App() {
  return (
    <div className="container">
      <h1>Veriler</h1>
      {renderContent()}
    </div>
  );
}
```

### 5. JSX İçinde Yorum Satırları

JSX içinde yorum yazmak için süslü parantezler ve JavaScript yorum yapısı kullanılır.

```jsx
return (
  <div>
    {/* Bu bir JSX yorumudur */}
    <h1>Başlık</h1>
    {/* 
      Çok satırlı 
      yorumlar da 
      desteklenir 
    */}
    <p>Paragraf</p>
  </div>
);
```

### 6. JSX Spread Operatörü

Props'ları yaymak (spread) için `...` operatörünü kullanabilirsiniz.

```jsx
function Button(props) {
  const { className, children, ...otherProps } = props;
  return (
    <button 
      className={`default-btn ${className || ''}`}
      {...otherProps}
    >
      {children}
    </button>
  );
}

// Kullanım
<Button 
  className="primary" 
  onClick={handleClick}
  disabled={isLoading}
>
  Gönder
</Button>
```

### 7. JSX İle Tipik Desenler

#### Koşullu Render

```jsx
{condition && <Component />}
{condition ? <ComponentA /> : <ComponentB />}
```

#### Ternary Operator'ün İç İçe Kullanımı

```jsx
{isLoggedIn 
  ? user.isAdmin 
    ? <AdminDashboard /> 
    : <UserDashboard />
  : <LoginForm />
}
```

#### Null Olabilecek Değerlerin Güvenli Erişimi

```jsx
{user?.address?.city || 'Şehir bilgisi yok'}
```

#### Liste Renderlaması ve Filtre Zinciri

```jsx
{users
  .filter(user => user.isActive)
  .map(user => (
    <UserItem 
      key={user.id} 
      user={user} 
    />
  ))
}
```

## React Fragment'lar

Ekstra DOM düğümleri oluşturmadan bir grup eleman döndürmek için React.Fragment veya kısaltması `<>...</>` kullanılır.

```jsx
// Uzun gösterim
<React.Fragment>
  <h1>Başlık</h1>
  <p>Paragraf</p>
</React.Fragment>

// Kısa gösterim
<>
  <h1>Başlık</h1>
  <p>Paragraf</p>
</>

// key prop'u gerektiğinde uzun gösterim kullanılmalıdır
<React.Fragment key={item.id}>
  <h2>{item.title}</h2>
  <p>{item.description}</p>
</React.Fragment>
```

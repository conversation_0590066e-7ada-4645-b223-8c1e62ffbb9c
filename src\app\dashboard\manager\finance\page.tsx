"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/components/auth/auth-provider";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  DollarSign,
  TrendingUp,
  TrendingDown,
  CreditCard,
  Calendar,
  Building,
  PieChart,
  BarChart3,
  Download,
  Filter,
  Loader2,
  AlertTriangle,
  CheckCircle,
  Clock,
  XCircle,
  Users,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { getAllGymsFinanceData } from "@/app/actions/gym-finance-actions";

interface AllGymsFinanceData {
  totalRevenue: number;
  monthlyRevenue: number;
  pendingPayments: number;
  completedPayments: number;
  revenueGrowth: number;
  paymentGrowth: number;
  totalMembers: number;
  activeMembers: number;
  totalGyms: number;
  averageRevenuePerGym: number;
  gymPerformance: {
    gymId: string;
    gymName: string;
    revenue: number;
    members: number;
    growth: number;
    averageRevenuePerMember: number;
  }[];
  monthlyData: {
    month: string;
    revenue: number;
    payments: number;
    newMembers: number;
  }[];
  paymentMethods: {
    method: string;
    amount: number;
    percentage: number;
  }[];
  recentTransactions: {
    id: string;
    memberName: string;
    gymName: string;
    amount: number;
    status: "completed" | "pending" | "failed";
    date: string;
    packageName: string;
  }[];
}

export default function FinancePage() {
  const { authUser } = useAuth();
  const [financeData, setFinanceData] = useState<AllGymsFinanceData | null>(
    null
  );
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedPeriod, setSelectedPeriod] = useState<
    "month" | "quarter" | "year"
  >("month");

  useEffect(() => {
    const loadFinanceData = async () => {
      if (!authUser) {
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        // Gerçek veritabanından veri al
        const response = await getAllGymsFinanceData(authUser.id);
        if (!response.success || !response.data) {
          throw new Error(response.error || "Finansal veriler alınamadı");
        }
        setFinanceData(response.data);
      } catch (err: any) {
        console.error("Error loading finance data:", err);
        setError(
          err.message || "Finansal veriler yüklenirken bir hata oluştu."
        );
      } finally {
        setIsLoading(false);
      }
    };

    loadFinanceData();
  }, [authUser, selectedPeriod]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("tr-TR", {
      style: "currency",
      currency: "TRY",
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value > 0 ? "+" : ""}${value.toFixed(1)}%`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("tr-TR", {
      day: "numeric",
      month: "short",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getPaymentStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "pending":
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case "failed":
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getPaymentStatusBadge = (status: string) => {
    switch (status) {
      case "completed":
        return (
          <Badge className="bg-green-100 text-green-800">Tamamlandı</Badge>
        );
      case "pending":
        return (
          <Badge className="bg-yellow-100 text-yellow-800">Beklemede</Badge>
        );
      case "failed":
        return <Badge className="bg-red-100 text-red-800">Başarısız</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="flex items-center gap-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Finansal veriler yükleniyor...</span>
        </div>
      </div>
    );
  }

  if (!authUser) {
    return (
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          Finansal verileri görüntülemek için giriş yapmanız gerekiyor.
        </AlertDescription>
      </Alert>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <DollarSign className="h-8 w-8" />
            Genel Finansal Özet
          </h1>
          <p className="text-muted-foreground">
            Tüm salonlarınızın toplu gelir analizi ve performans karşılaştırması
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" className="gap-2">
            <Filter className="h-4 w-4" />
            Filtrele
          </Button>
          <Button className="gap-2">
            <Download className="h-4 w-4" />
            Rapor İndir
          </Button>
        </div>
      </div>

      {/* Period Selection */}
      <div className="flex gap-2">
        {[
          { key: "month", label: "Bu Ay" },
          { key: "quarter", label: "Bu Çeyrek" },
          { key: "year", label: "Bu Yıl" },
        ].map((period) => (
          <Button
            key={period.key}
            variant={selectedPeriod === period.key ? "default" : "outline"}
            size="sm"
            onClick={() => setSelectedPeriod(period.key as any)}
          >
            {period.label}
          </Button>
        ))}
      </div>

      {financeData && (
        <>
          {/* Key Financial Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">
                  Toplam Gelir
                </CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatCurrency(financeData.totalRevenue)}
                </div>
                <div className="flex items-center gap-1 text-xs">
                  {financeData.revenueGrowth > 0 ? (
                    <TrendingUp className="h-3 w-3 text-green-500" />
                  ) : (
                    <TrendingDown className="h-3 w-3 text-red-500" />
                  )}
                  <span
                    className={cn(
                      financeData.revenueGrowth > 0
                        ? "text-green-500"
                        : "text-red-500"
                    )}
                  >
                    {formatPercentage(financeData.revenueGrowth)}
                  </span>
                  <span className="text-muted-foreground">önceki aya göre</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">
                  Aylık Gelir
                </CardTitle>
                <Calendar className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatCurrency(financeData.monthlyRevenue)}
                </div>
                <p className="text-xs text-muted-foreground">
                  Bu ayki toplam gelir
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">
                  Toplam Salon
                </CardTitle>
                <Building className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {financeData.totalGyms}
                </div>
                <p className="text-xs text-muted-foreground">
                  Aktif salon sayısı
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">
                  Toplam Üye
                </CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {financeData.activeMembers}
                </div>
                <p className="text-xs text-muted-foreground">
                  Aktif üye sayısı
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Additional Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">
                  Bekleyen Ödemeler
                </CardTitle>
                <Clock className="h-4 w-4 text-yellow-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-yellow-600">
                  {formatCurrency(financeData.pendingPayments)}
                </div>
                <p className="text-xs text-muted-foreground">
                  Onay bekleyen ödemeler
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">
                  Salon Başına Ortalama
                </CardTitle>
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatCurrency(financeData.averageRevenuePerGym)}
                </div>
                <p className="text-xs text-muted-foreground">
                  Ortalama salon geliri
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">
                  Ödeme Başarı Oranı
                </CardTitle>
                <PieChart className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {financeData.pendingPayments + financeData.completedPayments >
                  0
                    ? Math.round(
                        (financeData.completedPayments /
                          (financeData.pendingPayments +
                            financeData.completedPayments)) *
                          100
                      )
                    : 0}
                  %
                </div>
                <p className="text-xs text-muted-foreground">
                  Başarılı ödeme oranı
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Gym Performance & Monthly Data */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Gym Performance */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building className="h-5 w-5" />
                  Salon Performansı
                </CardTitle>
                <CardDescription>
                  Salonlarınızın gelir performansı karşılaştırması
                </CardDescription>
              </CardHeader>
              <CardContent>
                {financeData.gymPerformance.length === 0 ? (
                  <div className="text-center py-8">
                    <Building className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-lg font-medium text-muted-foreground">
                      Henüz salon yok
                    </p>
                    <p className="text-sm text-muted-foreground">
                      İlk salonunuzu oluşturun
                    </p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {financeData.gymPerformance.map((gym, index) => (
                      <div
                        key={gym.gymId}
                        className="flex items-center justify-between p-4 rounded-lg border"
                      >
                        <div className="flex items-center gap-3">
                          <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                            <span className="text-sm font-semibold">
                              #{index + 1}
                            </span>
                          </div>
                          <div>
                            <p className="font-medium">{gym.gymName}</p>
                            <p className="text-sm text-muted-foreground">
                              {gym.members} aktif üye
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold text-lg">
                            {formatCurrency(gym.revenue)}
                          </p>
                          <div className="flex items-center gap-1">
                            {gym.growth > 0 ? (
                              <TrendingUp className="h-3 w-3 text-green-500" />
                            ) : (
                              <TrendingDown className="h-3 w-3 text-red-500" />
                            )}
                            <span
                              className={cn(
                                "text-sm",
                                gym.growth > 0
                                  ? "text-green-500"
                                  : "text-red-500"
                              )}
                            >
                              {formatPercentage(gym.growth)}
                            </span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Monthly Performance */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Aylık Performans
                </CardTitle>
                <CardDescription>
                  Son 3 ayın gelir ve üye analizi
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {financeData.monthlyData.map((month, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-3 rounded-lg border"
                    >
                      <div>
                        <p className="font-medium">{month.month}</p>
                        <p className="text-sm text-muted-foreground">
                          {month.payments} ödeme • {month.newMembers} yeni üye
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold">
                          {formatCurrency(month.revenue)}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Payment Methods & Recent Transactions */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Payment Methods */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <PieChart className="h-5 w-5" />
                  Ödeme Yöntemleri
                </CardTitle>
                <CardDescription>
                  Ödeme yöntemlerine göre dağılım
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {financeData.paymentMethods.map((method, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between"
                    >
                      <div className="flex items-center gap-2">
                        <CreditCard className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm font-medium">
                          {method.method}
                        </span>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-semibold">
                          {formatCurrency(method.amount)}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          %{method.percentage}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Recent Transactions */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CreditCard className="h-5 w-5" />
                  Son İşlemler
                </CardTitle>
                <CardDescription>
                  En son gerçekleşen ödeme işlemleri
                </CardDescription>
              </CardHeader>
              <CardContent>
                {financeData.recentTransactions.length === 0 ? (
                  <div className="text-center py-8">
                    <CreditCard className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-lg font-medium text-muted-foreground">
                      Henüz işlem yok
                    </p>
                    <p className="text-sm text-muted-foreground">
                      Ödeme işlemleri burada görünecek
                    </p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {financeData.recentTransactions
                      .slice(0, 5)
                      .map((transaction) => (
                        <div
                          key={transaction.id}
                          className="flex items-center justify-between p-3 rounded-lg border"
                        >
                          <div className="flex items-center gap-3">
                            {getPaymentStatusIcon(transaction.status)}
                            <div>
                              <p className="text-sm font-medium">
                                {transaction.memberName}
                              </p>
                              <p className="text-xs text-muted-foreground">
                                {transaction.gymName} •{" "}
                                {transaction.packageName}
                              </p>
                              <p className="text-xs text-muted-foreground">
                                {formatDate(transaction.date)}
                              </p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="text-sm font-semibold">
                              {formatCurrency(transaction.amount)}
                            </p>
                            {getPaymentStatusBadge(transaction.status)}
                          </div>
                        </div>
                      ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </>
      )}
    </div>
  );
}

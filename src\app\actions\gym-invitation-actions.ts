"use server";

import { createClient } from "@/utils/supabase/server";
import { getSupabaseAdmin } from "@/utils/supabase/admin";
import { createNotification } from "@/lib/actions/notifications";
import { logSuccess, logError } from "@/lib/audit-logger";
import { revalidatePath } from "next/cache";

/**
 * Üyenin salona katılım isteği göndermesi (Senaryo 1)
 */
export async function sendMembershipRequest(gymId: string) {
  try {
    const supabase = await createClient();

    // 1. Kullanıcının oturum açıp açmadığını kontrol et
    const { data: userData, error: userError } = await supabase.auth.getUser();

    if (userError || !userData?.user) {
      return {
        success: false,
        error: "Katılım isteği göndermek için giriş yapmanız gerekiyor.",
      };
    }

    // 2. <PERSON><PERSON><PERSON><PERSON><PERSON> profilini al
    const { data: userProfile, error: profileError } = await supabase
      .from("users")
      .select("*")
      .eq("id", userData.user.id)
      .single();

    if (profileError || !userProfile) {
      return {
        success: false,
        error: "Kullanıcı profili bulunamadı.",
      };
    }

    // 3. Salon bilgilerini al
    const { data: gym, error: gymError } = await supabase
      .from("gyms")
      .select("id, name, manager_user_id")
      .eq("id", gymId)
      .single();

    if (gymError || !gym) {
      return {
        success: false,
        error: "Salon bulunamadı.",
      };
    }

    // 4. Zaten üye olup olmadığını kontrol et
    const { data: existingMembership, error: membershipError } = await supabase
      .from("memberships")
      .select("id, status")
      .eq("user_id", userData.user.id)
      .eq("gym_id", gymId)
      .maybeSingle();

    if (membershipError && membershipError.code !== "PGRST116") {
      return {
        success: false,
        error: "Üyelik kontrolü sırasında hata oluştu.",
      };
    }

    if (existingMembership) {
      return {
        success: false,
        error: "Bu salona zaten üyesiniz.",
      };
    }

    // 5. Bekleyen davet var mı kontrol et
    const { data: existingInvitation, error: invitationError } = await supabase
      .from("gym_invitations")
      .select("id, status")
      .eq("invitee_user_id", userData.user.id)
      .eq("gym_id", gymId)
      .eq("status", "pending")
      .maybeSingle();

    if (invitationError && invitationError.code !== "PGRST116") {
      return {
        success: false,
        error: "Davet kontrolü sırasında hata oluştu.",
      };
    }

    if (existingInvitation) {
      return {
        success: false,
        error: "Bu salon için zaten bekleyen bir isteğiniz bulunmaktadır.",
      };
    }

    // 6. Admin client ile davet oluştur
    const adminClient = getSupabaseAdmin();

    const { data: invitation, error: createError } = await adminClient
      .from("gym_invitations")
      .insert({
        inviter_user_id: userData.user.id, // Üye kendi kendine istek atıyor
        invitee_user_id: userData.user.id,
        gym_id: gymId,
        invitation_type: "member_to_gym", // Database constraint'e uygun
        status: "pending",
        message: `${userProfile.name} ${userProfile.surname} salonunuza katılmak istiyor.`,
      })
      .select()
      .single();

    if (createError) {
      console.error("Davet oluşturulurken hata:", createError);
      return {
        success: false,
        error: "Katılım isteği gönderilirken hata oluştu.",
      };
    }

    // 7. Manager'a bildirim gönder
    try {
      await createNotification({
        userId: gym.manager_user_id,
        title: "Yeni Üyelik İsteği",
        message: `${userProfile.name} ${userProfile.surname} ${gym.name} salonunuza katılmak istiyor.`,
        type: "membership_request",
        relatedEntityType: "gym_invitations",
        relatedEntityId: invitation.id,
      });
    } catch (notifError) {
      console.error("Bildirim gönderilirken hata:", notifError);
      // Bildirim hatası kritik değil, devam edebiliriz
    }

    // 8. Başarı audit log
    await logSuccess(
      "send_membership_request",
      "gym_invitation",
      `Üyelik isteği gönderildi: ${userProfile.name} ${userProfile.surname} → ${gym.name}`,
      {
        resourceId: invitation.id,
        gymId: gymId,
        newValues: {
          inviter_user_id: userData.user.id,
          invitee_user_id: userData.user.id,
          gym_id: gymId,
          invitation_type: "member_to_gym",
          status: "pending",
        },
        metadata: {
          user_name: `${userProfile.name} ${userProfile.surname}`,
          gym_name: gym.name,
          request_source: "web_search",
        },
      }
    );

    // 9. Sayfayı yenile
    revalidatePath("/findGym");

    return {
      success: true,
      data: invitation,
      message:
        "Katılım isteğiniz başarıyla gönderildi. Salon yöneticisi isteğinizi değerlendirip size bildirim gönderecektir.",
    };
  } catch (error: any) {
    console.error("Katılım isteği gönderilirken beklenmeyen hata:", error);

    // Hata audit log
    await logError(
      "send_membership_request",
      "gym_invitation",
      `Katılım isteği gönderilirken hata: ${error.message}`,
      error.message,
      {
        gymId: gymId,
        metadata: {
          error_type: "unexpected_error",
        },
      }
    );

    return {
      success: false,
      error:
        "Katılım isteği gönderilirken bir hata oluştu. Lütfen tekrar deneyin.",
    };
  }
}

/**
 * Manager'ın üyelik isteğini onaylaması
 */
export async function approveMembershipRequest(invitationId: string) {
  try {
    const supabase = await createClient();

    // 1. Kullanıcının oturum açıp açmadığını kontrol et
    const { data: userData, error: userError } = await supabase.auth.getUser();

    if (userError || !userData?.user) {
      return {
        success: false,
        error: "Oturum açmanız gerekiyor.",
      };
    }

    // 2. Admin client ile davet bilgilerini al
    const adminClient = getSupabaseAdmin();

    const { data: invitation, error: invitationError } = await adminClient
      .from("gym_invitations")
      .select(
        `
        *,
        gym:gyms(id, name, manager_user_id),
        invitee:users!gym_invitations_invitee_user_id_fkey(id, name, surname, email)
      `
      )
      .eq("id", invitationId)
      .single();

    if (invitationError || !invitation) {
      return {
        success: false,
        error: "Davet bulunamadı.",
      };
    }

    // 3. Manager yetkisi kontrolü
    if (invitation.gym.manager_user_id !== userData.user.id) {
      return {
        success: false,
        error: "Bu işlemi yapmaya yetkiniz bulunmamaktadır.",
      };
    }

    // 4. Davet durumu kontrolü
    if (invitation.status !== "pending") {
      return {
        success: false,
        error: "Bu davet zaten işleme alınmış.",
      };
    }

    // 5. Daveti onayla
    const { error: updateError } = await adminClient
      .from("gym_invitations")
      .update({
        status: "accepted",
        responded_at: new Date().toISOString(),
      })
      .eq("id", invitationId);

    if (updateError) {
      console.error("Davet güncellenirken hata:", updateError);
      return {
        success: false,
        error: "Davet onaylanırken hata oluştu.",
      };
    }

    // 6. Membership oluştur
    const { data: membership, error: membershipError } = await adminClient
      .from("memberships")
      .insert({
        user_id: invitation.invitee_user_id,
        gym_id: invitation.gym_id,
        status: "passive",
        approved_at: new Date().toISOString(),
        request_date: invitation.created_at,
      })
      .select()
      .single();

    if (membershipError) {
      console.error("Üyelik oluşturulurken hata:", membershipError);
      return {
        success: false,
        error: "Üyelik oluşturulurken hata oluştu.",
      };
    }

    // 7. Üyeye onay bildirimi gönder
    try {
      await createNotification({
        userId: invitation.invitee_user_id,
        title: "Üyelik İsteğiniz Onaylandı",
        message: `${invitation.gym.name} salonuna üyeliğiniz onaylandı. Artık salona gidebilirsiniz!`,
        type: "membership_approved",
        relatedEntityType: "memberships",
        relatedEntityId: membership.id,
      });
    } catch (notifError) {
      console.error("Bildirim gönderilirken hata:", notifError);
    }

    // 8. Başarı audit log
    await logSuccess(
      "approve_membership_request",
      "gym_invitation",
      `Üyelik isteği onaylandı: ${invitation.invitee.name} ${invitation.invitee.surname} → ${invitation.gym.name}`,
      {
        resourceId: invitationId,
        gymId: invitation.gym_id,
        oldValues: { status: "pending" },
        newValues: { status: "accepted" },
        metadata: {
          membership_id: membership.id,
          user_name: `${invitation.invitee.name} ${invitation.invitee.surname}`,
          gym_name: invitation.gym.name,
        },
      }
    );

    return {
      success: true,
      data: { invitation, membership },
      message: "Üyelik isteği başarıyla onaylandı.",
    };
  } catch (error: any) {
    console.error("Üyelik isteği onaylanırken beklenmeyen hata:", error);

    await logError(
      "approve_membership_request",
      "gym_invitation",
      `Üyelik isteği onaylanırken hata: ${error.message}`,
      error.message,
      {
        resourceId: invitationId,
        metadata: {
          error_type: "unexpected_error",
        },
      }
    );

    return {
      success: false,
      error:
        "Üyelik isteği onaylanırken bir hata oluştu. Lütfen tekrar deneyin.",
    };
  }
}

/**
 * Manager'ın üyelik isteğini reddetmesi
 */
export async function rejectMembershipRequest(
  invitationId: string,
  reason?: string
) {
  try {
    const supabase = await createClient();

    // 1. Kullanıcının oturum açıp açmadığını kontrol et
    const { data: userData, error: userError } = await supabase.auth.getUser();

    if (userError || !userData?.user) {
      return {
        success: false,
        error: "Oturum açmanız gerekiyor.",
      };
    }

    // 2. Admin client ile davet bilgilerini al
    const adminClient = getSupabaseAdmin();

    const { data: invitation, error: invitationError } = await adminClient
      .from("gym_invitations")
      .select(
        `
        *,
        gym:gyms(id, name, manager_user_id),
        invitee:users!gym_invitations_invitee_user_id_fkey(id, name, surname, email)
      `
      )
      .eq("id", invitationId)
      .single();

    if (invitationError || !invitation) {
      return {
        success: false,
        error: "Davet bulunamadı.",
      };
    }

    // 3. Manager yetkisi kontrolü
    if (invitation.gym.manager_user_id !== userData.user.id) {
      return {
        success: false,
        error: "Bu işlemi yapmaya yetkiniz bulunmamaktadır.",
      };
    }

    // 4. Davet durumu kontrolü
    if (invitation.status !== "pending") {
      return {
        success: false,
        error: "Bu davet zaten işleme alınmış.",
      };
    }

    // 5. Daveti reddet
    const { error: updateError } = await adminClient
      .from("gym_invitations")
      .update({
        status: "rejected",
        responded_at: new Date().toISOString(),
        message: reason || "Üyelik isteğiniz reddedildi.",
      })
      .eq("id", invitationId);

    if (updateError) {
      console.error("Davet güncellenirken hata:", updateError);
      return {
        success: false,
        error: "Davet reddedilirken hata oluştu.",
      };
    }

    // 6. Üyeye red bildirimi gönder
    try {
      await createNotification({
        userId: invitation.invitee_user_id,
        title: "Üyelik İsteğiniz Reddedildi",
        message:
          reason ||
          `${invitation.gym.name} salonuna üyelik isteğiniz reddedildi.`,
        type: "membership_rejected",
        relatedEntityType: "gym_invitations",
        relatedEntityId: invitationId,
      });
    } catch (notifError) {
      console.error("Bildirim gönderilirken hata:", notifError);
    }

    // 7. Başarı audit log
    await logSuccess(
      "reject_membership_request",
      "gym_invitation",
      `Üyelik isteği reddedildi: ${invitation.invitee.name} ${invitation.invitee.surname} → ${invitation.gym.name}`,
      {
        resourceId: invitationId,
        gymId: invitation.gym_id,
        oldValues: { status: "pending" },
        newValues: { status: "rejected", reason: reason },
        metadata: {
          user_name: `${invitation.invitee.name} ${invitation.invitee.surname}`,
          gym_name: invitation.gym.name,
          rejection_reason: reason,
        },
      }
    );

    return {
      success: true,
      message: "Üyelik isteği başarıyla reddedildi.",
    };
  } catch (error: any) {
    console.error("Üyelik isteği reddedilirken beklenmeyen hata:", error);

    await logError(
      "reject_membership_request",
      "gym_invitation",
      `Üyelik isteği reddedilirken hata: ${error.message}`,
      error.message,
      {
        resourceId: invitationId,
        metadata: {
          error_type: "unexpected_error",
        },
      }
    );

    return {
      success: false,
      error:
        "Üyelik isteği reddedilirken bir hata oluştu. Lütfen tekrar deneyin.",
    };
  }
}

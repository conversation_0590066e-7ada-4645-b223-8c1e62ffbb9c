# NextJS & Supabase ile En İyi Kimlik Doğrulama Sistemi Nasıl Oluşturulur?

Bu rehber, NextJS (App Router) ve Supabase kullanarak modern, güvenli ve kullanıcı dostu bir kimlik doğrulama sistemi oluşturmanız için size adım adım yol gösterecektir.

## G<PERSON>ş

Kimlik doğrulama, web uygulamalarının temel bir parçasıdır. Kullanıcıların kimliklerini doğrulamak, kişiselleştirilmiş deneyimler sunmak ve hassas verilere erişimi kontrol etmek için gereklidir. NextJS'in esnekliği ve Supabase'in güçlü backend hizmetleri (özellikle Auth) bir araya geldiğinde, etkili bir kimlik doğrulama çözümü oluşturmak oldukça kolaylaşır.

Bu rehberde ele alınacak ana konular:

- Proje kurulumu ve yapılandırması
- Supabase Auth (Kimlik Doğrulama) özelliklerinin kullanımı
- E-posta/şifre ile kayıt ve giriş
- Sosyal medya ile giriş (OAuth)
- Şifre sıfırlama ve e-posta doğrulama
- Magic Link ile parolasız giriş
- Oturum yönetimi ve korumalı rotalar
- Sunucu Eylemleri (Server Actions) ile güvenli işlemler
- İleri düzey konular: MFA, RLS, JWT yönetimi
- Güvenlik en iyi pratikleri

## 1. Projeyi Hazırlama

### 1.1. NextJS Projesi Oluşturma

Öncelikle bir NextJS projesi oluşturalım. App Router ve TypeScript kullanacağız.

```bash
npx create-next-app@latest my-auth-app --typescript --tailwind --eslint
# veya
yarn create next-app my-auth-app --typescript --tailwind --eslint
# veya
pnpm create next-app my-auth-app --typescript --tailwind --eslint
```

Proje dizinine gidin:
```bash
cd my-auth-app
```

### 1.2. Supabase Projesi Oluşturma

1.  **Supabase Hesabı:** Eğer bir Supabase hesabınız yoksa, [supabase.com](https://supabase.com) adresinden ücretsiz bir hesap oluşturun.
2.  **Yeni Proje:** Supabase dashboard'undan yeni bir proje oluşturun. Projenize bir isim verin, bir veritabanı şifresi oluşturun (güvenli bir yerde saklayın) ve projenizin oluşturulmasını bekleyin.
3.  **Proje Bilgileri:** Projeniz oluşturulduktan sonra, Proje Ayarları (Project Settings) > API bölümünden Proje URL'nizi (`Project URL`) ve `anon public` anahtarınızı (`anon public key`) not alın. Bunlar NextJS uygulamanızda Supabase'e bağlanmak için gerekecektir.

### 1.3. Gerekli Paketleri Yükleme

NextJS projenizde Supabase ile etkileşim kurmak ve kimlik doğrulama işlemlerini kolaylaştırmak için gerekli paketleri yükleyin:

```bash
npm install @supabase/supabase-js @supabase/ssr
# veya
yarn add @supabase/supabase-js @supabase/ssr
# veya
pnpm add @supabase/supabase-js @supabase/ssr
```

-   `@supabase/supabase-js`: Supabase ile etkileşim kurmak için temel JavaScript istemci kütüphanesi.
-   `@supabase/ssr`: NextJS (App Router ve Pages Router) ile Supabase Auth entegrasyonunu sunucu tarafında oluşturma (SSR) ve çerez yönetimi için güncel pakettir.

### 1.4. Çevresel Değişkenleri Yapılandırma

Projenizin kök dizininde `.env.local` adında bir dosya oluşturun ve Supabase proje bilgilerinizi buraya ekleyin:

```env
NEXT_PUBLIC_SUPABASE_URL=YOUR_SUPABASE_PROJECT_URL
NEXT_PUBLIC_SUPABASE_ANON_KEY=YOUR_SUPABASE_ANON_KEY
```

**ÖNEMLİ:** `YOUR_SUPABASE_PROJECT_URL` ve `YOUR_SUPABASE_ANON_KEY` kısımlarını kendi Supabase projenizin bilgileriyle değiştirin. `NEXT_PUBLIC_` öneki, bu değişkenlerin tarayıcı tarafında da erişilebilir olmasını sağlar, ki bu Supabase istemcisini başlatmak için gereklidir.

### 1.5. Supabase İstemcisini Oluşturma

Supabase ile etkileşim kuracak merkezi bir istemci oluşturmak iyi bir pratiktir. `@supabase/ssr` paketi, istemcileri oluşturmak için farklı yardımcı fonksiyonlar sunar. `src/lib/supabase` gibi bir klasör yapısı kullanabilirsiniz.

#### 1.5.1. Tarayıcı İstemcisi (`client.ts`)

`src/lib/supabase/client.ts` (veya `.js`):

```typescript
// src/lib/supabase/client.ts
import { createBrowserClient } from '@supabase/ssr'
import type { Database } from './database.types' // Eğer tipleri ürettiyseniz

export function createSupabaseBrowserClient() {
  return createBrowserClient<Database>( // Database tipini ekleyebilirsiniz
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  )
}
```
Bu istemci, Client Component'lerde kullanılacaktır.

#### 1.5.2. Sunucu İstemcisi (`server.ts`)

Server Component'ler, Route Handler'lar ve Server Action'lar içinde kullanılacak istemciyi oluşturmak için `createServerClient` kullanılır. Bu fonksiyon, `cookies` fonksiyonunu `next/headers`'tan alarak çerezleri okuyup yazabilir.

`src/lib/supabase/server.ts` (veya `.js`):

```typescript
// src/lib/supabase/server.ts
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import type { Database } from './database.types' // Eğer tipleri ürettiyseniz

export function createSupabaseServerClient() {
  const cookieStore = cookies()
  return createServerClient<Database>( // Database tipini ekleyebilirsiniz
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value
        },
        set(name: string, value: string, options) {
          try {
            cookieStore.set({ name, value, ...options })
          } catch (error) {
            // Sunucu eylemlerinde ve rota işleyicilerinde cookie'ler bu şekilde ayarlanır.
            // Middleware içinde cookie'leri ayarlamanız gerekiyorsa, lütfen createSupabaseMiddlewareClient'a bakın.
          }
        },
        remove(name: string, options) {
          try {
            cookieStore.set({ name, value: '', ...options })
          } catch (error) {
            // Sunucu eylemlerinde ve rota işleyicilerinde cookie'ler bu şekilde silinir.
            // Middleware içinde cookie'leri silmeniz gerekiyorsa, lütfen createSupabaseMiddlewareClient'a bakın.
          }
        },
      },
    }
  )
}

// İsteğe bağlı: Kullanıcı bilgilerini almak için bir yardımcı fonksiyon
export async function getUser() {
  const supabase = createSupabaseServerClient()
  try {
    const { data: { user } } = await supabase.auth.getUser()
    return user
  } catch (error) {
    console.error('Error getting user:', error)
    return null
  }
}
```

#### 1.5.3. Middleware İstemcisi (`middleware.ts`)

Next.js Middleware (`src/middleware.ts`) içinde oturum bilgilerini yenilemek ve yönetmek için kullanılır. `@supabase/ssr` paketi, middleware için özel bir istemci oluşturma yöntemi sunar.

`src/lib/supabase/middleware.ts` (veya `.js`):
```typescript
// src/lib/supabase/middleware.ts
import { createServerClient } from '@supabase/ssr'
import type { NextRequest, NextResponse } from 'next/server'
import type { Database } from './database.types' // Eğer tipleri ürettiyseniz

export async function createSupabaseMiddlewareClient(
  req: NextRequest,
  res: NextResponse
) {
  let response = NextResponse.next({ request: { headers: req.headers } })

  const supabase = createServerClient<Database>( // Database tipini ekleyebilirsiniz
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return req.cookies.get(name)?.value
        },
        set(name: string, value: string, options) {
          req.cookies.set({
            name,
            value,
            ...options,
          })
          // Middleware'de response'a cookie set etmek için bu şekilde bir yapı kullanılır.
          // Bu, auth-helpers-nextjs'in güncel sürümlerine göre değişiklik gösterebilir.
          // Dökümantasyonu kontrol etmek önemlidir.
          // Güncel @supabase/ssr kullanımı için, response objesi `createServerClient`'e doğrudan verilmez,
          // bunun yerine, middleware'den dönen `NextResponse` üzerinde cookie'ler manipüle edilir.
          // Bu örnek createServerClient'in genel yapısını gösterir, middleware'deki kullanımı için
          // src/middleware.ts dosyasına bakınız.
        },
        remove(name: string, options) {
          req.cookies.set({
            name,
            value: '',
            ...options,
          })
          // Benzer şekilde, cookie silme işlemi de response üzerinden yapılmalıdır.
        },
      },
    }
  )

  // Oturumu yenileme (Supabase Auth Helper'larının yaptığı gibi)
  // Bu, süresi dolmuşsa JWT'yi yeniler.
  const { data: { user } } = await supabase.auth.getUser()

  const { pathname } = req.nextUrl

  // Korumalı rotalar listesi
  const protectedRoutes = ['/dashboard', '/profile', '/settings'] // Örnek korumalı rotalar
  // Genel (public) rotalar
  const publicRoutes = ['/auth/login', '/auth/signup', '/auth/forgot-password', '/'] // Ana sayfa da public olabilir

  const isProtectedRoute = protectedRoutes.some(route => pathname.startsWith(route))
  const isPublicRoute = publicRoutes.some(route => pathname.startsWith(route)) || pathname === '/'
  const isAuthRoute = pathname.startsWith('/auth') && pathname !== '/auth/callback' && pathname !== '/auth/auth-code-error'

  if (isProtectedRoute && !session) {
    // Kullanıcı giriş yapmamış ve korumalı bir sayfaya erişmeye çalışıyor
    // Giriş sayfasına yönlendir, geldiği sayfayı `redirectedFrom` parametresiyle sakla
    const loginUrl = new URL('/auth/login', req.url)
    loginUrl.searchParams.set('redirectedFrom', pathname) 
    return NextResponse.redirect(loginUrl)
  }

  if (session && isAuthRoute) {
    // Kullanıcı giriş yapmış ve /auth/login, /auth/signup gibi bir kimlik doğrulama sayfasına gitmeye çalışıyor
    // Kullanıcıyı dashboard'a veya ana sayfaya yönlendir
    return NextResponse.redirect(new URL('/dashboard', req.url))
  }
  
  // Diğer durumlar için isteği olduğu gibi devam ettir
  return response
}

export const config = {
  matcher: [
    /*
     * Aşağıdakiler DIŞINDAKİ tüm istek yollarıyla eşleşir:
     * - api (API rotaları)
     * - _next/static (static dosyalar)
     * - _next/image (resim optimizasyon dosyaları)
     * - favicon.ico (favicon dosyası)
     * - Dosya uzantısı olan herhangi bir şey (örn: .png, .jpg, .svg)
     */
    '/((?!api|_next/static|_next/image|favicon.ico|.*\\.[^.]+$).*)',
  ],
}
```

**Middleware'in Açıklaması:**

1.  **Supabase İstemcisi Oluşturma:** `createServerClient` kullanılır. `@supabase/ssr` paketi, middleware'de çerezlerin nasıl yönetileceği konusunda özel bir yaklaşım sunar. İstek (`request`) çerezlerini okur ve yanıt (`response`) çerezlerini ayarlar. Bu, oturumun sunucu ve istemci arasında senkronize kalmasını sağlar.
2.  **Oturumu Alma/Yenileme:** `supabase.auth.getSession()` çağrısı, mevcut oturumu alır ve eğer token'ın süresi dolmuşsa ve yenilenebiliyorsa (refresh token ile) otomatik olarak yeniler. Güncellenmiş çerezler `response` objesine yazılır.
3.  **Rota Kontrolü:**
    *   `protectedRoutes`: Giriş yapılmasını gerektiren rotaların bir listesi.
    *   `publicRoutes`: Herkesin erişebileceği rotalar.
    *   `isAuthRoute`: `/auth/login`, `/auth/signup` gibi kimlik doğrulama ile ilgili sayfalar.
4.  **Yönlendirme Mantığı:**
    *   Eğer kullanıcı giriş yapmamışsa (`!session`) ve korumalı bir rotaya (`isProtectedRoute`) erişmeye çalışıyorsa, giriş sayfasına yönlendirilir. Kullanıcının ursprünglich gitmek istediği yol, `redirectedFrom` query parametresi olarak login URL'sine eklenir. Bu sayede başarılı girişten sonra kullanıcıyı doğru sayfaya geri yönlendirebilirsiniz.
    *   Eğer kullanıcı giriş yapmışsa (`session`) ve bir kimlik doğrulama sayfasına (`isAuthRoute`, örneğin `/auth/login`) gitmeye çalışıyorsa, genellikle bir ana sayfaya veya kullanıcı paneline (`/dashboard`) yönlendirilir.
5.  **`config.matcher`:** Middleware'in hangi yollarda çalışacağını belirler. Genellikle API rotaları, static dosyalar ve resimler gibi middleware tarafından işlenmesi gerekmeyen yollar hariç tutulur.

**Login Sayfasında Yönlendirme Parametresini Kullanma:**

`src/app/auth/login/page.tsx` içinde, başarılı girişten sonra `redirectedFrom` parametresini kontrol edip kullanıcıyı oraya yönlendirebilirsiniz:

```tsx
// src/app/auth/login/page.tsx içinde handleLogin fonksiyonu
// ...
    const { error: signInError } = await supabase.auth.signInWithPassword({
      email,
      password,
    })

    if (signInError) {
      // ... hata yönetimi ...
    } else {
      const searchParams = new URLSearchParams(window.location.search)
      const redirectedFrom = searchParams.get('redirectedFrom')
      router.push(redirectedFrom || '/dashboard') // Eğer redirectedFrom varsa oraya, yoksa dashboard'a
      // router.refresh() çağrısı burada router.push'tan sonra gerekmeyebilir,
      // çünkü push zaten yeni bir navigasyon başlatır ve sayfa güncellenir.
    }
// ...
```

### 4.2. Korumalı İçerik Gösterme

Middleware sayesinde korumalı rotalara sadece giriş yapmış kullanıcılar erişebilir. Sayfa veya bileşen içinde ek olarak kullanıcı bilgilerini alıp göstermek isteyebilirsiniz.

**Sunucu Bileşeninde Kullanıcı Bilgisi Alma:**

```tsx
// src/app/dashboard/page.tsx (Server Component)
import { createSupabaseServerClient } from "@/lib/supabase/server"
import { redirect } from "next/navigation"
import LogoutButton from "@/components/LogoutButton" // Önceki adımda oluşturmuştuk

export default async function DashboardPage() {
  const supabase = createSupabaseServerClient()
  const { data: { user } } = await supabase.auth.getUser()

  if (!session) {
    // Bu kontrol normalde middleware tarafından yapılır ama ek bir güvenlik katmanı olabilir.
    // Veya middleware'de /dashboard'a yönlendirme varsa buraya hiç gelinmez.
    redirect('/auth/login')
  }

  const { user } = session

  return (
    <div>
      <h1>Hoşgeldin, {user?.email}!</h1>
      <p>Bu senin kişisel dashboard sayfan.</p>
      <pre>{JSON.stringify(user, null, 2)}</pre>
      <LogoutButton />
    </div>
  )
}
```

**İstemci Bileşeninde Kullanıcı Bilgisi Alma:**

Bir istemci bileşeninde anlık kullanıcı durumunu almak veya değişiklikleri dinlemek için `onAuthStateChange` kullanılabilir.

```tsx
// src/components/UserProfile.tsx (Client Component)
'use client'

import { useEffect, useState } from 'react'
import { createSupabaseBrowserClient } from '@/lib/supabase/client'
import type { User } from '@supabase/supabase-js'

export default function UserProfile() {
  const supabase = createSupabaseBrowserClient()
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const getUserSession = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      setUser(session?.user ?? null)
      setLoading(false)
    }

    getUserSession()

    const { data: authListener } = supabase.auth.onAuthStateChange(
      (_event, session) => {
        setUser(session?.user ?? null)
        // Örneğin, kullanıcı giriş yapınca veya çıkış yapınca bu listener tetiklenir.
      }
    )

    return () => {
      authListener?.unsubscribe()
    }
  }, [supabase])

  if (loading) {
    return <p>Yükleniyor...</p>
  }

  if (!user) {
    return <p>Lütfen giriş yapın.</p>
  }

  return (
    <div>
      <h2>Profilim</h2>
      <p>E-posta: {user.email}</p>
      {/* Diğer kullanıcı bilgileri eklenebilir */}
    </div>
  )
}
```

## 5. Şifre Sıfırlama ve Diğer Auth İşlevleri

### 5.1. Şifremi Unuttum

Kullanıcıların şifrelerini unuttuklarında sıfırlayabilmeleri için bir mekanizma sunmalısınız.

#### 5.1.1. Şifre Sıfırlama İsteği Formu

`src/app/auth/forgot-password/page.tsx`:

```tsx
// src/app/auth/forgot-password/page.tsx
'use client'

import { createSupabaseBrowserClient } from "@/lib/supabase/client"
import { useState } from "react"

export default function ForgotPasswordPage() {
  const supabase = createSupabaseBrowserClient()
  const [email, setEmail] = useState('')
  const [message, setMessage] = useState('')
  const [error, setError] = useState('')
  const [loading, setLoading] = useState(false)

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setError('')
    setMessage('')
    setLoading(true)
    const { error: resetError } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/auth/update-password`, // Şifre güncelleme sayfasının URL'si
    })
    setLoading(false)
    if (resetError) {
      setError(resetError.message)
    } else {
      setMessage('Şifre sıfırlama talimatları e-posta adresinize gönderildi.')
    }
  }

  return (
    <div>
      <h1>Şifremi Unuttum</h1>
      <form onSubmit={handleSubmit}>
        <div>
          <label htmlFor="email">E-posta Adresiniz:</label>
          <input
            type="email"
            id="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
          />
        </div>
        {message && <p style={{ color: 'green' }}>{message}</p>}
        {error && <p style={{ color: 'red' }}>{error}</p>}
        <button type="submit" disabled={loading}>
          {loading ? 'Gönderiliyor...' : 'Sıfırlama Bağlantısı Gönder'}
        </button>
      </form>
    </div>
  )
}
```

-   `supabase.auth.resetPasswordForEmail` fonksiyonu, belirtilen e-posta adresine bir şifre sıfırlama bağlantısı gönderir.
-   `redirectTo` seçeneği, kullanıcının şifre sıfırlama bağlantısına tıkladıktan sonra yönlendirileceği (ve yeni şifresini gireceği) sayfanın URL'sini belirtir.

#### 5.1.2. Yeni Şifre Güncelleme Sayfası

Kullanıcı şifre sıfırlama e-postasındaki bağlantıya tıkladığında, bir token ile `/auth/update-password` gibi bir sayfaya yönlendirilir.

`src/app/auth/update-password/page.tsx`:

```tsx
// src/app/auth/update-password/page.tsx
'use client'

import { createSupabaseBrowserClient } from "@/lib/supabase/client"
import { useRouter, useSearchParams } from "next/navigation"
import { useEffect, useState, Suspense } from "react"
import { z } from 'zod';

const UpdatePasswordSchema = z.object({
  password: z.string().min(6, { message: 'Şifre en az 6 karakter olmalıdır.' }),
});

function UpdatePasswordForm() {
  const supabase = createSupabaseBrowserClient()
  const router = useRouter()
  const searchParams = useSearchParams()
  const [password, setPassword] = useState('')
  const [error, setError] = useState<string | null>(null)
  const [message, setMessage] = useState<string | null>(null)
  const [loading, setLoading] = useState(false)
  const [isValidToken, setIsValidToken] = useState<boolean | null>(null)

  useEffect(() => {
    // Bu sayfa yüklendiğinde URL'de bir "code" (veya Supabase'in kullandığı başka bir token) olmalı.
    // Supabase, şifre sıfırlama akışında bu token'ı (access_token olarak) hash fragment'ında gönderir.
    // Bu token'ın varlığını kontrol etmek ve gerekirse doğrulamak önemlidir.
    // `onAuthStateChange` ile `PASSWORD_RECOVERY` event'ini dinleyerek access_token'ı yakalayabiliriz.

    const { data: authListener } = supabase.auth.onAuthStateChange(async (event, session) => {
      if (event === "PASSWORD_RECOVERY") {
        setIsValidToken(true)
        // Token alındı, şifre güncelleme formu gösterilebilir.
        // Supabase client bu token'ı otomatik olarak kullanır `updateUser` için.
        if (session?.access_token) {
            // Opsiyonel: İsterseniz token'ı state'te saklayabilirsiniz ama updateUser için gerekmez.
        }
      } else if (event === "SIGNED_IN" && isValidToken) {
        // Şifre başarıyla güncellendi ve kullanıcı giriş yaptı.
        setMessage("Şifreniz başarıyla güncellendi. Yönlendiriliyorsunuz...");
        setTimeout(() => router.push('/dashboard'), 2000);
      } else if (!session && isValidToken === null) {
        // Eğer başlangıçta session yoksa ve token kontrolü yapılmadıysa, token'ı bekleyebiliriz.
        // Ancak direkt token yoksa veya geçersizse formu göstermeyebiliriz.
      }
    })

    // URL hash'inden access_token'ı kontrol et (Supabase bunu otomatik yapar onAuthStateChange ile)
    // Eğer hash fragment yoksa veya token geçersizse, isValidToken false olabilir.
    // Bu durumda kullanıcıya bir hata mesajı gösterilebilir.
    if (!window.location.hash.includes('access_token')) {
        // Bu kontrol basit bir örnektir, onAuthStateChange daha güvenilirdir.
        // setIsValidToken(false); 
    }

    return () => {
      authListener.unsubscribe()
    }
  }, [supabase, router, isValidToken])

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setError(null)
    setMessage(null)
    setLoading(true)

    const validation = UpdatePasswordSchema.safeParse({ password });
    if (!validation.success) {
      setError(validation.error.errors.map((err) => err.message).join(', '));
      setLoading(false);
      return;
    }

    // onAuthStateChange içinde yakalanan token (session.access_token) ile şifre güncellenir.
    // Supabase client, `updateUser` çağrısında bu token'ı otomatik olarak kullanır.
    const { error: updateError } = await supabase.auth.updateUser({
      password: password,
    })
    setLoading(false)

    if (updateError) {
      setError(updateError.message)
    } else {
      // Başarılı güncelleme mesajı onAuthStateChange içinde SIGNED_IN eventi ile yönetiliyor.
      // setMessage('Şifreniz başarıyla güncellendi! Giriş sayfasına yönlendiriliyorsunuz...')
      // setTimeout(() => router.push('/auth/login'), 3000)
    }
  }

  // if (isValidToken === false) {
  //   return <p>Geçersiz veya süresi dolmuş şifre sıfırlama bağlantısı.</p>;
  // }
  // if (isValidToken === null) {
  //   return <p>Token doğrulanıyor...</p>; 
  // }

  return (
    <form onSubmit={handleSubmit}>
      <h2>Yeni Şifre Oluştur</h2>
      <div>
        <label htmlFor="password">Yeni Şifre:</label>
        <input
          type="password"
          id="password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          required
        />
      </div>
      {message && <p style={{ color: 'green' }}>{message}</p>}
      {error && <p style={{ color: 'red' }}>{error}</p>}
      <button type="submit" disabled={loading || isValidToken === null || isValidToken === false}>
        {loading ? 'Güncelleniyor...' : 'Şifreyi Güncelle'}
      </button>
    </form>
  )
}

export default function UpdatePasswordPage() {
  // Suspense, useSearchParams'in Client Component'lerde doğru çalışması için gereklidir.
  return (
    <Suspense fallback={<div>Yükleniyor...</div>}>
      <UpdatePasswordForm />
    </Suspense>
  );
}

```

**Açıklamalar:**

-   Kullanıcı şifre sıfırlama linkine tıkladığında, Supabase onu `#access_token=TOKEN&refresh_token=TOKEN&...` gibi bir hash fragment içeren URL'ye yönlendirir.
-   `onAuthStateChange` listener'ı `PASSWORD_RECOVERY` event'ini dinler. Bu event tetiklendiğinde, Supabase client otomatik olarak `access_token`'ı alır ve saklar.
-   Kullanıcı yeni şifresini girip formu gönderdiğinde, `supabase.auth.updateUser({ password: newPassword })` çağrısı yapılır. Supabase client, daha önce sakladığı `access_token`'ı kullanarak bu isteği doğrular ve şifreyi günceller.
-   Başarılı güncelleme sonrası Supabase, kullanıcı için yeni bir oturum başlatır ve `SIGNED_IN` eventi tetiklenir. Bu event içinde kullanıcıyı dashboard'a veya başka bir sayfaya yönlendirebilirsiniz.
-   `useSearchParams` Client Component içinde kullanıldığında `Suspense` ile sarmalanmalıdır.
-   Token'ın geçerliliğini ve varlığını kontrol etmek önemlidir. Örnekte `isValidToken` state'i bu amaçla kullanılmaya çalışılmıştır, ancak `onAuthStateChange` genellikle bu süreci daha iyi yönetir.

### 5.2. E-posta Adresini Değiştirme

Kullanıcıların e-posta adreslerini güvenli bir şekilde değiştirmelerini sağlamak için Supabase `updateUser` ve e-posta onay akışını kullanabilirsiniz.

Supabase Auth ayarlarında "Güvenli E-posta Değişiklikleri" (Secure email change) etkinleştirilmiş olmalıdır.

```tsx
// Örnek bir profil ayarları sayfasında (Client Component)
'use client'
import { createSupabaseBrowserClient } from "@/lib/supabase/client"
import { useState } from "react"

export default function ProfileSettingsPage() {
  const supabase = createSupabaseBrowserClient()
  const [newEmail, setNewEmail] = useState('')
  const [message, setMessage] = useState('')
  const [error, setError] = useState('')

  const handleChangeEmail = async () => {
    setMessage('')
    setError('')
    const { data, error: updateError } = await supabase.auth.updateUser(
      { email: newEmail }, 
      // redirectUrl e-posta onayından sonra kullanıcıyı nereye yönlendireceğinizi belirtir.
      // { emailRedirectTo: `${window.location.origin}/profile` }
    )

    if (updateError) {
      setError(updateError.message)
    } else {
      setMessage(
        'E-posta güncelleme isteğiniz alındı. Lütfen hem eski hem de yeni e-posta adresinize gönderilen onay bağlantılarını kontrol edin.'
      )
    }
  }
  // ... form JSX ...
  return (
    <div>
      <input type="email" value={newEmail} onChange={e => setNewEmail(e.target.value)} placeholder="Yeni e-posta" />
      <button onClick={handleChangeEmail}>E-postayı Güncelle</button>
      {message && <p>{message}</p>}
      {error && <p>{error}</p>}
    </div>
  );
}
```

Kullanıcı yeni e-posta adresini girdiğinde:

1.  `supabase.auth.updateUser({ email: newEmail })` çağrılır.
2.  Supabase, hem eski hem de yeni e-posta adresine onay e-postaları gönderir.
3.  Kullanıcının her iki bağlantıya da tıklaması gerekir (veya Supabase ayarlarınıza bağlı olarak sadece yeni e-postaya).
4.  Onaylar tamamlandığında e-posta adresi güncellenir.

### 5.3. Magic Link ile Giriş (Parolasız)

Kullanıcıların şifre kullanmadan sadece e-postalarına gönderilen bir bağlantıya tıklayarak giriş yapmalarını sağlar.

`src/app/auth/magic-login/page.tsx`:

```tsx
// src/app/auth/magic-login/page.tsx
'use client'

import { createSupabaseBrowserClient } from "@/lib/supabase/client"
import { useState } from "react"

export default function MagicLoginPage() {
  const supabase = createSupabaseBrowserClient()
  const [email, setEmail] = useState('')
  const [message, setMessage] = useState('')
  const [error, setError] = useState('')
  const [loading, setLoading] = useState(false)

  const handleMagicLogin = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setError('')
    setMessage('')
    setLoading(true)

    const { error: signInError } = await supabase.auth.signInWithOtp({
      email: email,
      options: {
        // Kullanıcının e-postadaki bağlantıya tıkladıktan sonra yönlendirileceği URL.
        // Bu URL'de /auth/callback gibi bir handler olmalı ve exchangeCodeForSession yapmalı.
        emailRedirectTo: `${window.location.origin}/auth/callback`,
      },
    })
    setLoading(false)

    if (signInError) {
      setError(signInError.message)
    } else {
      setMessage('Giriş bağlantısı e-posta adresinize gönderildi. Lütfen kontrol edin.')
    }
  }

  return (
    <div>
      <h1>Magic Link ile Giriş</h1>
      <form onSubmit={handleMagicLogin}>
        <div>
          <label htmlFor="email">E-posta Adresiniz:</label>
          <input
            type="email"
            id="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
          />
        </div>
        {message && <p style={{ color: 'green' }}>{message}</p>}
        {error && <p style={{ color: 'red' }}>{error}</p>}
        <button type="submit" disabled={loading}>
          {loading ? 'Gönderiliyor...' : 'Giriş Bağlantısı Gönder'}
        </button>
      </form>
    </div>
  )
}
```

-   `supabase.auth.signInWithOtp` fonksiyonu, belirtilen sağlayıcı ile OAuth akışını başlatır.
-   `options.redirectTo`: Kullanıcının OAuth sağlayıcısında kimliğini doğruladıktan sonra yönlendirileceği URL'dir. Bu, daha önce e-posta onayı için kullandığımız `/auth/callback` Route Handler'ı olabilir. Bu handler, OAuth callback'lerini de işleyebilecek şekilde (genellikle Supabase bunu otomatik yapar) tasarlanmıştır.
-   OAuth akışı tamamlandığında (başarılı veya hatalı), kullanıcı `redirectTo` ile belirtilen URL'ye yönlendirilir. `/auth/callback` rotası, URL'deki kodu veya hatayı işleyerek oturumu başlatır veya hata yönetimi yapar.

Bu temel akışlar, uygulamanız için sağlam bir kimlik doğrulama temeli oluşturur. 

## 6. Sosyal Medya ile Giriş (OAuth)

Supabase, Google, GitHub, Facebook, Apple gibi popüler sosyal medya sağlayıcıları ile OAuth 2.0 entegrasyonunu kolaylaştırır.

### 6.1. Supabase Dashboard'da OAuth Sağlayıcısını Yapılandırma

1.  **Sağlayıcı Seçimi:** Supabase projenizin Authentication > Providers bölümünden kullanmak istediğiniz OAuth sağlayıcısını (örn: Google) seçin.
2.  **Client ID ve Client Secret:** Sağlayıcıyı etkinleştirmek için sizden bir `Client ID` ve `Client Secret` isteyecektir. Bu bilgileri ilgili OAuth sağlayıcısının geliştirici konsolundan (örn: Google Cloud Console, GitHub Developer Settings) almanız gerekir.
    *   Her sağlayıcının kendi arayüzü ve adımları vardır. Genellikle yeni bir OAuth uygulaması oluşturmanız, uygulamanızın adını, logosunu ve **yetkili yönlendirme URI'lerini (authorized redirect URIs)** belirtmeniz istenir.
    *   **Yetkili Yönlendirme URI'si:** Supabase, her sağlayıcı için kullanmanız gereken spesifik bir yönlendirme URI'si formatı sunar. Bu genellikle şuna benzer: `https://<PROJE_REF>.supabase.co/auth/v1/callback`.
    *   Bu URI'yi OAuth sağlayıcınızın ayarlarına doğru bir şekilde eklediğinizden emin olun.
3.  **Supabase'e Kaydetme:** Aldığınız Client ID ve Client Secret'ı Supabase dashboard'daki ilgili alanlara girip kaydedin.
4.  **URL Yapılandırması:** Supabase Authentication > URL Configuration bölümünde **Site URL**'inizin doğru ayarlandığından ve uygulamanızın çalışacağı **Ek Yönlendirme URL'leri** (örn: `http://localhost:3000/auth/callback` geliştirme için, `https://yourdomain.com/auth/callback` üretim için) eklediğinizden emin olun. OAuth sağlayıcıları, kullanıcıyı sadece bu beyaz listeye alınmış URL'lere geri yönlendirir.

### 6.2. OAuth Giriş Düğmesi Oluşturma

Kullanıcı arayüzünüzde sosyal medya ile giriş yapmak için bir düğme oluşturun.

`src/app/auth/login/page.tsx` veya ayrı bir bileşende:

```tsx
// src/components/OAuthButtons.tsx
'use client'

import { createSupabaseBrowserClient } from "@/lib/supabase/client"
import { FaGoogle, FaGithub } from 'react-icons/fa' // Örnek ikonlar için

export default function OAuthButtons() {
  const supabase = createSupabaseBrowserClient()

  const handleOAuthLogin = async (provider: 'google' | 'github') => {
    const redirectTo = `${window.location.origin}/auth/callback`
    const { error } = await supabase.auth.signInWithOAuth({
      provider: provider,
      options: {
        redirectTo: redirectTo,
        // Gerekirse ek kapsamlar (scopes) isteyebilirsiniz:
        // scopes: 'email profile',
      },
    })

    if (error) {
      console.error(`Error logging in with ${provider}:`, error.message)
      // Kullanıcıya hata mesajı gösterilebilir
    }
    // Başarılı olursa, kullanıcı OAuth sağlayıcısının sayfasına yönlendirilir.
    // Geri dönüş /auth/callback rotası üzerinden olur.
  }

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '10px', marginTop: '20px' }}>
      <button onClick={() => handleOAuthLogin('google')} style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
        <FaGoogle /> Google ile Giriş Yap
      </button>
      <button onClick={() => handleOAuthLogin('github')} style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
        <FaGithub /> GitHub ile Giriş Yap
      </button>
      {/* Diğer sağlayıcılar için butonlar eklenebilir */}
    </div>
  )
}
```

**Açıklamalar:**

-   `supabase.auth.signInWithOAuth` fonksiyonu, belirtilen sağlayıcı ile OAuth akışını başlatır.
-   `options.redirectTo`: Kullanıcının OAuth sağlayıcısında kimliğini doğruladıktan sonra yönlendirileceği URL'dir. Bu, daha önce e-posta onayı için kullandığımız `/auth/callback` Route Handler'ı olabilir. Bu handler, OAuth callback'lerini de işleyebilecek şekilde (genellikle Supabase bunu otomatik yapar) tasarlanmıştır.
-   OAuth akışı tamamlandığında (başarılı veya hatalı), kullanıcı `redirectTo` ile belirtilen URL'ye yönlendirilir. `/auth/callback` rotası, URL'deki kodu veya hatayı işleyerek oturumu başlatır veya hata yönetimi yapar.

### 6.3. OAuth Callback İşleme

`/auth/callback/route.ts` dosyamız zaten `exchangeCodeForSession` ile genel token değişimini yaptığı için OAuth callback'lerini de büyük ihtimalle doğru bir şekilde işleyecektir. Supabase, farklı akışlardan (e-posta onayı, şifre sıfırlama, OAuth) gelen token'ları benzer şekilde yönetir.

Eğer OAuth sağlayıcısından dönen `code` farklı bir işlem gerektiriyorsa, `exchangeCodeForSession` bunu genellikle halleder. Önemli olan, Supabase dashboard'unda ve OAuth sağlayıcınızın ayarlarında yönlendirme URL'lerinin doğru yapılandırılmış olmasıdır.

## 7. İleri Düzey Konular

### 7.1. Çok Faktörlü Kimlik Doğrulama (MFA/2FA)

Supabase, TOTP (Time-based One-Time Password) tabanlı MFA'yı destekler. Bu, kullanıcıların şifrelerine ek olarak bir kimlik doğrulama uygulamasından (örn: Google Authenticator, Authy) aldıkları kodu girmelerini gerektirir.

1.  **MFA'yı Etkinleştirme (Kullanıcı Başına):**
    Kullanıcı giriş yaptıktan sonra, MFA'yı etkinleştirmek için bir arayüz sunabilirsiniz.

    ```typescript
    // Kullanıcının MFA'yı kaydetmesi (enroll)
    const { data, error } = await supabase.auth.mfa.enroll({ factorType: 'totp' })
    if (error) throw error
    // data.totp.qr_code -> Bu QR kodunu kullanıcıya gösterin (kimlik doğrulama uygulaması ile taraması için)
    // data.totp.secret -> QR kodu gösterilemiyorsa bu secret'ı kullanıcıya verin
    ```

2.  **MFA'yı Doğrulama (Challenge & Verify):**
    Kullanıcı QR kodu tarayıp secret'ı kaydettikten sonra, bir doğrulama kodu girmesi gerekir.

    ```typescript
    // Kullanıcıdan alınan TOTP kodunu doğrulama
    const verificationCode = '123456' // Kullanıcının girdiği kod
    const factorId = data.id // enroll işleminden dönen faktör ID'si

    const { error: challengeError } = await supabase.auth.mfa.challenge({ factorId })
    if (challengeError) throw challengeError

    const { error: verifyError } = await supabase.auth.mfa.verify({
      factorId,
      code: verificationCode,
      challengeId: supabase.auth.mfa.getAuthenticatorAssuranceLevel().currentChallengeId // veya challenge'dan dönen ID
    })
    if (verifyError) throw verifyError
    // MFA başarıyla doğrulandı ve etkinleştirildi.
    ```

3.  **MFA ile Giriş:**
    MFA etkinleştirilmiş bir kullanıcı şifresiyle giriş yaptığında, Supabase bir sonraki adımda MFA kodu ister. `onAuthStateChange` veya `getSession` ile `aal` (Authenticator Assurance Level) değerini kontrol ederek MFA'nın gerekip gerekmediğini anlayabilirsiniz. Eğer `aal` `aal1` ise (sadece şifre) ve kullanıcı MFA etkinleştirmişse, `supabase.auth.mfa.challengeAndVerify` veya ayrı ayrı challenge ve verify adımlarını uygulamanız gerekir.

### 7.2. Kullanıcı Rolleri ve İzinler (RLS ile)

Veritabanınızda kullanıcı rollerini (örn: `admin`, `editor`, `member`) tanımlayabilir ve RLS politikalarını bu rollere göre yazabilirsiniz.

-   `users` tablonuza veya ayrı bir `user_roles` tablosuna rol bilgisini ekleyin.
-   RLS politikalarınızda `auth.jwt()->>'user_role'` (eğer JWT'ye rolü eklediyseniz) veya `(SELECT role FROM user_profiles WHERE id = auth.uid())` gibi ifadelerle kullanıcının rolünü kontrol edin.

### 7.3. JWT (JSON Web Token) Yönetimi

Supabase, oturumları yönetmek için JWT kullanır.

-   **Token Alma:** `supabase.auth.getSession()` ile mevcut oturumun `access_token` ve `refresh_token`'ını alabilirsiniz.
-   **Özel Claim Ekleme:** Supabase Function'ları (Edge Functions) kullanarak JWT'ye özel claim'ler (örneğin kullanıcı rolleri) ekleyebilirsiniz. Bu, genellikle bir "Token Hook" fonksiyonu ile yapılır.
-   **Token Süreleri:** Supabase dashboard'undan JWT ve refresh token sürelerini yapılandırabilirsiniz.

### 7.4. Oturum Süresi Yönetimi ve Otomatik Çıkış

-   JWT süreleri ve refresh token mekanizması, oturumların güvenli bir şekilde yönetilmesini sağlar.
-   İstemci tarafında, belirli bir inaktivite süresi sonunda kullanıcıyı otomatik olarak çıarmak için ek mantık yazabilirsiniz.

## 8. Güvenlik En İyi Pratikleri ve Özet

-   **Her Zaman HTTPS Kullanın:** Tüm iletişim şifrelenmelidir.
-   **Güçlü Şifre Politikaları:** Supabase varsayılan olarak bazı kontroller yapar, ancak kullanıcı arayüzünde de kullanıcıları güçlü şifreler oluşturmaya teşvik edin.
-   **E-posta Onayını Kullanın:** Sahte hesapları ve spam'i azaltır.
-   **RLS'yi Etkinleştirin ve Dikkatlice Yapılandırın:** Veritabanı erişimini sıkı bir şekilde kontrol edin.
-   **Rate Limiting ve Kötüye Kullanım Korumasını Kullanın:** Supabase dashboard'daki ayarları kontrol edin.
-   **Güvenli Yönlendirme URL'leri:** Sadece güvendiğiniz URL'leri beyaz listeye alın.
-   **CSRF Koruması:** Next.js genellikle Server Actions ve modern form gönderim yöntemleriyle CSRF'ye karşı koruma sağlar. Ancak API rotaları kullanıyorsanız ek önlemler gerekebilir.
-   **XSS Koruması:** React (Next.js) varsayılan olarak XSS'e karşı koruma sağlar, ancak `dangerouslySetInnerHTML` gibi özelliklerden kaçının ve kullanıcı girdilerini her zaman sanitize edin (özellikle HTML olarak render edilecekse).
-   **Bağımlılıkları Güncel Tutun:** `@supabase/ssr`, `next` ve diğer tüm paketleri düzenli olarak güncelleyin.
-   **Hata Yönetimi:** Kullanıcıya anlaşılır hata mesajları gösterin, ancak hassas bilgileri ifşa etmeyin.
-   **Loglama:** Önemli kimlik doğrulama olaylarını (başarılı/başarısız girişler, şifre değişiklikleri) loglayın.

Bu rehber, NextJS ve Supabase ile kapsamlı bir kimlik doğrulama sistemi kurmanız için size sağlam bir temel sunmuştur. Her uygulamanın kendine özgü gereksinimleri olacağından, bu temelleri kendi projenize göre uyarlamanız ve Supabase ile Next.js dokümanlarını düzenli olarak takip etmeniz önemlidir.

İyi kodlamalar!

### 3.4. Server Actions ile Kimlik Doğrulama Formları (Alternatif Yaklaşım)

Next.js Server Actions, form gönderimlerini ve veri mutasyonlarını doğrudan sunucu tarafında çalıştırmanın modern bir yoludur. Bu, istemci tarafı JavaScript'i azaltabilir ve formların progresif olarak geliştirilmesine olanak tanır.

#### 3.4.1. Kayıt Olma (Sign Up) için Server Action

Öncelikle, bir `actions.ts` dosyası oluşturalım (örneğin, `src/app/auth/actions.ts` içinde veya doğrudan sayfa bileşeninin Server Action'ı olarak).

`src/app/auth/actions.ts`:
```typescript
// src/app/auth/actions.ts
'use server'

import { createSupabaseServerClient } from '@/lib/supabase/server'
import { z } from 'zod'
import { redirect } from 'next/navigation'
import { headers } from 'next/headers' // Origin almak için

const SignUpSchema = z.object({
  email: z.string().email({ message: 'Geçerli bir e-posta adresi giriniz.' }),
  password: z.string().min(6, { message: 'Şifre en az 6 karakter olmalıdır.' }),
})

export async function signUpAction(prevState: any, formData: FormData) {
  const email = formData.get('email') as string
  const password = formData.get('password') as string

  const validation = SignUpSchema.safeParse({ email, password })

  if (!validation.success) {
    return {
      message: 'Geçersiz form verileri.',
      errors: validation.error.flatten().fieldErrors,
      fieldValues: { email, password: '' }
    }
  }

  const supabase = createSupabaseServerClient()
  const origin = headers().get('origin') // E-posta yönlendirmesi için origin'i al

  const { error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      // E-posta onayı için yönlendirme.
      // Callback URL'niz Supabase projenizin Auth ayarlarında beyaz listeye alınmış olmalı.
      emailRedirectTo: `${origin}/auth/callback`,
    },
  })

  if (error) {
    return {
      message: error.message === 'User already registered' 
        ? 'Bu e-posta adresi zaten kayıtlı.'
        : 'Kayıt sırasında bir hata oluştu: ' + error.message,
      errors: null,
      fieldValues: { email, password: '' }
    }
  }

  // Başarılı kayıt sonrası kullanıcıyı bir bilgilendirme sayfasına yönlendirebilir
  // veya doğrudan e-posta onayı için mesaj gösterebiliriz.
  // redirect('/auth/pending-confirmation'); // Örnek yönlendirme
  return {
    message: 'Kayıt başarılı! Lütfen e-posta adresinize gönderilen onay bağlantısını kontrol edin.',
    errors: null,
    fieldValues: { email: '', password: '' } // Formu temizle
  }
}
```

Kayıt formu (`src/app/auth/signup/page.tsx`) şimdi `useActionState` hook'unu kullanabilir:

```tsx
// src/app/auth/signup/page.tsx
'use client'

import { useActionState, useEffect } from 'react'
import { signUpAction } from './actions' // Server Action'ı import et
import Link from 'next/link'

export default function SignUpServerActionPage() {
  const initialState = { message: null, errors: null, fieldValues: { email: '', password: '' } }
  const [state, formAction, pending] = useActionState(signUpAction, initialState)

  useEffect(() => {
    if (state.message && !state.errors) {
      // Başarılı mesajı bir toast ile gösterebilir veya formu resetleyebilirsiniz.
      // Form alanları state.fieldValues ile zaten güncelleniyor.
      console.log(state.message);
    }
  }, [state]);

  return (
    <div>
      <h1>Kayıt Ol (Server Action)</h1>
      <form action={formAction}>
        <div>
          <label htmlFor="email">E-posta:</label>
          <input
            type="email"
            id="email"
            name="email" // Server Action için name attribute önemli
            defaultValue={state.fieldValues?.email}
            required
          />
          {state.errors?.email && (
            <p style={{ color: 'red' }}>{state.errors.email.join(', ')}</p>
          )}
        </div>
        <div>
          <label htmlFor="password">Şifre:</label>
          <input
            type="password"
            id="password"
            name="password" // Server Action için name attribute önemli
            defaultValue={state.fieldValues?.password}
            required
          />
          {state.errors?.password && (
            <p style={{ color: 'red' }}>{state.errors.password.join(', ')}</p>
          )}
        </div>
        
        {state.message && !state.errors && <p style={{ color: 'green' }}>{state.message}</p>}
        {state.message && state.errors && <p style={{ color: 'red' }}>{state.message}</p>}

        <button type="submit" disabled={pending}>
          {pending ? 'Kaydediliyor...' : 'Kayıt Ol'}
        </button>
      </form>
      <p>
        Zaten bir hesabınız var mı? <Link href="/auth/login">Giriş Yap</Link>
      </p>
    </div>
  )
}
```

#### 3.4.2. Giriş Yapma (Sign In) için Server Action

`src/app/auth/actions.ts` dosyasına giriş için bir Server Action ekleyelim:

```typescript
// src/app/auth/actions.ts (devamı)
'use server' // Dosyanın başında zaten var

// ... (signUpAction ve Zod şemaları)

import { revalidatePath } from 'next/cache' // Gerekirse

const LoginSchema = z.object({
  email: z.string().email({ message: 'Geçerli bir e-posta adresi giriniz.' }),
  password: z.string().min(1, { message: 'Şifre boş olamaz.' }),
})

export async function signInAction(prevState: any, formData: FormData) {
  const email = formData.get('email') as string
  const password = formData.get('password') as string

  const validation = LoginSchema.safeParse({ email, password })

  if (!validation.success) {
    return {
      message: 'Geçersiz form verileri.',
      errors: validation.error.flatten().fieldErrors,
      fieldValues: { email, password: '' }
    }
  }

  const supabase = createSupabaseServerClient()
  const { error } = await supabase.auth.signInWithPassword({
    email,
    password,
  })

  if (error) {
    let userMessage = 'Giriş sırasında bir hata oluştu.';
    if (error.message === 'Invalid login credentials') {
      userMessage = 'Geçersiz e-posta veya şifre.';
    } else if (error.message === 'Email not confirmed') {
      userMessage = 'E-posta adresiniz henüz onaylanmamış. Lütfen e-postanızı kontrol edin.';
    }
    return {
      message: userMessage,
      errors: null, // Genel bir mesaj olduğu için spesifik alan hatası yok
      fieldValues: { email, password: '' }
    }
  }

  // Başarılı giriş sonrası. Middleware zaten korumalı sayfalara yönlendirme yapacaktır.
  // Ancak, burada revalidatePath ile bazı yolları yeniden doğrulamak veya
  // doğrudan redirect ile kullanıcıyı belirli bir sayfaya göndermek isteyebilirsiniz.
  // Middleware'in çalışması için genellikle bir sonraki navigasyonda session güncellenir.
  // Oturumun hemen yansıması için redirect gerekebilir.
  
  // Örneğin, /dashboard yolunu yeniden doğrula ve kullanıcıyı oraya yönlendir.
  // revalidatePath('/dashboard') // Eğer /dashboard cachelenmişse ve güncel veri gerekiyorsa
  redirect('/dashboard') // Kullanıcıyı dashboard'a yönlendir
}
```

Giriş formu (`src/app/auth/login/page.tsx`) da `useActionState` kullanabilir:

```tsx
// src/app/auth/login/page.tsx
'use client'

import { useActionState, useEffect } from 'react'
import { signInAction } from './actions' // Server Action'ı import et
import Link from 'next/link'
import { useRouter, useSearchParams } from 'next/navigation'


export default function LoginServerActionPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const redirectedFrom = searchParams.get('redirectedFrom');

  const initialState = { message: null, errors: null, fieldValues: { email: '', password: '' } };
  // signInAction'ın başarılı olması durumunda redirect yapacağı için,
  // bu bileşende başarılı mesajı göstermeyeceğiz, yönlendirme olacak.
  const [state, formAction, pending] = useActionState(
    async (prevState: any, formData: FormData) => {
      const result = await signInAction(prevState, formData);
      // signInAction redirect yaptığı için burası normalde çalışmaz
      // ama eğer redirect olmazsa (örneğin hata durumunda), state güncellenir.
      if (result?.message && result.errors === null && !result.message.includes('hata')) {
         // Bu blok normalde signInAction'daki redirect nedeniyle çalışmayacak.
         // Eğer bir şekilde çalışırsa ve başarılıysa yönlendirme yap.
        router.push(redirectedFrom || '/dashboard');
      }
      return result;
    },
    initialState
  );
  
  // Eğer Server Action doğrudan redirect yapıyorsa, useEffect'e gerek kalmaz.
  // Hata mesajları state üzerinden gösterilir.
  // useEffect(() => {
  //   if (state.message && state.errors === null && !state.message.includes('hata')) {
  //     // Başarılı giriş sonrası yönlendirme Server Action içinde yapılıyor.
  //     // router.push(redirectedFrom || '/dashboard');
  //   }
  // }, [state, router, redirectedFrom]);

  return (
    <div>
      <h1>Giriş Yap (Server Action)</h1>
      <form action={formAction}>
        <div>
          <label htmlFor="email">E-posta:</label>
          <input
            type="email"
            id="email"
            name="email"
            defaultValue={state.fieldValues?.email}
            required
          />
          {state.errors?.email && (
             <p style={{ color: 'red' }}>{state.errors.email.join(', ')}</p>
          )}
        </div>
        <div>
          <label htmlFor="password">Şifre:</label>
          <input
            type="password"
            id="password"
            name="password"
            defaultValue={state.fieldValues?.password}
            required
          />
           {state.errors?.password && (
             <p style={{ color: 'red' }}>{state.errors.password.join(', ')}</p>
          )}
        </div>

        {state.message && <p style={{ color: state.errors || state.message.includes('hata') ? 'red' : 'green' }}>{state.message}</p>}
        
        <button type="submit" disabled={pending}>
          {pending ? 'Giriş Yapılıyor...' : 'Giriş Yap'}
        </button>
      </form>
      <p>
        Hesabınız yok mu? <Link href="/auth/signup">Kayıt Ol</Link>
      </p>
      <p>
        <Link href="/auth/forgot-password">Şifremi Unuttum</Link>
      </p>
    </div>
  )
}
```

**Server Actions Kullanmanın Avantajları:**

-   **Azaltılmış İstemci Tarafı Kod:** Kimlik doğrulama mantığının büyük kısmı sunucuya taşınır.
-   **Progresif İyileştirme:** JavaScript devre dışı bırakılsa bile formlar çalışmaya devam edebilir (Next.js bunu yönetir).
-   **Güvenlik:** Hassas işlemler sunucuda kalır.
-   **Daha Basit Durum Yönetimi:** `useActionState` hook'u ile form durumları (bekleme, hata, veri) kolayca yönetilir.

Bu Server Action örnekleri, rehberdeki istemci tarafı form yönetimi örneklerine bir alternatif olarak sunulmuştur. Her iki yaklaşımın da kendi kullanım senaryoları ve avantajları vardır.

Bu temel akışlar, uygulamanız için sağlam bir kimlik doğrulama temeli oluşturur. 
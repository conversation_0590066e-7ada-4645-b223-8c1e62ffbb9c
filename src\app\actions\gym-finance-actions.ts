"use server";

import { createAction } from "@/lib/actions/core";
import { ApiResponse } from "@/lib/actions/types";
import { Gyms, Tables } from "@/lib/supabase/types";

interface GymFinanceData {
  totalRevenue: number;
  monthlyRevenue: number;
  pendingPayments: number;
  completedPayments: number;
  revenueGrowth: number;
  paymentGrowth: number;
  totalMembers: number;
  activeMembers: number;
  averageRevenuePerMember: number;
  monthlyData: {
    month: string;
    revenue: number;
    payments: number;
    newMembers: number;
  }[];
  packageRevenue: {
    packageName: string;
    revenue: number;
    memberCount: number;
    percentage: number;
  }[];
  recentTransactions: {
    id: string;
    memberName: string;
    amount: number;
    status: "completed" | "pending" | "failed";
    date: string;
    packageName: string;
    paymentMethod: string;
  }[];
}

/**
 * Belirli bir salon için finansal özet verilerini getirir
 */
export async function getGymFinanceData(
  gymId: string
): Promise<ApiResponse<GymFinanceData>> {
  return await createAction(
    async (_, supabase) => {
      // Salon bilgilerini kontrol et
      const { data: gym, error: gymError } = await supabase
        .from("gyms")
        .select("id, name, manager_user_id")
        .eq("id", gymId)
        .single();

      if (gymError || !gym) {
        throw new Error("Salon bulunamadı");
      }

      // Mevcut tarih bilgileri
      const now = new Date();
      const currentMonth = now.getMonth();
      const currentYear = now.getFullYear();
      const lastMonth = currentMonth === 0 ? 11 : currentMonth - 1;
      const lastMonthYear = currentMonth === 0 ? currentYear - 1 : currentYear;

      // Bu ayın başlangıcı ve bitişi
      const thisMonthStart = new Date(
        currentYear,
        currentMonth,
        1
      ).toISOString();
      const thisMonthEnd = new Date(
        currentYear,
        currentMonth + 1,
        0,
        23,
        59,
        59
      ).toISOString();

      // Geçen ayın başlangıcı ve bitişi
      const lastMonthStart = new Date(
        lastMonthYear,
        lastMonth,
        1
      ).toISOString();
      const lastMonthEnd = new Date(
        lastMonthYear,
        lastMonth + 1,
        0,
        23,
        59,
        59
      ).toISOString();

      // Salon üyeliklerini al
      const { data: memberships, error: membershipsError } = await supabase
        .from("memberships")
        .select(
          `
        id,
        user_id,
        status,
        created_at,
        users!inner(name, surname)
      `
        )
        .eq("gym_id", gymId);

      if (membershipsError) {
        throw new Error(
          `Üyelikler alınırken hata: ${membershipsError.message}`
        );
      }

      // Salon aboneliklerini al (gelir hesaplaması için)
      const { data: subscriptions, error: subscriptionsError } = await supabase
        .from("subscriptions")
        .select(
          `
        id,
        membership_id,
        gym_package_id,
        start_date,
        end_date,
        payment_status,
        purchase_price,
        created_at,
        gym_packages!inner(name, price_amount),
        memberships!inner(
          user_id,
          gym_id,
          users!inner(name, surname)
        )
      `
        )
        .eq("memberships.gym_id", gymId);

      if (subscriptionsError) {
        throw new Error(
          `Abonelikler alınırken hata: ${subscriptionsError.message}`
        );
      }

      // Toplam gelir hesaplama
      const completedSubscriptions =
        subscriptions?.filter(
          (sub: any) => sub.payment_status === "completed"
        ) || [];
      const pendingSubscriptions =
        subscriptions?.filter((sub: any) => sub.payment_status === "pending") ||
        [];

      const totalRevenue = completedSubscriptions.reduce(
        (sum: number, sub: any) => sum + (sub.purchase_price || 0),
        0
      );
      const pendingPayments = pendingSubscriptions.reduce(
        (sum: number, sub: any) => sum + (sub.purchase_price || 0),
        0
      );

      // Bu ay geliri
      const thisMonthSubscriptions = completedSubscriptions.filter(
        (sub: any) =>
          sub.created_at >= thisMonthStart && sub.created_at <= thisMonthEnd
      );
      const monthlyRevenue = thisMonthSubscriptions.reduce(
        (sum: number, sub: any) => sum + (sub.purchase_price || 0),
        0
      );

      // Geçen ay geliri (büyüme hesaplaması için)
      const lastMonthSubscriptions = completedSubscriptions.filter(
        (sub: any) =>
          sub.created_at >= lastMonthStart && sub.created_at <= lastMonthEnd
      );
      const lastMonthRevenue = lastMonthSubscriptions.reduce(
        (sum: number, sub: any) => sum + (sub.purchase_price || 0),
        0
      );

      // Büyüme oranları
      const revenueGrowth =
        lastMonthRevenue > 0
          ? ((monthlyRevenue - lastMonthRevenue) / lastMonthRevenue) * 100
          : monthlyRevenue > 0
          ? 100
          : 0;

      const paymentGrowth =
        lastMonthSubscriptions.length > 0
          ? ((thisMonthSubscriptions.length - lastMonthSubscriptions.length) /
              lastMonthSubscriptions.length) *
            100
          : thisMonthSubscriptions.length > 0
          ? 100
          : 0;

      // Üye istatistikleri
      const totalMembers = memberships?.length || 0;
      const activeMembers =
        memberships?.filter((m: any) => m.status === "active").length || 0;
      const averageRevenuePerMember =
        activeMembers > 0 ? totalRevenue / activeMembers : 0;

      // Son 3 ayın verileri
      const monthlyData = [];
      for (let i = 2; i >= 0; i--) {
        const monthDate = new Date(currentYear, currentMonth - i, 1);
        const monthStart = new Date(
          monthDate.getFullYear(),
          monthDate.getMonth(),
          1
        ).toISOString();
        const monthEnd = new Date(
          monthDate.getFullYear(),
          monthDate.getMonth() + 1,
          0,
          23,
          59,
          59
        ).toISOString();

        const monthSubscriptions = completedSubscriptions.filter(
          (sub: any) =>
            sub.created_at >= monthStart && sub.created_at <= monthEnd
        );

        const monthMemberships =
          memberships?.filter(
            (m: any) => m.created_at >= monthStart && m.created_at <= monthEnd
          ) || [];

        monthlyData.push({
          month: monthDate.toLocaleDateString("tr-TR", { month: "long" }),
          revenue: monthSubscriptions.reduce(
            (sum: number, sub: any) => sum + (sub.purchase_price || 0),
            0
          ),
          payments: monthSubscriptions.length,
          newMembers: monthMemberships.length,
        });
      }

      // Paket bazında gelir analizi
      const packageRevenueMap = new Map();
      completedSubscriptions.forEach((sub: any) => {
        const packageName = sub.gym_packages?.name || "Bilinmeyen Paket";
        const current = packageRevenueMap.get(packageName) || {
          revenue: 0,
          memberCount: 0,
        };
        packageRevenueMap.set(packageName, {
          revenue: current.revenue + (sub.purchase_price || 0),
          memberCount: current.memberCount + 1,
        });
      });

      const packageRevenue = Array.from(packageRevenueMap.entries())
        .map(([packageName, data]) => ({
          packageName,
          revenue: data.revenue,
          memberCount: data.memberCount,
          percentage:
            totalRevenue > 0 ? (data.revenue / totalRevenue) * 100 : 0,
        }))
        .sort((a, b) => b.revenue - a.revenue);

      // Son işlemler (son 10 abonelik)
      const recentTransactions =
        subscriptions?.slice(0, 10).map((sub: any) => ({
          id: sub.id,
          memberName: `${sub.memberships?.users?.name} ${sub.memberships?.users?.surname}`,
          amount: sub.purchase_price || 0,
          status: sub.payment_status as "completed" | "pending" | "failed",
          date: sub.created_at,
          packageName: sub.gym_packages?.name || "Bilinmeyen Paket",
          paymentMethod: "Kredi Kartı", // Mock data - gerçek implementasyonda payment_method field'ı olacak
        })) || [];

      const financeData: GymFinanceData = {
        totalRevenue,
        monthlyRevenue,
        pendingPayments,
        completedPayments: totalRevenue,
        revenueGrowth,
        paymentGrowth,
        totalMembers,
        activeMembers,
        averageRevenuePerMember,
        monthlyData,
        packageRevenue,
        recentTransactions,
      };

      return financeData;
    },
    { requireAuth: true }
  );
}

/**
 * Salon için ödeme durumu istatistiklerini getirir
 */
export async function getGymPaymentStats(gymId: string): Promise<
  ApiResponse<{
    totalPayments: number;
    completedPayments: number;
    pendingPayments: number;
    failedPayments: number;
    completionRate: number;
  }>
> {
  return await createAction(
    async (_, supabase) => {
      const { data: subscriptions, error } = await supabase
        .from("subscriptions")
        .select("payment_status")
        .eq("gym_id", gymId);

      if (error) {
        throw new Error(
          `Ödeme istatistikleri alınırken hata: ${error.message}`
        );
      }

      const totalPayments = subscriptions?.length || 0;
      const completedPayments =
        subscriptions?.filter((s: any) => s.payment_status === "completed")
          .length || 0;
      const pendingPayments =
        subscriptions?.filter((s: any) => s.payment_status === "pending")
          .length || 0;
      const failedPayments =
        subscriptions?.filter((s: any) => s.payment_status === "failed")
          .length || 0;
      const completionRate =
        totalPayments > 0 ? (completedPayments / totalPayments) * 100 : 0;

      return {
        totalPayments,
        completedPayments,
        pendingPayments,
        failedPayments,
        completionRate,
      };
    },
    { requireAuth: true }
  );
}

/**
 * Salon için gelir trendini getirir (son 6 ay)
 */
export async function getGymRevenueTrend(gymId: string): Promise<
  ApiResponse<
    {
      month: string;
      revenue: number;
      memberCount: number;
    }[]
  >
> {
  return await createAction(
    async (_, supabase) => {
      const now = new Date();
      const sixMonthsAgo = new Date(now.getFullYear(), now.getMonth() - 5, 1);

      const { data: subscriptions, error } = await supabase
        .from("subscriptions")
        .select("created_at, purchase_price, payment_status")
        .eq("gym_id", gymId)
        .eq("payment_status", "completed")
        .gte("created_at", sixMonthsAgo.toISOString());

      if (error) {
        throw new Error(`Gelir trendi alınırken hata: ${error.message}`);
      }

      const { data: memberships, error: membershipError } = await supabase
        .from("memberships")
        .select("created_at")
        .eq("gym_id", gymId)
        .gte("created_at", sixMonthsAgo.toISOString());

      if (membershipError) {
        throw new Error(
          `Üyelik trendi alınırken hata: ${membershipError.message}`
        );
      }

      const trendData = [];
      for (let i = 5; i >= 0; i--) {
        const monthDate = new Date(now.getFullYear(), now.getMonth() - i, 1);
        const monthStart = new Date(
          monthDate.getFullYear(),
          monthDate.getMonth(),
          1
        ).toISOString();
        const monthEnd = new Date(
          monthDate.getFullYear(),
          monthDate.getMonth() + 1,
          0,
          23,
          59,
          59
        ).toISOString();

        const monthSubscriptions =
          subscriptions?.filter(
            (sub: any) =>
              sub.created_at >= monthStart && sub.created_at <= monthEnd
          ) || [];

        const monthMemberships =
          memberships?.filter(
            (m: any) => m.created_at >= monthStart && m.created_at <= monthEnd
          ) || [];

        trendData.push({
          month: monthDate.toLocaleDateString("tr-TR", {
            month: "short",
            year: "numeric",
          }),
          revenue: monthSubscriptions.reduce(
            (sum: number, sub: any) => sum + (sub.purchase_price || 0),
            0
          ),
          memberCount: monthMemberships.length,
        });
      }

      return trendData;
    },
    { requireAuth: true }
  );
}

/**
 * Yöneticinin tüm salonları için genel finansal özet verilerini getirir
 */
export async function getAllGymsFinanceData(managerUserId: string): Promise<
  ApiResponse<{
    totalRevenue: number;
    monthlyRevenue: number;
    pendingPayments: number;
    completedPayments: number;
    revenueGrowth: number;
    paymentGrowth: number;
    totalMembers: number;
    activeMembers: number;
    totalGyms: number;
    averageRevenuePerGym: number;
    gymPerformance: {
      gymId: string;
      gymName: string;
      revenue: number;
      members: number;
      growth: number;
      averageRevenuePerMember: number;
    }[];
    monthlyData: {
      month: string;
      revenue: number;
      payments: number;
      newMembers: number;
    }[];
    paymentMethods: {
      method: string;
      amount: number;
      percentage: number;
    }[];
    recentTransactions: {
      id: string;
      memberName: string;
      gymName: string;
      amount: number;
      status: "completed" | "pending" | "failed";
      date: string;
      packageName: string;
    }[];
  }>
> {
  return await createAction(
    async (_, supabase) => {
      // Yöneticinin salonlarını al
      const { data: gyms, error: gymsError } = await supabase
        .from("gyms")
        .select("id, name, slug")
        .eq("manager_user_id", managerUserId);

      if (gymsError) {
        throw new Error(`Salonlar alınırken hata: ${gymsError.message}`);
      }

      if (!gyms || gyms.length === 0) {
        // Salon yoksa boş data döndür
        return {
          totalRevenue: 0,
          monthlyRevenue: 0,
          pendingPayments: 0,
          completedPayments: 0,
          revenueGrowth: 0,
          paymentGrowth: 0,
          totalMembers: 0,
          activeMembers: 0,
          totalGyms: 0,
          averageRevenuePerGym: 0,
          gymPerformance: [],
          monthlyData: [],
          paymentMethods: [],
          recentTransactions: [],
        };
      }

      const gymIds = gyms.map((gym: Gyms) => gym.id);

      // Mevcut tarih bilgileri
      const now = new Date();
      const currentMonth = now.getMonth();
      const currentYear = now.getFullYear();
      const lastMonth = currentMonth === 0 ? 11 : currentMonth - 1;
      const lastMonthYear = currentMonth === 0 ? currentYear - 1 : currentYear;

      // Bu ayın başlangıcı ve bitişi
      const thisMonthStart = new Date(
        currentYear,
        currentMonth,
        1
      ).toISOString();
      const thisMonthEnd = new Date(
        currentYear,
        currentMonth + 1,
        0,
        23,
        59,
        59
      ).toISOString();

      // Geçen ayın başlangıcı ve bitişi
      const lastMonthStart = new Date(
        lastMonthYear,
        lastMonth,
        1
      ).toISOString();
      const lastMonthEnd = new Date(
        lastMonthYear,
        lastMonth + 1,
        0,
        23,
        59,
        59
      ).toISOString();

      // Tüm salonların üyeliklerini al
      const { data: allMemberships, error: membershipsError } = await supabase
        .from("memberships")
        .select(
          `
        id,
        user_id,
        gym_id,
        status,
        created_at,
        users!inner(name, surname),
        gyms!inner(name)
      `
        )
        .in("gym_id", gymIds);

      if (membershipsError) {
        throw new Error(
          `Üyelikler alınırken hata: ${membershipsError.message}`
        );
      }

      // Tüm salonların aboneliklerini al
      const { data: allSubscriptions, error: subscriptionsError } =
        await supabase
          .from("subscriptions")
          .select(
            `
        id,
        membership_id,
        gym_package_id,
        start_date,
        end_date,
        payment_status,
        purchase_price,
        created_at,
        gym_packages!inner(name, price_amount, gym_id),
        memberships!inner(
          user_id,
          gym_id,
          users!inner(name, surname),
          gyms!inner(name)
        )
      `
          )
          .in("memberships.gym_id", gymIds);

      if (subscriptionsError) {
        throw new Error(
          `Abonelikler alınırken hata: ${subscriptionsError.message}`
        );
      }

      // Toplam gelir hesaplama
      const completedSubscriptions =
        allSubscriptions?.filter(
          (sub: any) => sub.payment_status === "completed"
        ) || [];
      const pendingSubscriptions =
        allSubscriptions?.filter(
          (sub: any) => sub.payment_status === "pending"
        ) || [];

      const totalRevenue = completedSubscriptions.reduce(
        (sum: number, sub: any) => sum + (sub.purchase_price || 0),
        0
      );
      const pendingPayments = pendingSubscriptions.reduce(
        (sum: number, sub: any) => sum + (sub.purchase_price || 0),
        0
      );

      // Bu ay geliri
      const thisMonthSubscriptions = completedSubscriptions.filter(
        (sub: any) =>
          sub.created_at >= thisMonthStart && sub.created_at <= thisMonthEnd
      );
      const monthlyRevenue = thisMonthSubscriptions.reduce(
        (sum: number, sub: any) => sum + (sub.purchase_price || 0),
        0
      );

      // Geçen ay geliri (büyüme hesaplaması için)
      const lastMonthSubscriptions = completedSubscriptions.filter(
        (sub: any) =>
          sub.created_at >= lastMonthStart && sub.created_at <= lastMonthEnd
      );
      const lastMonthRevenue = lastMonthSubscriptions.reduce(
        (sum: number, sub: any) => sum + (sub.purchase_price || 0),
        0
      );

      // Büyüme oranları
      const revenueGrowth =
        lastMonthRevenue > 0
          ? ((monthlyRevenue - lastMonthRevenue) / lastMonthRevenue) * 100
          : monthlyRevenue > 0
          ? 100
          : 0;

      const paymentGrowth =
        lastMonthSubscriptions.length > 0
          ? ((thisMonthSubscriptions.length - lastMonthSubscriptions.length) /
              lastMonthSubscriptions.length) *
            100
          : thisMonthSubscriptions.length > 0
          ? 100
          : 0;

      // Üye istatistikleri
      const totalMembers = allMemberships?.length || 0;
      const activeMembers =
        allMemberships?.filter((m: any) => m.status === "active").length || 0;
      const totalGyms = gyms.length;
      const averageRevenuePerGym = totalGyms > 0 ? totalRevenue / totalGyms : 0;

      // Salon bazında performans
      const gymPerformance = gyms
        .map((gym: any) => {
          const gymMemberships =
            allMemberships?.filter((m: any) => m.gym_id === gym.id) || [];
          const gymSubscriptions = completedSubscriptions.filter(
            (sub: any) => sub.memberships?.gym_id === gym.id
          );
          const gymRevenue = gymSubscriptions.reduce(
            (sum: number, sub: any) => sum + (sub.purchase_price || 0),
            0
          );

          // Geçen ay bu salon geliri
          const gymLastMonthSubs = gymSubscriptions.filter(
            (sub: any) =>
              sub.created_at >= lastMonthStart && sub.created_at <= lastMonthEnd
          );
          const gymThisMonthSubs = gymSubscriptions.filter(
            (sub: any) =>
              sub.created_at >= thisMonthStart && sub.created_at <= thisMonthEnd
          );

          const gymLastMonthRevenue = gymLastMonthSubs.reduce(
            (sum: number, sub: any) => sum + (sub.purchase_price || 0),
            0
          );
          const gymThisMonthRevenue = gymThisMonthSubs.reduce(
            (sum: number, sub: any) => sum + (sub.purchase_price || 0),
            0
          );

          const gymGrowth =
            gymLastMonthRevenue > 0
              ? ((gymThisMonthRevenue - gymLastMonthRevenue) /
                  gymLastMonthRevenue) *
                100
              : gymThisMonthRevenue > 0
              ? 100
              : 0;

          const activeGymMembers = gymMemberships.filter(
            (m: any) => m.status === "active"
          ).length;
          const averageRevenuePerMember =
            activeGymMembers > 0 ? gymRevenue / activeGymMembers : 0;

          return {
            gymId: gym.id,
            gymName: gym.name,
            revenue: gymRevenue,
            members: activeGymMembers,
            growth: gymGrowth,
            averageRevenuePerMember,
          };
        })
        .sort((a: any, b: any) => b.revenue - a.revenue);

      // Son 3 ayın verileri
      const monthlyData = [];
      for (let i = 2; i >= 0; i--) {
        const monthDate = new Date(currentYear, currentMonth - i, 1);
        const monthStart = new Date(
          monthDate.getFullYear(),
          monthDate.getMonth(),
          1
        ).toISOString();
        const monthEnd = new Date(
          monthDate.getFullYear(),
          monthDate.getMonth() + 1,
          0,
          23,
          59,
          59
        ).toISOString();

        const monthSubscriptions = completedSubscriptions.filter(
          (sub: any) =>
            sub.created_at >= monthStart && sub.created_at <= monthEnd
        );

        const monthMemberships =
          allMemberships?.filter(
            (m: any) => m.created_at >= monthStart && m.created_at <= monthEnd
          ) || [];

        monthlyData.push({
          month: monthDate.toLocaleDateString("tr-TR", { month: "long" }),
          revenue: monthSubscriptions.reduce(
            (sum: number, sub: any) => sum + (sub.purchase_price || 0),
            0
          ),
          payments: monthSubscriptions.length,
          newMembers: monthMemberships.length,
        });
      }

      // Ödeme yöntemleri (mock data - gerçek implementasyonda payment_method field'ı olacak)
      const paymentMethods = [
        {
          method: "Kredi Kartı",
          amount: Math.round(totalRevenue * 0.65),
          percentage: 65,
        },
        {
          method: "Nakit",
          amount: Math.round(totalRevenue * 0.24),
          percentage: 24,
        },
        {
          method: "Banka Transferi",
          amount: Math.round(totalRevenue * 0.11),
          percentage: 11,
        },
      ];

      // Son işlemler (son 10 abonelik)
      const recentTransactions =
        allSubscriptions?.slice(0, 10).map((sub: any) => ({
          id: sub.id,
          memberName: `${sub.memberships?.users?.name} ${sub.memberships?.users?.surname}`,
          gymName: sub.memberships?.gyms?.name || "Bilinmeyen Salon",
          amount: sub.purchase_price || 0,
          status: sub.payment_status as "completed" | "pending" | "failed",
          date: sub.created_at,
          packageName: sub.gym_packages?.name || "Bilinmeyen Paket",
        })) || [];

      return {
        totalRevenue,
        monthlyRevenue,
        pendingPayments,
        completedPayments: totalRevenue,
        revenueGrowth,
        paymentGrowth,
        totalMembers,
        activeMembers,
        totalGyms,
        averageRevenuePerGym,
        gymPerformance,
        monthlyData,
        paymentMethods,
        recentTransactions,
      };
    },
    { requireAuth: true }
  );
}

import type React from "react";
import { Inter } from "next/font/google";
import { ThemeProvider } from "@/components/theme-provider";
import { AuthProvider } from "@/components/auth/auth-provider";
import "./globals.css";
import { Toaster } from "@/components/ui/toaster";
import ConditionalFooter from "@/components/footer";
import { Header } from "@/components/header";
const inter = Inter({ subsets: ["latin"] });

export const metadata = {
  title: "Sportiva - Spor Salonu Yönetim Platformu",
  description:
    "Spor salonu yöneticileri ve üyeleri için dijital platform. Üyelik yönetimi, ödeme takibi, antrenman programları ve daha fazlası.",
  keywords: [
    "spor salonu yönetimi",
    "fitness yönetim sistemi",
    "üyelik yönetimi",
    "spor salonu yazılımı",
    "dijital spor salonu",
    "antrenman takip",
    "fitness uygulaması",
  ],
  authors: [{ name: "Sportiva Team" }],
  creator: "Sportiva",
  publisher: "Sportiva",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL("https://sportiva.com"),
  alternates: {
    canonical: "/",
  },
  openGraph: {
    title: "Sportiva - Spor Salonu Yönetim Platformu",
    description: "Spor salonu yöneticileri ve üyeleri için dijital platform",
    url: "https://sportiva.com",
    siteName: "Sportiva",
    locale: "tr_TR",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Sportiva - Spor Salonu Yönetim Platformu",
    description: "Spor salonu yöneticileri ve üyeleri için dijital platform",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="tr" suppressHydrationWarning>
      <AuthProvider>
        <body className={inter.className}>
          <ThemeProvider
            attribute="class"
            defaultTheme="light"
            enableSystem
            disableTransitionOnChange
          >
            <main className="w-full min-h-screen flex flex-col overflow-auto relative">
            <Header />
            {children}
            <ConditionalFooter />
            <Toaster />
            </main>
          </ThemeProvider>
        </body>
      </AuthProvider>
    </html>
  );
}

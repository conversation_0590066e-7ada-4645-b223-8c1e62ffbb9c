import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON>ooter,
  CardHeader,
} from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

export default function PricingLoading() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
      {/* Header Skeleton */}
      <div className="bg-white dark:bg-slate-900 border-b border-slate-200 dark:border-slate-700">
        <div className="container mx-auto px-4 py-6">
          <div className="text-center space-y-4">
            <Skeleton className="h-10 w-96 mx-auto bg-slate-200 dark:bg-slate-700" />
            <Skeleton className="h-6 w-[600px] mx-auto bg-slate-200 dark:bg-slate-700" />
            <Skeleton className="h-6 w-[500px] mx-auto bg-slate-200 dark:bg-slate-700" />
          </div>
        </div>
      </div>

      {/* Billing Toggle Skeleton */}
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center space-x-4 mb-12">
          <Skeleton className="h-6 w-12" />
          <Skeleton className="h-6 w-12" />
          <Skeleton className="h-6 w-12" />
          <Skeleton className="h-6 w-32" />
        </div>

        {/* Pricing Cards Skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-7xl mx-auto">
          {[1, 2, 3].map((i) => (
            <Card
              key={i}
              className="relative bg-white dark:bg-slate-800 border-slate-200 dark:border-slate-700"
            >
              <CardHeader className="text-center pb-2">
                <Skeleton className="h-8 w-8 mx-auto mb-4 bg-slate-200 dark:bg-slate-700" />
                <Skeleton className="h-8 w-32 mx-auto bg-slate-200 dark:bg-slate-700" />
                <Skeleton className="h-4 w-40 mx-auto bg-slate-200 dark:bg-slate-700" />
                <Skeleton className="h-6 w-20 mx-auto mt-2 bg-slate-200 dark:bg-slate-700" />
              </CardHeader>
              <CardContent className="text-center">
                <div className="mb-6">
                  <Skeleton className="h-12 w-32 mx-auto bg-slate-200 dark:bg-slate-700" />
                  <Skeleton className="h-4 w-16 mx-auto mt-2 bg-slate-200 dark:bg-slate-700" />
                  <Skeleton className="h-4 w-24 mx-auto mt-1 bg-slate-200 dark:bg-slate-700" />
                </div>
                <div className="space-y-3">
                  {[1, 2, 3, 4, 5].map((j) => (
                    <div key={j} className="flex items-center">
                      <Skeleton className="h-4 w-4 mr-3 bg-slate-200 dark:bg-slate-700" />
                      <Skeleton className="h-4 flex-1 bg-slate-200 dark:bg-slate-700" />
                    </div>
                  ))}
                </div>
              </CardContent>
              <CardFooter>
                <Skeleton className="h-10 w-full bg-slate-200 dark:bg-slate-700" />
              </CardFooter>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
}

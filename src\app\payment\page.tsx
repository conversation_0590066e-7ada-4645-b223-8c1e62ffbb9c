import { Metadata } from "next";
import { Suspense } from "react";
import { PaymentClient } from "@/components/payment/payment-client";
import PaymentLoading from "./loading";

export const metadata: Metadata = {
  title: "Ödeme - Sportiva | Spor Salonu Yönetim Sistemi",
  description:
    "Sportiva spor salonu yönetim sistemi ödeme sayfası. Güvenli ödeme ile paketinizi satın alın.",
  robots: "noindex, nofollow", // Ödeme sayfası indexlenmemeli
};

export default function PaymentPage() {
  return (
    <Suspense fallback={<PaymentLoading />}>
      <PaymentClient />
    </Suspense>
  );
}

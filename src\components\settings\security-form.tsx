"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle } from "lucide-react"
import { Separator } from "@/components/ui/separator"
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from "@/components/ui/dialog"
import { updatePassword, deleteUserAccount } from "@/app/actions/user-actions"
import { toast } from "sonner"

interface SecurityFormProps {
  userId: string
  email: string
}

export function SecurityForm({ userId, email }: SecurityFormProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [deleteConfirmation, setDeleteConfirmation] = useState("")
  const [passwordData, setPasswordData] = useState({
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  })

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setPasswordData((prev) => ({ ...prev, [name]: value }))
  }

  const handlePasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError(null)
    setSuccess(null)

    // Validation
    if (!passwordData.currentPassword || !passwordData.newPassword || !passwordData.confirmPassword) {
      setError("Tüm şifre alanları zorunludur.")
      return
    }

    if (passwordData.newPassword !== passwordData.confirmPassword) {
      setError("Yeni şifre ve şifre tekrarı eşleşmiyor.")
      return
    }

    if (passwordData.newPassword.length < 8) {
      setError("Şifre en az 8 karakter uzunluğunda olmalıdır.")
      return
    }

    setIsLoading(true)

    try {
      // FormData oluştur
      const formData = new FormData();
      formData.append("userId", userId);
      formData.append("currentPassword", passwordData.currentPassword);
      formData.append("newPassword", passwordData.newPassword);
      
      // Update password using server action
      const result = await updatePassword(formData);

      if (result.error) {
        throw new Error(result.error);
      }

      setSuccess("Şifreniz başarıyla güncellendi.");
      toast.success("Şifreniz güncellendi");
      
      setPasswordData({
        currentPassword: "",
        newPassword: "",
        confirmPassword: "",
      });
    } catch (err: any) {
      console.error("Password update error:", err)
      setError(err.message || "Şifre güncellenirken bir hata oluştu. Lütfen tekrar deneyin.")
      toast.error("Şifre güncelleme hatası");
    } finally {
      setIsLoading(false)
    }
  }

  const handleDeleteAccount = async () => {
    if (deleteConfirmation !== email) {
      setError("Hesabınızı silmek için e-posta adresinizi doğru girmelisiniz.")
      return
    }

    setIsLoading(true)

    try {
      // Delete account using server action
      const result = await deleteUserAccount(userId)

      if (result.error) {
        throw new Error(result.error)
      }

      toast.success("Hesabınız başarıyla silindi");
      // Redirect to home page after successful deletion
      router.push("/")
    } catch (err: any) {
      console.error("Account deletion error:", err)
      setError(err.message || "Hesap silinirken bir hata oluştu. Lütfen tekrar deneyin.")
      toast.error("Hesap silme hatası");
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-8">
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert variant="default" className="bg-green-50 text-green-800 border-green-200">
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}

      <form onSubmit={handlePasswordSubmit} className="space-y-4">
        <h3 className="text-lg font-medium">Şifre Değiştir</h3>
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="currentPassword">Mevcut Şifre</Label>
            <Input
              id="currentPassword"
              name="currentPassword"
              type="password"
              value={passwordData.currentPassword}
              onChange={handlePasswordChange}
              required
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="newPassword">Yeni Şifre</Label>
            <Input
              id="newPassword"
              name="newPassword"
              type="password"
              value={passwordData.newPassword}
              onChange={handlePasswordChange}
              required
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="confirmPassword">Yeni Şifre Tekrar</Label>
            <Input
              id="confirmPassword"
              name="confirmPassword"
              type="password"
              value={passwordData.confirmPassword}
              onChange={handlePasswordChange}
              required
            />
          </div>
        </div>
        <Button type="submit" disabled={isLoading}>
          {isLoading ? "Güncelleniyor..." : "Şifreyi Güncelle"}
        </Button>
      </form>

      <Separator />

      <div className="space-y-4">
        <h3 className="text-lg font-medium text-destructive">Tehlikeli Bölge</h3>
        <p className="text-sm text-muted-foreground">
          Hesabınızı sildiğinizde, tüm verileriniz kalıcı olarak silinecektir. Bu işlem geri alınamaz.
        </p>

        <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
          <DialogTrigger asChild>
            <Button variant="destructive">Hesabımı Sil</Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Hesabınızı silmek istediğinize emin misiniz?</DialogTitle>
              <DialogDescription>
                Bu işlem geri alınamaz. Hesabınız ve tüm verileriniz kalıcı olarak silinecektir.
                Onaylamak için e-posta adresinizi ({email}) aşağıya yazın.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-2 py-4">
              <Label htmlFor="deleteConfirmation">E-posta adresiniz</Label>
              <Input
                id="deleteConfirmation"
                value={deleteConfirmation}
                onChange={(e) => setDeleteConfirmation(e.target.value)}
                placeholder={email}
              />
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
                İptal
              </Button>
              <Button 
                variant="destructive" 
                onClick={handleDeleteAccount} 
                disabled={isLoading || deleteConfirmation !== email}
              >
                {isLoading ? "Siliniyor..." : "Hesabımı Sil"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  )
}

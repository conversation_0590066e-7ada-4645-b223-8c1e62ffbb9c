"use client"

import { <PERSON>, Sun } from "lucide-react";
import { But<PERSON> } from "./ui/button";
import { useTheme } from "next-themes";
export default function ThemeButton (){
      const { theme, setTheme } = useTheme();
    return    <Button
    variant="ghost"
    size="icon"
    onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
    aria-label="Toggle theme"
  >
    <Sun className="h-5 w-5 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
    <Moon className="absolute h-5 w-5 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
    <span className="sr-only">Toggle theme</span>
  </Button>
  }
"use client"

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from "recharts"
import { useState, useEffect } from "react"
import { useAuth } from "@/components/auth/auth-provider"
import { getMyGyms } from "@/app/actions/gym-actions"

export function GymAnalytics() {
  const { authUser } = useAuth()
  const [analyticsData, setAnalyticsData] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchAnalytics = async () => {
      if (!authUser) return
      
      setIsLoading(true)
      
      try {
        // Server Action kullanarak salon bilgilerini getir
        const response = await getMyGyms()
        
        if (!response.success || !response.data || response.data.length === 0) {
          setError("Salon bilgileri getirilemedi veya henüz salon oluşturmadınız.")
          return
        }
        
        const gym = response.data[0] // İlk salonu kullanalım
        
        // Not: Burada gerçek analitik veriler için bir endpoint olmalı
        // Şimdilik mock veri kullanıyoruz, ilerleyen aşamada
        // salon-bazlı analitik veriler için bir server action eklenebilir
        
        const mockData = [
          { month: "Ocak", newMembers: 12, revenue: 3500 },
          { month: "Şubat", newMembers: 15, revenue: 4200 },
          { month: "Mart", newMembers: 8, revenue: 2800 },
          { month: "Nisan", newMembers: 20, revenue: 5500 },
          { month: "Mayıs", newMembers: 10, revenue: 3200 },
          { month: "Haziran", newMembers: 18, revenue: 4800 },
        ]

        setAnalyticsData(mockData)
      } catch (err) {
        console.error("Analitik verileri getirirken hata:", err)
        setError("Veriler yüklenirken bir hata oluştu. Lütfen sayfayı yenileyin.")
      } finally {
        setIsLoading(false)
      }
    }

    fetchAnalytics()
  }, [authUser])

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-6 bg-muted/30 rounded-lg">
        <h3 className="text-lg font-medium mb-2">Analitik veriler yüklenemedi</h3>
        <p className="text-muted-foreground">{error}</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Analitik Raporlar</h2>
          <p className="text-muted-foreground">Salonunuzun performansını ve büyüme trendlerini görüntüleyin.</p>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-1 lg:grid-cols-1">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Aylık Performans</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={analyticsData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="newMembers" fill="#8884d8" name="Yeni Üyeler" />
                <Bar dataKey="revenue" fill="#82ca9d" name="Gelir (₺)" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

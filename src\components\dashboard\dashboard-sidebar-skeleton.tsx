"use client"

import { Skeleton } from "@/components/ui/skeleton"

export function DashboardSidebarSkeleton() {
  return (
    <div className="fixed top-14 left-0 z-20 hidden h-[calc(100vh-3.5rem)] w-[200px] flex-col border-r bg-sidebar text-sidebar-foreground lg:flex">
      <div className="space-y-4 px-2 py-4">
        {/* Logo ve Başlık */}
        <div className="flex items-center gap-2 px-2 py-1.5">
          <Skeleton className="h-8 w-8 rounded-md bg-sidebar-accent/50" />
          <Skeleton className="h-5 w-32 bg-sidebar-accent/50" />
        </div>

        {/* Divider */}
        <div className="my-2 h-px bg-sidebar-border" />

        {/* Ana <PERSON> Öğeleri */}
        <div className="space-y-1">
          {[1, 2, 3, 4, 5].map((i) => (
            <div key={`nav-${i}`} className="flex items-center gap-2 rounded-md px-2 py-2">
              <Skeleton className="h-5 w-5 rounded-md bg-sidebar-accent/50" />
              <Skeleton className="h-4 w-24 bg-sidebar-accent/50" />
            </div>
          ))}
        </div>

        {/* Divider */}
        <div className="my-2 h-px bg-sidebar-border" />

        {/* Alt Menü Öğeleri */}
        <div className="space-y-1">
          {[1, 2, 3].map((i) => (
            <div key={`sub-${i}`} className="flex items-center gap-2 rounded-md px-2 py-2">
              <Skeleton className="h-5 w-5 rounded-md bg-sidebar-accent/50" />
              <Skeleton className="h-4 w-28 bg-sidebar-accent/50" />
            </div>
          ))}
        </div>

        {/* Divider */}
        <div className="my-2 h-px bg-sidebar-border" />

        {/* Profil Bölümü */}
        <div className="mt-auto px-2 py-2">
          <div className="flex items-center gap-2">
            <Skeleton className="h-8 w-8 rounded-full bg-sidebar-accent/50" />
            <div className="space-y-1">
              <Skeleton className="h-4 w-24 bg-sidebar-accent/50" />
              <Skeleton className="h-3 w-20 bg-sidebar-accent/50" />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

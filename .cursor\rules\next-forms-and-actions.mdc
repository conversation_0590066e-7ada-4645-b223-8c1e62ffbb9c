---
description: 
globs: 
alwaysApply: false
---
# Next.js Forms ve Server Actions Kullanım Rehberi

Bu rehber, Next.js'de form yönetimi, Server Actions ve React 19'un yeni özellikleri için en iyi pratikleri açıklar.

## Form Bileşeni Yapısı

Form bileşenleri genellikle şu şekilde yapılandırılır:

```tsx
'use client'

import { useActionState } from "react"
import { someServerAction } from "@/app/actions/some-actions"
import { ApiResponse } from "@/lib/actions/types"

// Form bileşeni
export function SomeForm() {
  const [state, action] = useActionState(someServerAction, initialState)
  
  return (
    <form action={action} className="space-y-4">
      {/* Form alanları */}
      <Button type="submit">Gönder</Button>
    </form>
  )
}
```

## Server Action Yapısı

Server Action'lar genellikle ayrı bir dosyada tanımlanır ve `'use server'` direktifi ile işaretlenir:

```tsx
// app/actions/some-actions.ts
'use server'

import { z } from "zod"

// Doğrulama şeması
const schema = z.object({
  // Alan doğrulamaları
})

export async function someServerAction(prevState: ApiResponse, formData: FormData) {
  // 1. Form verilerini çıkar
  const rawData = {
    field1: formData.get("field1"),
    field2: formData.get("field2"),
  }
  
  // 2. Verileri doğrula
  const validationResult = schema.safeParse(rawData)
  
  if (!validationResult.success) {
    return {
      success: false,
      error: "Doğrulama hatası",
      // Hataları döndür
    }
  }
  
  // 3. Veritabanı işlemleri
  try {
    // Veritabanı işlemleri...
    return {
      success: true,
      message: "İşlem başarılı",
    }
  } catch (error) {
    return {
      success: false,
      error: "Bir hata oluştu",
    }
  }
}
```

## `useActionState` ile Form Durumu Yönetimi

React 19'un `useActionState` hook'u, form durumunu yönetmek için güçlü bir araçtır:

```tsx
// 1. Hook'u başlat
const [state, action, isPending] = useActionState(serverAction, initialState)

// 2. Form'a action'ı geç
<form action={action}>...</form>

// 3. Bekleme durumunu yönet
<Button disabled={isPending}>
  {isPending ? "İşleniyor..." : "Gönder"}
</Button>

// 4. Başarı/hata durumlarını göster
{state.error && <Alert variant="destructive">{state.error}</Alert>}
{state.success && <Alert>{state.message}</Alert>}
```

## Form Sıfırlama ve Veri Kalıcılığı

- `useActionState` ile başarılı gönderimlerden sonra formlar otomatik olarak sıfırlanır
- Hata durumunda değerleri korumak için `defaultValue` kullanılır:

```tsx
<Input
  name="fieldName"
  defaultValue={state.data?.fieldName || ""}
  className={state.errors?.fieldName ? "border-red-500" : ""}
/>
{state.errors?.fieldName && (
  <p className="text-sm text-red-500">{state.errors.fieldName}</p>
)}
```

## İstemci ve Sunucu Tarafı Doğrulama

İki katmanlı doğrulama yaklaşımı önerilir:

1. **İstemci Tarafı Doğrulama** (UX için):
   - HTML özellikleri: `required`, `minLength`, `pattern`
   - Anında kullanıcı geribildirimi sağlar

2. **Sunucu Tarafı Doğrulama** (güvenlik için):
   - Zod şemaları ile veri yapısı ve kuralları tanımlanır
   - Tüm girdiler sunucuda doğrulanır

## Örnekler

### Kayıt Formu

[register-form.tsx](mdc:src/components/register-form.tsx) dosyasında kayıt formu örneği bulunmaktadır.

### Adres Formu Örneği

```tsx
export function AddressForm() {
  const [state, action, isPending] = useActionState(submitAddress, initialState)
  
  return (
    <Card>
      <CardContent>
        <form action={action} className="space-y-4">
          <div>
            <Label>Adres</Label>
            <Input 
              name="address"
              defaultValue={state.data?.address || ""}
            />
          </div>
          <Button type="submit" disabled={isPending}>
            {isPending ? "Kaydediliyor..." : "Kaydet"}
          </Button>
        </form>
      </CardContent>
    </Card>
  )
}
```

## En İyi Pratikler

1. Form mantığını sunucu tarafı action'larda tutun
2. İstemci bileşenlerini sadece kullanıcı etkileşimi için kullanın
3. Her zaman çift doğrulama yapın: İstemci (UX için) ve sunucu (güvenlik için)
4. Form durumunu ve hataları `useActionState` ile yönetin
5. Hata durumunda form değerlerini koruyun
6. Sunucu action'larında açık hata mesajları döndürün
7. Veri doğrulama için Zod gibi tip güvenli kütüphaneler kullanın


"use client";

import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import {
  CheckCircle,
  CreditCard,
  Crown,
  Zap,
  Star,
  ArrowRight,
  Settings,
  ExternalLink,
} from "lucide-react";

interface SubscriptionStatusProps {
  userId: string;
  isManager: boolean;
}

export function SubscriptionStatus({ isManager }: SubscriptionStatusProps) {
  const router = useRouter();

  if (isManager) {
    return (
      <div className="space-y-6">
        <Alert className="bg-green-50 text-green-800 border-green-200">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription>
            Şu anda yönetici aboneliğiniz bulunmaktadır. Abonelik detaylarınızı
            yönetici panelinden görüntüleyebilirsiniz.
          </AlertDescription>
        </Alert>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Crown className="h-5 w-5 text-primary" />
              Mevcut Abonelik
            </CardTitle>
            <CardDescription>Yönetici aboneliğinizin detayları</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="font-medium">Abonelik Tipi:</span>
                <Badge
                  variant="secondary"
                  className="bg-primary/10 text-primary"
                >
                  Professional Yönetici
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="font-medium">Durum:</span>
                <Badge className="bg-green-100 text-green-800">Aktif</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="font-medium">Sonraki Ödeme:</span>
                <span className="text-sm text-muted-foreground">
                  15 Şubat 2025
                </span>
              </div>

              <div className="pt-4 space-y-2">
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={() => router.push("/dashboard/manager")}
                >
                  <Settings className="mr-2 h-4 w-4" />
                  Yönetici Paneline Git
                </Button>

                <Button
                  variant="outline"
                  className="w-full"
                  onClick={() => router.push("/pricing")}
                >
                  <ExternalLink className="mr-2 h-4 w-4" />
                  Paket Değiştir / Yükselt
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Üye kullanıcı için
  return (
    <div className="space-y-6">
      <Alert>
        <CreditCard className="h-4 w-4" />
        <AlertDescription>
          Şu anda üye aboneliğiniz bulunmaktadır. Salon yöneticisi olmak için
          yönetici paketlerinden birini satın alabilirsiniz.
        </AlertDescription>
      </Alert>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5 text-blue-600" />
            Mevcut Durum
          </CardTitle>
          <CardDescription>
            Üyelik durumunuz ve abonelik seçenekleri
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="font-medium">Hesap Tipi:</span>
              <Badge variant="secondary">Üye Hesabı</Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="font-medium">Durum:</span>
              <Badge className="bg-blue-100 text-blue-800">Aktif</Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="border-2 border-dashed border-primary/50 hover:border-primary transition-colors">
        <CardHeader>
          <div className="flex items-center space-x-2">
            <Crown className="h-5 w-5 text-primary" />
            <CardTitle>Salon Yöneticisi Olun</CardTitle>
          </div>
          <CardDescription>
            Salon sahibi veya yöneticisi olarak Sportiva'da salonunuzu yönetin
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-2">
            <h3 className="font-medium">Yönetici Aboneliği ile:</h3>
            <ul className="space-y-1 list-disc pl-5 text-sm text-muted-foreground">
              <li>Salonunuzu platforma ekleyebilirsiniz</li>
              <li>Üyelerinizi yönetebilirsiniz</li>
              <li>Paketler oluşturabilirsiniz</li>
              <li>Dersler ve etkinlikler düzenleyebilirsiniz</li>
              <li>Detaylı raporlar alabilirsiniz</li>
              <li>Ödeme takibi yapabilirsiniz</li>
            </ul>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 text-center">
            <div className="p-3 bg-blue-50 rounded-lg border">
              <Zap className="h-6 w-6 text-blue-600 mx-auto mb-1" />
              <div className="text-sm font-medium">Starter</div>
              <div className="text-xs text-muted-foreground">₺199/ay'dan</div>
            </div>
            <div className="p-3 bg-green-50 rounded-lg border border-green-200">
              <Star className="h-6 w-6 text-green-600 mx-auto mb-1" />
              <div className="text-sm font-medium">Professional</div>
              <div className="text-xs text-muted-foreground">₺299/ay'dan</div>
              <Badge className="bg-green-100 text-green-800 text-xs mt-1">
                Önerilen
              </Badge>
            </div>
            <div className="p-3 bg-purple-50 rounded-lg border">
              <Crown className="h-6 w-6 text-purple-600 mx-auto mb-1" />
              <div className="text-sm font-medium">Enterprise</div>
              <div className="text-xs text-muted-foreground">₺499/ay'dan</div>
            </div>
          </div>

          <Button className="w-full" onClick={() => router.push("/pricing")}>
            Paketleri İncele ve Satın Al
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}

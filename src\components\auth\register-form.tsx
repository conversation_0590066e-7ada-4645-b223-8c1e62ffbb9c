"use client";
import { useActionState, useState } from "react";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, User, Mail, Lock, Eye, EyeOff, Du<PERSON>bell } from "lucide-react";
import { registerAction } from "@/lib/auth-actions";

// Floating Input Component
function FloatingInput({
  id,
  name,
  type,
  placeholder,
  required,
  minLength,
  icon: Icon,
  showPassword,
  onTogglePassword,
}: {
  id: string;
  name: string;
  type: string;
  placeholder: string;
  required?: boolean;
  minLength?: number;
  icon: any;
  showPassword?: boolean;
  onTogglePassword?: () => void;
}) {
  const [focused, setFocused] = useState(false);
  const [hasValue, setHasValue] = useState(false);

  return (
    <div className="relative group">
      <div className="relative">
        <input
          id={id}
          name={name}
          type={type}
          required={required}
          minLength={minLength}
          className={`
            w-full px-4 py-4 pl-12 pr-12
            text-gray-900 bg-white/90 dark:text-white dark:bg-gray-900/50
            border-2 rounded-xl transition-all duration-300 ease-in-out backdrop-blur-sm
            ${
              focused || hasValue
                ? "border-orange-500 bg-white dark:bg-gray-900/70"
                : "border-gray-300 hover:border-gray-400 dark:border-gray-700 dark:hover:border-gray-600"
            }
            focus:outline-none focus:border-orange-500 focus:bg-white dark:focus:bg-gray-900/70
            placeholder-transparent peer
          `}
          placeholder={placeholder}
          onFocus={() => setFocused(true)}
          onBlur={(e) => {
            setFocused(false);
            setHasValue(e.target.value !== "");
          }}
          onChange={(e) => setHasValue(e.target.value !== "")}
        />

        {/* Floating Label */}
        <label
          htmlFor={id}
          className={`
            absolute left-12 transition-all duration-300 ease-in-out pointer-events-none
            ${
              focused || hasValue
                ? "-top-2 text-xs text-orange-500 bg-white dark:bg-gray-900 px-2 rounded"
                : "top-4 text-gray-500 dark:text-gray-400"
            }
          `}
        >
          {placeholder}
        </label>

        {/* Left Icon */}
        <Icon
          className={`
          absolute left-4 top-4 w-5 h-5 transition-colors duration-300
          ${
            focused || hasValue
              ? "text-orange-500"
              : "text-gray-500 dark:text-gray-400"
          }
        `}
        />

        {/* Password Toggle */}
        {type === "password" && onTogglePassword && (
          <button
            type="button"
            onClick={onTogglePassword}
            className="absolute right-4 top-4 text-gray-500 dark:text-gray-400 hover:text-orange-500 transition-colors"
          >
            {showPassword ? (
              <EyeOff className="w-5 h-5" />
            ) : (
              <Eye className="w-5 h-5" />
            )}
          </button>
        )}
      </div>
    </div>
  );
}

export function RegisterForm() {
  const [registerState, registerFormAction, registerPending] = useActionState(
    registerAction,
    {
      error: "",
      success: false,
    }
  );

  const [showPassword, setShowPassword] = useState(false);

  return (
    <div className="w-full max-w-md mx-auto">
      {/* Sportif Header */}
      <div className="text-center mb-8">
        <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-orange-500 to-red-500 rounded-full mb-4">
          <Dumbbell className="w-8 h-8 text-white" />
        </div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          Sportiva'ya Katıl
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Spor salonu deneyimini dijitalleştir
        </p>
      </div>

      {/* Form Container */}
      <div className="bg-white/80 dark:bg-gray-900/30 backdrop-blur-lg border border-gray-200 dark:border-gray-800 rounded-2xl p-8 shadow-2xl">
        <form action={registerFormAction} className="space-y-6">
          <FloatingInput
            id="name"
            name="name"
            type="text"
            placeholder="Adınız"
            required
            icon={User}
          />

          <FloatingInput
            id="surname"
            name="surname"
            type="text"
            placeholder="Soyadınız"
            required
            icon={User}
          />

          <FloatingInput
            id="email"
            name="email"
            type="email"
            placeholder="E-posta Adresiniz"
            required
            icon={Mail}
          />

          <div className="space-y-2">
            <FloatingInput
              id="password"
              name="password"
              type={showPassword ? "text" : "password"}
              placeholder="Şifreniz"
              required
              minLength={6}
              icon={Lock}
              showPassword={showPassword}
              onTogglePassword={() => setShowPassword(!showPassword)}
            />
            <p className="text-xs text-gray-600 dark:text-gray-500 ml-1">
              En az 6 karakter olmalıdır
            </p>
          </div>

          {registerState.error && (
            <Alert
              variant="destructive"
              className="bg-red-50 dark:bg-red-900/50 border-red-300 dark:border-red-500 text-red-800 dark:text-red-200"
            >
              <AlertDescription>{registerState.error}</AlertDescription>
            </Alert>
          )}

          {registerState.success && (
            <Alert className="bg-green-50 dark:bg-green-900/50 border-green-300 dark:border-green-500 text-green-800 dark:text-green-200">
              <AlertDescription>
                Hesap başarıyla oluşturuldu! Giriş yapabilirsiniz.
              </AlertDescription>
            </Alert>
          )}

          <Button
            type="submit"
            disabled={registerPending}
            className="w-full py-4 bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white font-semibold rounded-xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
          >
            {registerPending ? (
              <>
                <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                Hesap Oluşturuluyor...
              </>
            ) : (
              <>
                <Dumbbell className="mr-2 h-5 w-5" />
                Hesap Oluştur
              </>
            )}
          </Button>
        </form>

        {/* Login Link */}
        <div className="mt-8 text-center">
          <p className="text-gray-600 dark:text-gray-400">
            Zaten hesabın var mı?{" "}
            <Link
              href="/login"
              className="text-orange-500 hover:text-orange-400 font-semibold transition-colors"
            >
              Giriş Yap
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}

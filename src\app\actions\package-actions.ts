"use server";

import { createAction } from "@/lib/actions/core";
import { ApiResponse } from "@/lib/actions/types";
import { createNotification } from "@/lib/actions/notifications";
import { z } from "zod";
import type { Tables, UpdateTables } from "@/lib/supabase/types";

// Gym package validasyon şeması - Form ile uyumlu
const gymPackageSchema = z.object({
  gym_id: z.string().uuid("Geçerli bir salon ID'si gereklidir"),
  name: z
    .string()
    .min(1, "Paket adı gereklidir")
    .max(100, "Paket adı en fazla 100 karakter olabilir"),
  package_type: z.enum(["monthly", "quarterly", "yearly", "daily", "trial"], {
    errorMap: () => ({ message: "Geçerli bir paket türü seçiniz" }),
  }),
  duration_days: z.coerce.number().int().positive().optional().nullable(),
  price_amount: z.coerce.number().positive("Fiyat 0'dan b<PERSON>yük olmalıdır"),
  description: z
    .string()
    .max(500, "Açıklama en fazla 500 karakter olabilir")
    .optional()
    .nullable(),
  is_active: z.boolean().default(true),
});

/**
 * Salon paketi oluşturur
 */
export async function createGymPackage(
  packageData: any
): Promise<ApiResponse<Tables<"gym_packages">>> {
  return await createAction(
    async (_, supabase, userId) => {
      // Veriyi validasyondan geçir
      const validatedData = gymPackageSchema.parse(packageData);

      // Salon yöneticisi yetkisi kontrolü
      const { data: gym, error: gymError } = await supabase
        .from("gyms")
        .select("manager_user_id")
        .eq("id", validatedData.gym_id)
        .single();

      if (gymError || !gym) {
        throw new Error("Salon bilgileri alınamadı.");
      }

      if (gym.manager_user_id !== userId) {
        throw new Error("Bu salon için paket oluşturma yetkiniz bulunmuyor.");
      }

      // Paketi oluştur
      const { data: newPackage, error } = await supabase
        .from("gym_packages")
        .insert(validatedData)
        .select()
        .single();

      if (error) {
        throw new Error(`Paket oluşturulurken hata: ${error.message}`);
      }

      // Bildirim oluştur
      await createNotification({
        userId: gym.manager_user_id,
        title: "Yeni Paket Oluşturuldu",
        message: `${newPackage.name} paketi başarıyla oluşturuldu.`,
        type: "package_created",
        relatedEntityType: "gym_packages",
        relatedEntityId: newPackage.id,
      });

      return newPackage;
    },
    {
      requireAuth: true,
      revalidatePaths: ["/dashboard/manager/packages"],
    }
  );
}

/**
 * Salon paketlerini salon ID'sine göre getirir
 * @param gymId Salon ID'si
 * @returns Salon paketleri veya hata
 */
export async function getPackagesByGymId(
  gymId: string
): Promise<ApiResponse<Tables<"gym_packages">[]>> {
  if (!gymId) {
    return { success: false, error: "Salon ID'si gereklidir." };
  }

  return await createAction(async (_, supabase) => {
    // Herkese açık paketleri getirirken yetkilendirme kontrolü yapılmaz
    const { data, error } = await supabase
      .from("gym_packages")
      .select("*")
      .eq("gym_id", gymId)
      .eq("is_active", true);

    if (error) {
      throw new Error(`Paketler getirilirken hata: ${error.message}`);
    }

    return data || [];
  });
}

/**
 * Salon paketlerini yöneticiler için getirir (aktif olmayan paketler dahil)
 * @param gymId Salon ID'si
 * @returns Salon paketleri veya hata
 */
export async function getPackagesByGymIdForManager(
  gymId: string
): Promise<ApiResponse<Tables<"gym_packages">[]>> {
  if (!gymId) {
    return { success: false, error: "Salon ID'si gereklidir." };
  }

  return await createAction(
    async (_, supabase, userId) => {
      // Salon yöneticisi yetkisi kontrolü
      const { data: gym } = await supabase
        .from("gyms")
        .select("manager_user_id")
        .eq("id", gymId)
        .single();

      if (!gym || gym.manager_user_id !== userId) {
        throw new Error(
          "Bu salonun paketlerini yönetmek için yetkiniz bulunmuyor."
        );
      }

      // Tüm paketleri getir (aktif olmayanlar dahil)
      const { data, error } = await supabase
        .from("gym_packages")
        .select("*")
        .eq("gym_id", gymId);

      if (error) {
        throw new Error(`Paketler getirilirken hata: ${error.message}`);
      }

      return data || [];
    },
    { requireAuth: true }
  );
}

/**
 * Salon paketini günceller
 */
export async function updateGymPackage(
  packageId: string,
  packageData: UpdateTables<"gym_packages">
): Promise<ApiResponse<Tables<"gym_packages">>> {
  return await createAction(
    async (_, supabase, userId) => {
      // Paketin mevcut olup olmadığını ve kullanıcının yetkisini kontrol et
      const { data: pkg } = await supabase
        .from("gym_packages")
        .select("gym_id")
        .eq("id", packageId)
        .single();

      if (!pkg) {
        throw new Error("Paket bulunamadı.");
      }

      // Salon yöneticisi yetkisi kontrolü
      const { data: gym } = await supabase
        .from("gyms")
        .select("manager_user_id")
        .eq("id", pkg.gym_id)
        .single();

      if (!gym || gym.manager_user_id !== userId) {
        throw new Error("Bu paketi güncellemek için yetkiniz bulunmuyor.");
      }

      // Paketi güncelle
      const { data: updatedPackage, error } = await supabase
        .from("gym_packages")
        .update(packageData)
        .eq("id", packageId)
        .select()
        .single();

      if (error) {
        throw new Error(`Paket güncellenirken hata: ${error.message}`);
      }

      return updatedPackage;
    },
    { requireAuth: true, revalidatePaths: ["/dashboard/manager/packages"] }
  );
}

/**
 * Salon paketini siler
 */
export async function deleteGymPackage(
  packageId: string
): Promise<ApiResponse> {
  return await createAction(
    async (_, supabase, userId) => {
      // Paketin mevcut olup olmadığını ve kullanıcının yetkisini kontrol et
      const { data: pkg } = await supabase
        .from("gym_packages")
        .select("gym_id")
        .eq("id", packageId)
        .single();

      if (!pkg) {
        throw new Error("Paket bulunamadı.");
      }

      // Salon yöneticisi yetkisi kontrolü
      const { data: gym } = await supabase
        .from("gyms")
        .select("manager_user_id")
        .eq("id", pkg.gym_id)
        .single();

      if (!gym || gym.manager_user_id !== userId) {
        throw new Error("Bu paketi silmek için yetkiniz bulunmuyor.");
      }

      // Paketi sil
      const { error } = await supabase
        .from("gym_packages")
        .delete()
        .eq("id", packageId);

      if (error) {
        throw new Error(`Paket silinirken hata: ${error.message}`);
      }

      return { deleted: true };
    },
    { requireAuth: true, revalidatePaths: ["/dashboard/manager/packages"] }
  );
}

/**
 * Salon paketlerini yöneticiler için satın alma sayıları ile birlikte getirir
 * @param gymId Salon ID'si
 * @returns Salon paketleri ve satın alma sayıları veya hata
 */
export async function getPackagesWithPurchaseCountsByGymId(
  gymId: string
): Promise<ApiResponse<any[]>> {
  if (!gymId) {
    return { success: false, error: "Salon ID'si gereklidir." };
  }

  return await createAction(
    async (_, supabase, userId) => {
      // Salon yöneticisi yetkisi kontrolü
      const { data: gym } = await supabase
        .from("gyms")
        .select("manager_user_id")
        .eq("id", gymId)
        .single();

      if (!gym || gym.manager_user_id !== userId) {
        throw new Error(
          "Bu salonun paketlerini yönetmek için yetkiniz bulunmuyor."
        );
      }

      // Tüm paketleri getir
      const { data: packagesData, error: packagesError } = await supabase
        .from("gym_packages")
        .select("*")
        .eq("gym_id", gymId)
        .order("created_at", { ascending: false });

      if (packagesError) {
        throw new Error(`Paketler getirilirken hata: ${packagesError.message}`);
      }

      if (!packagesData || packagesData.length === 0) {
        return [];
      }

      // Paketlerin satın alma sayılarını getir
      const packageIds = packagesData.map(
        (pkg: Tables<"gym_packages">) => pkg.id
      );

      const { data: subscriptionsData, error: subscriptionsError } =
        await supabase
          .from("subscriptions")
          .select("gym_package_id, id")
          .in("gym_package_id", packageIds);

      if (subscriptionsError) {
        throw new Error(
          `Abonelikler getirilirken hata: ${subscriptionsError.message}`
        );
      }

      // Her paket için abonelik sayısını hesapla
      const subscriptionCounts: Record<string, number> = {};
      subscriptionsData?.forEach((sub: { gym_package_id: string }) => {
        subscriptionCounts[sub.gym_package_id] =
          (subscriptionCounts[sub.gym_package_id] || 0) + 1;
      });

      // Satın alma sayılarını paketle eşle
      const packagesWithCounts = packagesData.map(
        (pkg: Tables<"gym_packages">) => {
          return {
            ...pkg,
            purchase_count: subscriptionCounts[pkg.id] || 0,
          };
        }
      );

      return packagesWithCounts;
    },
    { requireAuth: true }
  );
}

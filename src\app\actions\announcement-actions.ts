"use server"

import { createAction } from "@/lib/actions/core"
import { ApiResponse } from "@/lib/actions/types"
import { createNotification } from "@/lib/actions/notifications"
import { z } from "zod"
import type { InsertTables, UpdateTables } from "@/lib/supabase/types"

// Validasyon şeması
const announcementSchema = z.object({
  title: z.string().min(1, "Başlık gereklidir"),
  content: z.string().min(1, "İçerik gereklidir"),
  is_public: z.boolean().default(true),
  gym_id: z.string().uuid("Geçerli bir salon ID'si gereklidir"),
})

// Üyelik için tip tanımı
type Membership = {
  user_id: string;
  status: string;
}

/**
 * Salon duyurusu oluşturur
 */
export async function createGymAnnouncement(announcementData: InsertTables<"announcements">): Promise<ApiResponse> {
  return await createAction(async (_, supabase) => {
    const { gym_id, title, is_public } = announcementData
    
    // Duyuru ekle
    const { data: announcement, error } = await supabase
      .from("announcements")
      .insert(announcementData)
      .select()
      .single()
      
    if (error) {
      throw new Error(`Duyuru oluşturulurken hata: ${error.message}`)
    }

    // If the announcement is public, notify all active members
    if (is_public) {
      // Get all active memberships for this gym
      const { data: memberships } = await supabase
        .from("memberships")
        .select("user_id, status")
        .eq("gym_id", gym_id)
        .in("status", ["active", "approved_passive"])
      
      if (memberships && memberships.length > 0) {
        // Toplu bildirim için kullanıcı ID'lerini topla
        const userIds = memberships.map((member: Membership) => member.user_id)
        
        // Toplu bildirimleri oluştur
        await Promise.all(userIds.map((userId: string) => 
          createNotification({
            userId,
            title: "Yeni Salon Duyurusu",
            message: title,
            type: "new_announcement",
            relatedEntityType: "announcements",
            relatedEntityId: announcement.id
          })
        ))
      }
    }

    return announcement
  }, { revalidatePaths: ["/dashboard/manager"] })
}

/**
 * Salon duyurusunu günceller
 */
export async function updateGymAnnouncement(
  announcementId: string, 
  announcementData: UpdateTables<"announcements">
): Promise<ApiResponse> {
  return await createAction(async (_, supabase) => {
    const { data: announcement, error } = await supabase
      .from("announcements")
      .update(announcementData)
      .eq("id", announcementId)
      .select()
      .single()
      
    if (error) {
      throw new Error(`Duyuru güncellenirken hata: ${error.message}`)
    }
    
    return announcement
  }, { revalidatePaths: ["/dashboard/manager"] })
}

/**
 * Salon duyurusunu siler
 */
export async function deleteGymAnnouncement(announcementId: string): Promise<ApiResponse> {
  return await createAction(async (_, supabase) => {
    const { error } = await supabase
      .from("announcements")
      .delete()
      .eq("id", announcementId)
      
    if (error) {
      throw new Error(`Duyuru silinirken hata: ${error.message}`)
    }
    
    return { deleted: true }
  }, { revalidatePaths: ["/dashboard/manager"] })
}

/**
 * Salon için halka açık duyuruları getirir
 */
export async function getPublicAnnouncementsByGymId(gymId: string): Promise<ApiResponse> {
  return await createAction(async (_, supabase) => {
    const { data: announcements, error } = await supabase
      .from("announcements")
      .select("*")
      .eq("gym_id", gymId)
      .eq("is_public", true)
      .order("created_at", { ascending: false })
      
    if (error) {
      throw new Error(`Duyurular yüklenirken hata: ${error.message}`)
    }
    
    return announcements
  })
}

"use client"
import { useEffect, useState, useRef } from "react"  
import type { RealtimeChannel, RealtimePostgresChangesPayload} from "@supabase/supabase-js"
import { createClient } from "@/utils/supabase/client"

type SubscriptionCallback<T extends Record<string, any>> = (payload: RealtimePostgresChangesPayload<T>) => void

export function useRealtimeSubscription<T extends Record<string, any>>(
  table: string,
  callback: SubscriptionCallback<T>,
  options?: {
    event?: "INSERT" | "UPDATE" | "DELETE" | "*"
    filter?: string
    filterValues?: any[]
  },
) {
  const [channel, setChannel] = useState<RealtimeChannel | null>(null)
  const [isSubscribed, setIsSubscribed] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  
  // Store callback in a ref to prevent infinite resubscription
  const callbackRef = useRef(callback);
  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);
  
  // Store last options to avoid unnecessary resubscription
  const optionsRef = useRef(options);

  useEffect(() => {
    // Skip subscription if options is undefined
    if (!table || !options) {
      return;
    }
    
    // Check if options have changed in a way that requires resubscription
    const shouldResubscribe = 
      !optionsRef.current || 
      optionsRef.current.event !== options.event || 
      optionsRef.current.filter !== options.filter;
    
    if (!shouldResubscribe && channel) {
      return; // Keep existing subscription
    }
    
    // Update options ref
    optionsRef.current = options;
    
    // Unsubscribe from existing channel if any
    if (channel) {
      channel.unsubscribe();
    }
    
    const supabase = createClient()
    const event = options?.event || "*"

    const subscription = supabase
      .channel(`table-changes-${table}-${Date.now()}`) // Add timestamp to prevent channel name collisions
      .on(
        "postgres_changes" as any, // Type assertion to fix incompatible type error
        {
          event,
          schema: "public",
          table,
          ...(options?.filter ? { filter: options.filter, filterValues: options?.filterValues } : {}),
        },
        (payload: any) => {
          callbackRef.current(payload as RealtimePostgresChangesPayload<T>)
        },
      )
      .subscribe((status) => {
        if (status === "SUBSCRIBED") {
          setIsSubscribed(true)
        } else if (status === "CHANNEL_ERROR") {
          setError(new Error("Failed to subscribe to real-time changes"))
          setIsSubscribed(false)
        }
      })

    setChannel(subscription)

    return () => {
      subscription.unsubscribe()
    }
  }, [table, options])

  return { isSubscribed, error, channel }
}


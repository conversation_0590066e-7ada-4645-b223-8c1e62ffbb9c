---
description:
globs:
alwaysApply: false
---
# React Temel Kavramlar

## <PERSON><PERSON><PERSON><PERSON><PERSON> (Components)

React uygulamalarının temel yapı taşları bileşenlerdir. Bileşenler kendi mantı<PERSON>ını (JavaScript) ve görünümünü (JSX) yönetir.

```jsx
function Welcome(props) {
  return <h1><PERSON><PERSON><PERSON><PERSON>, {props.name}!</h1>;
}
```

- <PERSON><PERSON><PERSON><PERSON> adları her zaman büyük harfle başlar (PascalCase)
- Bileşenler ya fonksiyonel (önerilen) ya da sınıf tabanlı olabilir
- JSX döndürürler

## Props (Properties)

Üst bileşenden alt bileşene veri aktarmak için kullanılır. Props salt okunurdur, alt bileşen tarafından değiştirilemez.

```jsx
function UserCard(props) {
  return (
    <div>
      <h2>{props.username}</h2>
      <p>E-posta: {props.email}</p>
    </div>
  );
}

// Kullanımı:
<UserCard username="ayse_k" email="<EMAIL>" />
```

Varsayılan değerler belirtilebilir:

```jsx
function Button({ label, theme = 'primary' }) {
  // ...
}
```

## State (Durum)

Bileşenin zaman içinde değişebilen ve UI'ı etkileyen verileridir.

```jsx
import { useState } from 'react';

function Counter() {
  const [count, setCount] = useState(0);
  
  return (
    <div>
      <p>Sayı: {count}</p>
      <button onClick={() => setCount(count + 1)}>Artır</button>
    </div>
  );
}
```

- State değerini doğrudan değiştirmeyin, her zaman güncelleme fonksiyonunu kullanın
- State üst bileşene taşınarak (lifting state up) alt bileşenler arasında paylaşılabilir
- Nesneler veya diziler için değişmezlik (immutability) prensibine uyun

## JSX Kuralları

- Tek bir kök eleman döndürülmelidir
- Tüm etiketler kapatılmalıdır
- `class` yerine `className` kullanılmalıdır
- JavaScript ifadeleri süslü parantezler `{}` içinde kullanılabilir

## Koşullu Renderlama

```jsx
function Greeting({ isLoggedIn }) {
  if (isLoggedIn) {
    return <h1>Hoş geldiniz!</h1>;
  }
  return <h1>Lütfen giriş yapın.</h1>;
}

// Ternary operatör ile:
function LoginStatus({ isLoggedIn }) {
  return (
    <div>
      {isLoggedIn ? <LogoutButton /> : <LoginButton />}
    </div>
  );
}

// Mantıksal && operatörü ile:
function Messages({ unreadCount }) {
  return (
    <div>
      {unreadCount > 0 && (
        <p>{unreadCount} okunmamış mesaj</p>
      )}
    </div>
  );
}
```

## Olay Yönetimi (Event Handling)

```jsx
function ButtonClick() {
  const handleClick = () => {
    alert('Butona tıklandı!');
  };

  return (
    <button onClick={handleClick}>Tıkla</button>
  );
}
```

- React olay adları camelCase olarak yazılır (onClick, onMouseOver)
- Olaylara fonksiyon referansları geçilir, fonksiyon çağrıları değil
- Parametre geçmek için arrow function kullanılabilir: `onClick={(e) => handleClick(id, e)}`

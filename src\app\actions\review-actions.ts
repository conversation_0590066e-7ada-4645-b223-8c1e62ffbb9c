"use server"

import { createAction } from "@/lib/actions/core"
import { ApiResponse } from "@/lib/actions/types"
import { createNotification } from "@/lib/actions/notifications"
import { z } from "zod"
import type { InsertTables, UpdateTables } from "@/lib/supabase/types"

/**
 * Salon için değerlendirme/yorum ekler
 */
export async function submitGymReview(reviewData: InsertTables<"reviews">): Promise<ApiResponse> {
  return await createAction(async (_, supabase) => {
    const { user_id, gym_id, rating, comment } = reviewData
    
    // Kullanıcının aktif üyeliği var mı kontrol et
    const { data: membership } = await supabase
      .from("memberships")
      .select("*")
      .eq("user_id", user_id)
      .eq("gym_id", gym_id)
      .eq("status", "active")
      .maybeSingle()

    if (!membership) {
      throw new Error("Değerlendirme yapabilmek için aktif bir üyeliğiniz olmalıdır.")
    }

    // Kullanıcı daha önce değerlendirme yapmış mı kontrol et
    const { data: existingReview } = await supabase
      .from("reviews")
      .select("*")
      .eq("user_id", user_id)
      .eq("gym_id", gym_id)
      .maybeSingle()

    if (existingReview) {
      throw new Error("Bu salon için zaten bir değerlendirme yapmışsınız.")
    }

    // Değerlendirme oluştur
    const { data: review, error } = await supabase
      .from("reviews")
      .insert({
        ...reviewData,
        created_at: new Date().toISOString()
      })
      .select()
      .single()
      
    if (error) {
      throw new Error(`Değerlendirme oluşturulurken hata: ${error.message}`)
    }

    // Salon bilgilerini al
    const { data: gym, error: gymError } = await supabase
      .from("gyms")
      .select("*")
      .eq("id", gym_id)
      .single()

    if (gymError || !gym) {
      throw new Error("Salon bilgileri alınamadı.")
    }

    // Kullanıcı bilgilerini al
    const { data: user, error: userError } = await supabase
      .from("users")
      .select("name, surname")
      .eq("id", user_id)
      .single()

    // Salon yöneticisine bildirim gönder
    await createNotification({
      userId: gym.manager_id,
      title: "Yeni Değerlendirme",
      message: `${user?.name || "Bir üye"} salonunuzu ${rating} yıldız ile değerlendirdi.`,
      type: "new_review",
      relatedEntityType: "reviews",
      relatedEntityId: review.id
    })

    return review
  }, { revalidatePaths: [`/gyms/${reviewData.gym_id}`] })
}

/**
 * Değerlendirmeyi günceller
 */
export async function updateGymReview(
  reviewId: string, 
  reviewData: UpdateTables<"reviews">
): Promise<ApiResponse> {
  return await createAction(async (_, supabase) => {
    // Değerlendirmeyi güncelle
    const { data: review, error } = await supabase
      .from("reviews")
      .update({
        ...reviewData,
        updated_at: new Date().toISOString()
      })
      .eq("id", reviewId)
      .select()
      .single()
      
    if (error) {
      throw new Error(`Değerlendirme güncellenirken hata: ${error.message}`)
    }

    return review
  }, { revalidatePaths: [`/gyms/${reviewData.gym_id}`] })
}

/**
 * Değerlendirmeyi siler
 */
export async function deleteGymReview(reviewId: string): Promise<ApiResponse> {
  return await createAction(async (_, supabase) => {
    // Önce değerlendirme bilgilerini al (gym_id için gerekli)
    const { data: review, error: fetchError } = await supabase
      .from("reviews")
      .select("gym_id")
      .eq("id", reviewId)
      .single()
      
    if (fetchError) {
      throw new Error("Değerlendirme bulunamadı.")
    }
    
    // Değerlendirmeyi sil
    const { error } = await supabase
      .from("reviews")
      .delete()
      .eq("id", reviewId)
      
    if (error) {
      throw new Error(`Değerlendirme silinirken hata: ${error.message}`)
    }

    return { 
      deleted: true,
      gymId: review.gym_id
    }
  }, { revalidatePaths: ["/dashboard/member"] })
}

/**
 * Salon için değerlendirmeleri getirir
 */
export async function getReviewsByGymId(gymId: string): Promise<ApiResponse> {
  return await createAction(async (_, supabase) => {
    const { data: reviews, error } = await supabase
      .from("reviews")
      .select(`
        *,
        user:users(id, name, surname, profile_picture_url)
      `)
      .eq("gym_id", gymId)
      .order("created_at", { ascending: false })
      
    if (error) {
      throw new Error(`Değerlendirmeler yüklenirken hata: ${error.message}`)
    }

    // Calculate average rating
    let averageRating = 0;
    if (reviews && reviews.length > 0) {
      const sum = reviews.reduce((acc: number, review: any) => acc + review.rating, 0);
      averageRating = sum / reviews.length;
    }
    
    return {
      reviews,
      averageRating
    }
  })
}

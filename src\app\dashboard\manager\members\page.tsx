"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/components/auth/auth-provider";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Users,
  Search,
  Filter,
  Download,
  Eye,
  Building,
  Calendar,
  CreditCard,
  UserCheck,
  UserX,
  Loader2,
  AlertTriangle,
} from "lucide-react";
import { cn } from "@/lib/utils";

interface Member {
  id: string;
  name: string;
  surname: string;
  email: string;
  phone: string;
  gymName: string;
  gymSlug: string;
  membershipStatus: "active" | "passive" | "pending" | "cancelled";
  subscriptionStatus: "active" | "expired" | "pending";
  joinDate: string;
  lastActivity: string;
  totalSpent: number;
  packageName: string;
}

export default function AllMembersPage() {
  const { authUser } = useAuth();
  const [members, setMembers] = useState<Member[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [gymFilter, setGymFilter] = useState<string>("all");

  useEffect(() => {
    const loadMembers = async () => {
      if (!authUser) {
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        // Mock data - gerçek implementasyonda API'den gelecek
        const mockMembers: Member[] = [
          {
            id: "1",
            name: "Ahmet",
            surname: "Yılmaz",
            email: "<EMAIL>",
            phone: "0532 123 4567",
            gymName: "Merkez Spor Salonu",
            gymSlug: "merkez-spor-salonu",
            membershipStatus: "active",
            subscriptionStatus: "active",
            joinDate: "2024-01-15",
            lastActivity: "2024-01-20",
            totalSpent: 1200,
            packageName: "Premium Aylık",
          },
          {
            id: "2",
            name: "Fatma",
            surname: "Demir",
            email: "<EMAIL>",
            phone: "0533 987 6543",
            gymName: "Şube 1 Fitness",
            gymSlug: "sube-1-fitness",
            membershipStatus: "active",
            subscriptionStatus: "expired",
            joinDate: "2023-12-10",
            lastActivity: "2024-01-18",
            totalSpent: 2400,
            packageName: "Standart Aylık",
          },
          {
            id: "3",
            name: "Mehmet",
            surname: "Kaya",
            email: "<EMAIL>",
            phone: "0534 555 1234",
            gymName: "Şube 2 Gym",
            gymSlug: "sube-2-gym",
            membershipStatus: "pending",
            subscriptionStatus: "pending",
            joinDate: "2024-01-19",
            lastActivity: "2024-01-19",
            totalSpent: 0,
            packageName: "Basic Aylık",
          },
          {
            id: "4",
            name: "Ayşe",
            surname: "Öztürk",
            email: "<EMAIL>",
            phone: "0535 777 8888",
            gymName: "Merkez Spor Salonu",
            gymSlug: "merkez-spor-salonu",
            membershipStatus: "active",
            subscriptionStatus: "active",
            joinDate: "2023-11-05",
            lastActivity: "2024-01-21",
            totalSpent: 3600,
            packageName: "Premium Yıllık",
          },
          {
            id: "5",
            name: "Can",
            surname: "Arslan",
            email: "<EMAIL>",
            phone: "0536 999 0000",
            gymName: "Şube 1 Fitness",
            gymSlug: "sube-1-fitness",
            membershipStatus: "passive",
            subscriptionStatus: "expired",
            joinDate: "2023-08-20",
            lastActivity: "2024-01-10",
            totalSpent: 1800,
            packageName: "Standart Aylık",
          },
        ];

        // Simulated API delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        setMembers(mockMembers);
      } catch (err) {
        console.error("Error loading members:", err);
        setError("Üye verileri yüklenirken bir hata oluştu.");
      } finally {
        setIsLoading(false);
      }
    };

    loadMembers();
  }, [authUser]);

  const getMembershipStatusBadge = (status: Member["membershipStatus"]) => {
    switch (status) {
      case "active":
        return <Badge className="bg-green-100 text-green-800">Aktif</Badge>;
      case "passive":
        return <Badge className="bg-yellow-100 text-yellow-800">Pasif</Badge>;
      case "pending":
        return <Badge className="bg-blue-100 text-blue-800">Beklemede</Badge>;
      case "cancelled":
        return <Badge className="bg-red-100 text-red-800">İptal</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const getSubscriptionStatusBadge = (status: Member["subscriptionStatus"]) => {
    switch (status) {
      case "active":
        return <Badge className="bg-green-100 text-green-800">Aktif</Badge>;
      case "expired":
        return <Badge className="bg-red-100 text-red-800">Süresi Dolmuş</Badge>;
      case "pending":
        return <Badge className="bg-yellow-100 text-yellow-800">Beklemede</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("tr-TR");
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("tr-TR", {
      style: "currency",
      currency: "TRY",
    }).format(amount);
  };

  const filteredMembers = members.filter(member => {
    const matchesSearch = 
      member.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      member.surname.toLowerCase().includes(searchTerm.toLowerCase()) ||
      member.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      member.phone.includes(searchTerm);

    const matchesStatus = statusFilter === "all" || member.membershipStatus === statusFilter;
    const matchesGym = gymFilter === "all" || member.gymSlug === gymFilter;

    return matchesSearch && matchesStatus && matchesGym;
  });

  const uniqueGyms = Array.from(new Set(members.map(m => ({ name: m.gymName, slug: m.gymSlug }))))
    .map(gym => gym);

  const stats = {
    total: members.length,
    active: members.filter(m => m.membershipStatus === "active").length,
    pending: members.filter(m => m.membershipStatus === "pending").length,
    passive: members.filter(m => m.membershipStatus === "passive").length,
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="flex items-center gap-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Üye verileri yükleniyor...</span>
        </div>
      </div>
    );
  }

  if (!authUser) {
    return (
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          Üye listesini görüntülemek için giriş yapmanız gerekiyor.
        </AlertDescription>
      </Alert>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <Users className="h-8 w-8" />
            Tüm Üyeler
          </h1>
          <p className="text-muted-foreground">
            Tüm salonlarınızdaki üyelerin merkezi listesi
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" className="gap-2">
            <Filter className="h-4 w-4" />
            Filtrele
          </Button>
          <Button className="gap-2">
            <Download className="h-4 w-4" />
            Dışa Aktar
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Toplam Üye</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Aktif Üye</CardTitle>
            <UserCheck className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.active}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Bekleyen Üye</CardTitle>
            <Calendar className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{stats.pending}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Pasif Üye</CardTitle>
            <UserX className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stats.passive}</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filtreler</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Üye ara (isim, email, telefon)..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Durum seçin" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tüm Durumlar</SelectItem>
                <SelectItem value="active">Aktif</SelectItem>
                <SelectItem value="passive">Pasif</SelectItem>
                <SelectItem value="pending">Beklemede</SelectItem>
                <SelectItem value="cancelled">İptal</SelectItem>
              </SelectContent>
            </Select>
            <Select value={gymFilter} onValueChange={setGymFilter}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Salon seçin" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tüm Salonlar</SelectItem>
                {uniqueGyms.map((gym) => (
                  <SelectItem key={gym.slug} value={gym.slug}>
                    {gym.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Members Table */}
      <Card>
        <CardHeader>
          <CardTitle>Üye Listesi ({filteredMembers.length})</CardTitle>
          <CardDescription>
            Filtrelenmiş üye listesi ve detayları
          </CardDescription>
        </CardHeader>
        <CardContent>
          {filteredMembers.length === 0 ? (
            <div className="text-center py-8">
              <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-lg font-medium text-muted-foreground">Üye bulunamadı</p>
              <p className="text-sm text-muted-foreground">
                Arama kriterlerinizi değiştirmeyi deneyin
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Üye</TableHead>
                    <TableHead>Salon</TableHead>
                    <TableHead>Üyelik Durumu</TableHead>
                    <TableHead>Abonelik Durumu</TableHead>
                    <TableHead>Katılım Tarihi</TableHead>
                    <TableHead>Son Aktivite</TableHead>
                    <TableHead>Toplam Harcama</TableHead>
                    <TableHead>İşlemler</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredMembers.map((member) => (
                    <TableRow key={member.id}>
                      <TableCell>
                        <div>
                          <p className="font-medium">{member.name} {member.surname}</p>
                          <p className="text-sm text-muted-foreground">{member.email}</p>
                          <p className="text-sm text-muted-foreground">{member.phone}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Building className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm">{member.gymName}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        {getMembershipStatusBadge(member.membershipStatus)}
                      </TableCell>
                      <TableCell>
                        {getSubscriptionStatusBadge(member.subscriptionStatus)}
                      </TableCell>
                      <TableCell>{formatDate(member.joinDate)}</TableCell>
                      <TableCell>{formatDate(member.lastActivity)}</TableCell>
                      <TableCell>{formatCurrency(member.totalSpent)}</TableCell>
                      <TableCell>
                        <Button variant="ghost" size="sm" className="gap-2">
                          <Eye className="h-4 w-4" />
                          Detay
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

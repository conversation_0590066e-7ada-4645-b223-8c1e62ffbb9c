"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import {
  CheckCircle,
  AlertCircle,
  Dumbbell,
  ArrowRight,
  Crown,
  Star,
  Zap,
  Loader2,
  AlertTriangle,
  CreditCard,
  Edit3,
} from "lucide-react";
import { createGym, getMyGyms } from "@/app/actions/gym-actions";
import { getManagerGymLimit } from "@/app/actions/subscription-actions";
import { useAuth } from "@/components/auth/auth-provider";
import { Tables } from "@/lib/supabase/types";

type SubscriptionTier = "starter" | "professional" | "enterprise";
type GymData = Tables<"gyms">;

export function GymSetupClient() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { authUser } = useAuth();

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [showSuccess, setShowSuccess] = useState(false);

  // Subscription and limit states
  const [isCheckingLimits, setIsCheckingLimits] = useState(true);
  const [subscriptionTier, setSubscriptionTier] =
    useState<SubscriptionTier>("starter");
  const [maxGyms, setMaxGyms] = useState<number | null>(1);
  const [currentGymCount, setCurrentGymCount] = useState<number>(0);
  const [canCreateGym, setCanCreateGym] = useState<boolean>(true);
  const [userGyms, setUserGyms] = useState<GymData[]>([]);

  // Form state
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    phone: "",
    email: "",
    address: "",
    city: "",
    district: "",
    gym_type: "",
  });

  useEffect(() => {
    // URL'den success parametresi varsa başarı mesajını göster
    if (searchParams.get("success") === "true") {
      setShowSuccess(true);
    }
  }, [searchParams]);

  // Check subscription limits when component mounts
  useEffect(() => {
    const checkSubscriptionLimits = async () => {
      if (!authUser) {
        setIsCheckingLimits(false);
        return;
      }

      try {
        setIsCheckingLimits(true);

        // Get manager's subscription limits
        const limitResponse = await getManagerGymLimit(authUser.id);
        if (limitResponse.success && limitResponse.data) {
          setSubscriptionTier(limitResponse.data.tier as SubscriptionTier);
          setMaxGyms(limitResponse.data.maxGyms);
        }

        // Get current gym count and gym data
        const gymsResponse = await getMyGyms();
        if (gymsResponse.success && gymsResponse.data) {
          const gymCount = gymsResponse.data.length;
          setCurrentGymCount(gymCount);
          setUserGyms(gymsResponse.data);

          // Check if user can create more gyms
          const maxGymLimit = limitResponse.data?.maxGyms;
          const canCreate = !maxGymLimit || gymCount < maxGymLimit;
          setCanCreateGym(canCreate);
        }
      } catch (error) {
        console.error("Error checking subscription limits:", error);
        // Set conservative defaults on error
        setCanCreateGym(false);
      } finally {
        setIsCheckingLimits(false);
      }
    };

    checkSubscriptionLimits();
  }, [authUser]);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (value: string) => {
    setFormData((prev) => ({ ...prev, gym_type: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!authUser) {
      router.push("/login");
      return;
    }

    // Double-check if user can create gym before submitting
    if (!canCreateGym) {
      setError("Salon oluşturma limitinize ulaştınız. Paketinizi yükseltin.");
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // FormData oluştur
      const formDataToSend = new FormData();
      formDataToSend.append("userId", authUser.id);
      formDataToSend.append("name", formData.name);
      formDataToSend.append("description", formData.description);
      formDataToSend.append("phone", formData.phone);
      formDataToSend.append("email", formData.email);
      formDataToSend.append("address", formData.address);
      formDataToSend.append("city", formData.city);
      formDataToSend.append("district", formData.district);
      formDataToSend.append("gym_type", formData.gym_type);

      const result = await createGym(formDataToSend);

      if (result.error) {
        setError(result.error);
        return;
      }

      setSuccess(
        "Salon başarıyla oluşturuldu! Dashboard'a yönlendiriliyorsunuz..."
      );

      // 2 saniye sonra dashboard'a yönlendir
      setTimeout(() => {
        router.push("/dashboard/manager");
      }, 2000);
    } catch (error: any) {
      setError(error.message || "Salon oluşturulurken bir hata oluştu.");
    } finally {
      setIsLoading(false);
    }
  };

  // Helper functions for tier information
  const getTierIcon = (tier: SubscriptionTier) => {
    switch (tier) {
      case "starter":
        return <Zap className="h-5 w-5" />;
      case "professional":
        return <Star className="h-5 w-5" />;
      case "enterprise":
        return <Crown className="h-5 w-5" />;
    }
  };

  const getTierName = (tier: SubscriptionTier) => {
    switch (tier) {
      case "starter":
        return "Başlangıç";
      case "professional":
        return "Profesyonel";
      case "enterprise":
        return "Kurumsal";
    }
  };

  const getTierColor = (tier: SubscriptionTier) => {
    switch (tier) {
      case "starter":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300";
      case "professional":
        return "bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300";
      case "enterprise":
        return "bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300";
    }
  };

  // Gym list component
  const GymListComponent = ({
    title,
    showAddButton = false,
  }: {
    title: string;
    showAddButton?: boolean;
  }) => {
    if (userGyms.length === 0) {
      return (
        <Card className="max-w-2xl mx-auto mb-6 bg-white dark:bg-slate-800 border-slate-200 dark:border-slate-700">
          <CardContent className="p-6 text-center">
            <div className="h-12 w-12 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center mx-auto mb-3">
              <Dumbbell className="h-6 w-6 text-gray-400" />
            </div>
            <p className="text-gray-600 dark:text-gray-300 mb-2">
              Henüz salon oluşturmamışsınız
            </p>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              İlk salonunuzu oluşturmak için aşağıdaki formu doldurun
            </p>
          </CardContent>
        </Card>
      );
    }

    return (
      <Card className="max-w-2xl mx-auto mb-6 bg-white dark:bg-slate-800 border-slate-200 dark:border-slate-700">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg text-gray-900 dark:text-white">
              {title}
            </CardTitle>
            {showAddButton && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => router.push("/pricing")}
              >
                <CreditCard className="h-4 w-4 mr-2" />
                Paket Yükselt
              </Button>
            )}
          </div>
          <CardDescription className="text-gray-600 dark:text-gray-300">
            {userGyms.length} salon oluşturuldu
          </CardDescription>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="grid gap-3">
            {userGyms.map((gym) => (
              <div
                key={gym.id}
                className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-slate-700 rounded-lg hover:bg-gray-100 dark:hover:bg-slate-600 transition-colors"
              >
                <div className="h-10 w-10 rounded-lg bg-primary/10 dark:bg-primary/20 flex items-center justify-center flex-shrink-0">
                  {gym.logo_url ? (
                    <img
                      src={gym.logo_url}
                      alt={gym.name}
                      className="h-8 w-8 rounded object-cover"
                    />
                  ) : (
                    <Dumbbell className="h-5 w-5 text-primary" />
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2">
                    <p className="font-medium text-gray-900 dark:text-white truncate">
                      {gym.name}
                    </p>
                    <Badge
                      variant="outline"
                      className="text-xs px-2 py-0 bg-green-50 text-green-700 border-green-200 dark:bg-green-900/30 dark:text-green-300 dark:border-green-800"
                    >
                      {gym.status === "active" ? "Aktif" : gym.status}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-4 mt-1">
                    {gym.city && (
                      <div className="flex items-center gap-1 text-xs text-gray-500 dark:text-gray-400">
                        <span>📍</span>
                        <span>
                          {gym.city}
                          {gym.district && `, ${gym.district}`}
                        </span>
                      </div>
                    )}
                    <div className="flex items-center gap-1 text-xs text-gray-500 dark:text-gray-400">
                      <span>🔗</span>
                      <span className="font-mono">/{gym.slug}</span>
                    </div>
                  </div>
                </div>
                <div className="flex gap-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() =>
                      router.push(`/dashboard/manager/gyms/edit/${gym.id}`)
                    }
                    className="flex-shrink-0"
                    title="Salonu Düzenle"
                  >
                    <Edit3 className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() =>
                      router.push(`/dashboard/manager/gym/${gym.slug}`)
                    }
                    className="flex-shrink-0"
                    title="Salon Dashboard'ına Git"
                  >
                    <ArrowRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  };

  // Show loading state while checking limits
  if (isCheckingLimits) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 flex items-center justify-center">
        <Card className="w-full max-w-md bg-white dark:bg-slate-800 border-slate-200 dark:border-slate-700">
          <CardContent className="flex flex-col items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
            <p className="text-muted-foreground">
              Abonelik bilgileriniz kontrol ediliyor...
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (showSuccess) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 flex items-center justify-center">
        <Card className="w-full max-w-md bg-white dark:bg-slate-800 border-slate-200 dark:border-slate-700">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 h-12 w-12 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center">
              <CheckCircle className="h-6 w-6 text-green-600 dark:text-green-400" />
            </div>
            <CardTitle className="text-2xl text-gray-900 dark:text-white">
              Ödeme Başarılı!
            </CardTitle>
            <CardDescription className="text-gray-600 dark:text-gray-300">
              Paketiniz başarıyla satın alındı. Şimdi salonunuzun bilgilerini
              girin.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button className="w-full" onClick={() => setShowSuccess(false)}>
              Salon Kurulumuna Devam Et
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Show limit reached message if user cannot create more gyms
  if (!canCreateGym) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
        <div className="container mx-auto px-4 py-8">
          {/* Header */}
          <div className="text-center mb-8">
            <div className="mx-auto mb-4 h-16 w-16 rounded-full bg-orange-100 dark:bg-orange-900/30 flex items-center justify-center">
              <AlertTriangle className="h-8 w-8 text-orange-600 dark:text-orange-400" />
            </div>
            <h1 className="text-3xl font-bold mb-2 text-gray-900 dark:text-white">
              Salon Oluşturma Limiti
            </h1>
            <p className="text-muted-foreground dark:text-gray-300 max-w-2xl mx-auto">
              Mevcut paketinizle salon oluşturma limitinize ulaştınız. Daha
              fazla salon oluşturmak için paketinizi yükseltin.
            </p>
          </div>

          {/* Subscription Status Card */}
          <Card className="max-w-2xl mx-auto mb-6 bg-white dark:bg-slate-800 border-slate-200 dark:border-slate-700">
            <CardHeader>
              <CardTitle className="flex items-center gap-3 text-gray-900 dark:text-white">
                {getTierIcon(subscriptionTier)}
                Mevcut Paketiniz: {getTierName(subscriptionTier)}
              </CardTitle>
              <CardDescription className="text-gray-600 dark:text-gray-300">
                Salon oluşturma durumunuz
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Current Status */}
              <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-slate-700 rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="h-10 w-10 rounded-full bg-red-100 dark:bg-red-900/30 flex items-center justify-center">
                    <AlertTriangle className="h-5 w-5 text-red-600 dark:text-red-400" />
                  </div>
                  <div>
                    <p className="font-medium text-gray-900 dark:text-white">
                      Limit Doldu
                    </p>
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      {currentGymCount} / {maxGyms || "∞"} salon oluşturuldu
                    </p>
                  </div>
                </div>
                <Badge className={getTierColor(subscriptionTier)}>
                  {getTierName(subscriptionTier)}
                </Badge>
              </div>

              {/* Upgrade Options */}
              <div className="space-y-4">
                <h3 className="font-semibold text-gray-900 dark:text-white">
                  Paket Yükseltme Seçenekleri
                </h3>

                {subscriptionTier === "starter" && (
                  <div className="grid gap-3">
                    <div className="p-4 border border-purple-200 dark:border-purple-800 rounded-lg bg-purple-50 dark:bg-purple-900/20">
                      <div className="flex items-center gap-2 mb-2">
                        <Star className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                        <span className="font-medium text-purple-900 dark:text-purple-100">
                          Profesyonel Paket
                        </span>
                      </div>
                      <p className="text-sm text-purple-700 dark:text-purple-300 mb-3">
                        3 salona kadar oluşturabilirsiniz
                      </p>
                    </div>
                    <div className="p-4 border border-amber-200 dark:border-amber-800 rounded-lg bg-amber-50 dark:bg-amber-900/20">
                      <div className="flex items-center gap-2 mb-2">
                        <Crown className="h-5 w-5 text-amber-600 dark:text-amber-400" />
                        <span className="font-medium text-amber-900 dark:text-amber-100">
                          Kurumsal Paket
                        </span>
                      </div>
                      <p className="text-sm text-amber-700 dark:text-amber-300 mb-3">
                        Sınırsız salon oluşturabilirsiniz
                      </p>
                    </div>
                  </div>
                )}

                {subscriptionTier === "professional" && (
                  <div className="p-4 border border-amber-200 dark:border-amber-800 rounded-lg bg-amber-50 dark:bg-amber-900/20">
                    <div className="flex items-center gap-2 mb-2">
                      <Crown className="h-5 w-5 text-amber-600 dark:text-amber-400" />
                      <span className="font-medium text-amber-900 dark:text-amber-100">
                        Kurumsal Paket
                      </span>
                    </div>
                    <p className="text-sm text-amber-700 dark:text-amber-300 mb-3">
                      Sınırsız salon oluşturabilirsiniz
                    </p>
                  </div>
                )}
              </div>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-3">
                <Button
                  onClick={() => router.push("/pricing")}
                  className="flex-1"
                >
                  <CreditCard className="mr-2 h-4 w-4" />
                  Paket Yükselt
                </Button>
                <Button
                  variant="outline"
                  onClick={() => router.push("/dashboard/manager")}
                  className="flex-1"
                >
                  Dashboard'a Dön
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Current Gyms List */}
          <GymListComponent title="Mevcut Salonlarınız" showAddButton={true} />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="mx-auto mb-4 h-16 w-16 rounded-full bg-primary/10 dark:bg-primary/20 flex items-center justify-center">
            <Dumbbell className="h-8 w-8 text-primary dark:text-primary-foreground" />
          </div>
          <h1 className="text-3xl font-bold mb-2 text-gray-900 dark:text-white">
            Salon Kurulumu
          </h1>
          <p className="text-muted-foreground dark:text-gray-300 max-w-2xl mx-auto">
            Salonunuzun temel bilgilerini girin ve Sportiva'yı kullanmaya
            başlayın. Bu bilgileri daha sonra ayarlar bölümünden
            değiştirebilirsiniz.
          </p>
        </div>

        {/* Subscription Status Info */}
        <Card className="max-w-2xl mx-auto mb-6 bg-white dark:bg-slate-800 border-slate-200 dark:border-slate-700">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                {getTierIcon(subscriptionTier)}
                <div>
                  <p className="font-medium text-gray-900 dark:text-white">
                    {getTierName(subscriptionTier)} Paket
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    {currentGymCount} / {maxGyms || "∞"} salon oluşturuldu
                  </p>
                </div>
              </div>
              <Badge className={getTierColor(subscriptionTier)}>
                {maxGyms ? `${maxGyms - currentGymCount} kalan` : "Sınırsız"}
              </Badge>
            </div>
          </CardContent>
        </Card>

        {/* Current Gyms List */}
        <GymListComponent title="Mevcut Salonlarınız" />

        {/* Form */}
        <Card className="max-w-2xl mx-auto bg-white dark:bg-slate-800 border-slate-200 dark:border-slate-700">
          <CardHeader>
            <CardTitle className="text-gray-900 dark:text-white">
              Salon Bilgileri
            </CardTitle>
            <CardDescription className="text-gray-600 dark:text-gray-300">
              Salonunuzun temel bilgilerini girin
            </CardDescription>
          </CardHeader>
          <form onSubmit={handleSubmit}>
            <CardContent className="space-y-6">
              {error && (
                <Alert
                  variant="destructive"
                  className="bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800"
                >
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription className="text-red-800 dark:text-red-200">
                    {error}
                  </AlertDescription>
                </Alert>
              )}

              {success && (
                <Alert className="bg-green-50 dark:bg-green-900/20 text-green-800 dark:text-green-200 border-green-200 dark:border-green-800">
                  <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />
                  <AlertDescription className="text-green-800 dark:text-green-200">
                    {success}
                  </AlertDescription>
                </Alert>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label
                    htmlFor="name"
                    className="text-gray-700 dark:text-gray-300"
                  >
                    Salon Adı *
                  </Label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    placeholder="Örn: Fitness Plus"
                    required
                    className="bg-white dark:bg-slate-700 border-slate-300 dark:border-slate-600 text-gray-900 dark:text-white placeholder:text-gray-500 dark:placeholder:text-gray-400"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="gym_type">Salon Türü *</Label>
                  <Select
                    value={formData.gym_type}
                    onValueChange={handleSelectChange}
                    required
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Salon türünü seçin" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="fitness">Fitness</SelectItem>
                      <SelectItem value="crossfit">CrossFit</SelectItem>
                      <SelectItem value="yoga">Yoga</SelectItem>
                      <SelectItem value="pilates">Pilates</SelectItem>
                      <SelectItem value="boxing">Boks</SelectItem>
                      <SelectItem value="martial_arts">
                        Dövüş Sanatları
                      </SelectItem>
                      <SelectItem value="swimming">Yüzme</SelectItem>
                      <SelectItem value="other">Diğer</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Açıklama</Label>
                <Textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  placeholder="Salonunuz hakkında kısa bir açıklama..."
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="phone">Telefon *</Label>
                  <Input
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    placeholder="0555 123 45 67"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email">E-posta *</Label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    placeholder="<EMAIL>"
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="address">Adres *</Label>
                <Textarea
                  id="address"
                  name="address"
                  value={formData.address}
                  onChange={handleInputChange}
                  placeholder="Tam adres bilgisi..."
                  rows={2}
                  required
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="city">Şehir *</Label>
                  <Input
                    id="city"
                    name="city"
                    value={formData.city}
                    onChange={handleInputChange}
                    placeholder="İstanbul"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="district">İlçe *</Label>
                  <Input
                    id="district"
                    name="district"
                    value={formData.district}
                    onChange={handleInputChange}
                    placeholder="Kadıköy"
                    required
                  />
                </div>
              </div>
            </CardContent>

            <div className="px-6 pb-6">
              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                    Salon Oluşturuluyor...
                  </>
                ) : (
                  <>
                    Salon Kurulumunu Tamamla
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </>
                )}
              </Button>
            </div>
          </form>
        </Card>
      </div>
    </div>
  );
}

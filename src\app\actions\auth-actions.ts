"use server"
import { createAction} from "@/lib/actions/core"
import { ApiResponse } from "@/lib/actions/types"
import { z } from "zod"
import { revalidatePath } from "next/cache"
import { redirect } from "next/navigation"
import { getSupabaseAdmin } from "@/utils/supabase/admin"

type RawData = {
  email: string
  password: string
  name?: string | null
  surname?: string | null
}
/**
 * Güçlü şifre ve form validasyonu için şema tanımları
 */
const registerSchema = z.object({
  name: z.string().min(2, { message: "İsim en az 2 karakter olmalıdır" }),
  surname: z.string().min(2, { message: "Soyisim en az 2 karakter olmalıdır" }),
  email: z.string().email({ message: "Geçerli bir e-posta adresi giriniz" }),
  password: z.string()
    .min(6, { message: "Şifre en az 6 karakter olmalıdır" })
    .regex(/[a-z]/, { message: "Şifrede en az bir küçük harf olmalıdır" })
    .regex(/[A-Z]/, { message: "Şifrede en az bir büyük harf olmalıdır" })
    .regex(/[0-9]/, { message: "Şifrede en az bir rakam olmalıdır" }),
})

const loginSchema = z.object({
  email: z.string().email({ message: "Geçerli bir e-posta adresi giriniz" }),
  password: z.string().min(1, { message: "Şifre boş olamaz" }),
})

/**
 * Kullanıcı kaydı oluşturur
 */
export async function registerUser(prevState: ApiResponse, formData: FormData): Promise<ApiResponse> {
 

  const rawData: RawData = {
    email: formData.get("email") as string,
    password: formData.get("password") as string,
    name: formData.get("name") as string,
    surname: formData.get("surname") as string,
  }

  // Form validasyonu
  const validationResult = registerSchema.safeParse(rawData)

  if (!validationResult.success) {
    const errors = validationResult.error.flatten().fieldErrors
    const firstError = Object.values(errors)[0]?.[0] || 'Geçersiz form verisi'
    return {
      success: false,
      error: firstError,
      message: "Lütfen formu dikkatlice doldurunuz.",
      data: {
        name: rawData.name,
        email: rawData.email
      }
    }
  }

  try {
    return await createAction(async (_, supabase) => {
      const adminClient = getSupabaseAdmin()

      // Aynı email ile kullanıcı var mı kontrol et
      const { data: existingUser } = await adminClient
        .from("users")
        .select("id")
        .eq("email", rawData.email)
        .maybeSingle()

      if (existingUser) {
        throw new Error("Bu e-posta adresi zaten kullanılıyor.")
      }
      // Yeni kullanıcı oluştur
      const { data, error: signUpError } = await supabase.auth.signUp({
        email: rawData.email,
        password: rawData.password,
      })

      const userId = data.user?.id

      if (signUpError || !userId) {
        throw new Error(`Kayıt olurken hata: ${signUpError?.message ?? "Bilinmeyen hata"}`)
      }

      // Profil kaydı
      const { error: profileError } = await supabase.from("users").insert({
        id: userId,
        email: rawData.email,
        name: rawData.name,
        surname: rawData.surname,
        created_at: new Date().toISOString(),
      })

      // Profil kaydı başarısızsa auth.users kaydını da sil
      if (profileError) {
        await adminClient.auth.admin.deleteUser(userId)
        throw new Error(`Profil oluşturulurken hata: ${profileError.message}`)
      }

      // Yolları yeniden doğrula
      revalidatePath('/dashboard/member')

      return { 
        success: true,
        message: "Kayıt işlemi başarılı.",
        data: {
          user: data.user
        }
      }
    })
  } catch (error: any) {
    // Kullanıcı dostu hata mesajları
    let userMessage = "Kayıt işlemi sırasında bir hata oluştu"
    
    if (error.message.includes("e-posta adresi zaten")) {
      userMessage = "Bu e-posta adresi zaten kullanılıyor."
    } else if (error.message.includes("Profil oluşturulurken")) {
      userMessage = "Profil bilgileriniz kaydedilirken bir sorun oluştu. Lütfen tekrar deneyin."
    }
    
    return {
      success: false,
      error: userMessage,
      data: {
        name: rawData.name,
        email: rawData.email
      }
    }
  }
}

/**
 * Kullanıcı giriş yapar
 */
export async function loginUser(prevState: ApiResponse, formData: FormData): Promise<ApiResponse> {
  const rawData: RawData = {
    email: formData.get("email") as string,
    password: formData.get("password") as string,
  }

  // Form validasyonu
  const validationResult = loginSchema.safeParse(rawData)
  if (!validationResult.success) {
    const errors = validationResult.error.flatten().fieldErrors
    const firstError = Object.values(errors)[0]?.[0] || 'Geçersiz form verisi'
    return { 
      success: false, 
      error: firstError,
      message: "Lütfen formu dikkatlice doldurunuz.",
      data: {
        email: rawData.email
      }
    }
  }
  
  try {
    return await createAction(async (_, supabase) => {
      const { data, error: signInError } = await supabase.auth.signInWithPassword({
        email: rawData.email,
        password: rawData.password,
      })
      
      if (signInError) {
        // Kullanıcı dostu hata mesajları
        let userMessage = "Giriş yapılırken hata oluştu."
        if (signInError.message.includes("Invalid login credentials")) {
          userMessage = "Geçersiz e-posta veya şifre."
        } else if (signInError.message.includes("Email not confirmed")) {
          userMessage = "E-posta adresiniz henüz doğrulanmamış. Lütfen e-postanızı kontrol edin."
        }
        
        throw new Error(userMessage)
      }

      // Kullanıcı oturumunu yeniden doğrula
      revalidatePath('/dashboard')
      
      return {
        success: true,
        message: "Giriş başarılı",
        data: {
          user: data.user
        }
      }
    })
  } catch (error: any) {
    return {
      success: false,
      error: error.message || "Giriş yapılırken bir hata oluştu",
      data: {
        email: rawData.email
      }
    }
  }
}

/**
 * Kullanıcı çıkış yapar
 */
export async function logoutUser(): Promise<ApiResponse> {
  try {
    return await createAction(async (_, supabase) => {
      const { error } = await supabase.auth.signOut()
      
      if (error) {
        throw new Error(`Çıkış yapılırken hata: ${error.message}`)
      }
      
      // Oturum bilgilerini yeniden doğrula
      revalidatePath('/')
      redirect('/')
      
      return { 
        success: true, 
        message: "Başarıyla çıkış yapıldı" 
      }
    })
  } catch (error: any) {
    return {
      success: false,
      error: error.message || "Çıkış yapılırken bir hata oluştu",
    }
  }
}

/**
 * Şifre sıfırlama e-postası gönderir
 */
export async function sendPasswordResetEmail(email: string): Promise<ApiResponse> {
  // E-posta validasyonu
  if (!email || !email.includes('@')) {
    return {
      success: false,
      error: "Geçerli bir e-posta adresi giriniz",
      data: { email }
    }
  }
  
  try {
    return await createAction(async (_, supabase) => {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/auth/callback?redirectTo=/login?success=true&type=password_reset`,
      })
      
      if (error) {
        throw new Error(`Şifre sıfırlama başlatılırken hata: ${error.message}`)
      }
      
      return {
        success: true,
        message: "Şifre sıfırlama bağlantısı e-posta adresinize gönderildi"
      }
    })
  } catch (error: any) {
    let userMessage = "Şifre sıfırlama işlemi sırasında bir hata oluştu"
    
    if (error.message.includes("Email not found")) {
      userMessage = "Bu e-posta adresiyle kayıtlı bir hesap bulunamadı."
    }
    
    return {
      success: false,
      error: userMessage,
      data: { email }
    }
  }
}
"use client";

import { useState, useEffect, useMemo } from "react";
import { <PERSON>, Check, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useAuth } from "@/components/auth/auth-provider";
import { createClient } from "@/utils/supabase/client";
import type { Tables } from "@/lib/supabase/types";
import { useRealtimeSubscription } from "@/hooks/use-realtime-subscription";
import {
  acceptMembershipRequestFromNotification,
  rejectMembershipRequestFromNotification,
} from "@/app/actions/membership-actions";

export function NotificationDropdown() {
  const { authUser } = useAuth();
  const [notifications, setNotifications] = useState<Tables<"notifications">[]>(
    []
  );
  const [unreadCount, setUnreadCount] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [processingNotifications, setProcessingNotifications] = useState<
    Set<string>
  >(new Set());

  // Create a stable filter value that won't change on re-renders
  const userIdFilter = useMemo(() => {
    return authUser?.id ? `user_id=eq.${authUser.id}` : undefined;
  }, [authUser?.id]);

  // Fetch notifications on mount
  useEffect(() => {
    if (!authUser) return;

    const fetchNotifications = async () => {
      setIsLoading(true);
      const supabase = createClient();

      const { data, error } = await supabase
        .from("notifications")
        .select(
          "id, title, message, is_read, created_at, type, related_entity_type, related_entity_id, metadata"
        )
        .eq("user_id", authUser.id)
        .order("created_at", { ascending: false })
        .limit(10);

      if (error) throw error;
      if (data) {
        setNotifications(data as Tables<"notifications">[]);
        setUnreadCount(data.filter((n) => !n.is_read).length);
      }

      setIsLoading(false);
    };

    fetchNotifications();
  }, [authUser]);

  // Subscribe to new notifications
  useRealtimeSubscription<Tables<"notifications">>(
    "notifications",
    (payload) => {
      if (
        payload.eventType === "INSERT" &&
        payload.new.user_id === authUser?.id
      ) {
        setNotifications((prev) => [payload.new, ...prev].slice(0, 10));
        setUnreadCount((prev) => prev + 1);
      }
    },
    userIdFilter
      ? {
          event: "INSERT",
          filter: userIdFilter,
        }
      : undefined
  );

  const markAsRead = async (notificationId: string) => {
    if (!authUser) return;

    const supabase = createClient();

    await supabase
      .from("notifications")
      .update({ is_read: true })
      .eq("id", notificationId);

    setNotifications((prev) =>
      prev.map((n) => (n.id === notificationId ? { ...n, is_read: true } : n))
    );

    setUnreadCount((prev) => Math.max(0, prev - 1));
  };

  const markAllAsRead = async () => {
    if (!authUser) return;

    const supabase = createClient();

    await supabase
      .from("notifications")
      .update({ is_read: true })
      .eq("user_id", authUser.id)
      .eq("is_read", false);

    setNotifications((prev) => prev.map((n) => ({ ...n, is_read: true })));

    setUnreadCount(0);
  };

  const handleAcceptMembershipRequest = async (
    notificationId: string,
    event: React.MouseEvent
  ) => {
    event.stopPropagation();

    setProcessingNotifications((prev) => new Set(prev).add(notificationId));

    try {
      const result = await acceptMembershipRequestFromNotification(
        notificationId
      );

      if (result.success) {
        // Remove notification from list or mark as read
        setNotifications((prev) => prev.filter((n) => n.id !== notificationId));
        setUnreadCount((prev) => Math.max(0, prev - 1));
      } else {
        console.error("Failed to accept membership request:", result.error);
      }
    } catch (error) {
      console.error("Error accepting membership request:", error);
    } finally {
      setProcessingNotifications((prev) => {
        const newSet = new Set(prev);
        newSet.delete(notificationId);
        return newSet;
      });
    }
  };

  const handleRejectMembershipRequest = async (
    notificationId: string,
    event: React.MouseEvent
  ) => {
    event.stopPropagation();

    setProcessingNotifications((prev) => new Set(prev).add(notificationId));

    try {
      const result = await rejectMembershipRequestFromNotification(
        notificationId
      );

      if (result.success) {
        // Remove notification from list or mark as read
        setNotifications((prev) => prev.filter((n) => n.id !== notificationId));
        setUnreadCount((prev) => Math.max(0, prev - 1));
      } else {
        console.error("Failed to reject membership request:", result.error);
      }
    } catch (error) {
      console.error("Error rejecting membership request:", error);
    } finally {
      setProcessingNotifications((prev) => {
        const newSet = new Set(prev);
        newSet.delete(notificationId);
        return newSet;
      });
    }
  };

  if (!authUser) return null;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="icon" className="relative">
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <span className="absolute -top-1 -right-1 flex h-5 w-5 items-center justify-center rounded-full bg-primary text-xs text-primary-foreground">
              {unreadCount}
            </span>
          )}
          <span className="sr-only">Bildirimler</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-80">
        <DropdownMenuLabel className="flex items-center justify-between">
          <span>Bildirimler</span>
          {unreadCount > 0 && (
            <Button variant="ghost" size="sm" onClick={markAllAsRead}>
              Tümünü Okundu İşaretle
            </Button>
          )}
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        {isLoading ? (
          <div className="p-4 text-center text-sm text-muted-foreground">
            Yükleniyor...
          </div>
        ) : notifications.length === 0 ? (
          <div className="p-4 text-center text-sm text-muted-foreground">
            Bildirim bulunmuyor
          </div>
        ) : (
          notifications.map((notification) => {
            const isMembershipRequest =
              notification.type === "membership_request";
            const isProcessing = processingNotifications.has(notification.id);

            return (
              <DropdownMenuItem
                key={notification.id}
                className="flex flex-col items-start p-3 cursor-pointer"
                onClick={() =>
                  !isMembershipRequest && markAsRead(notification.id)
                }
              >
                <div className="flex items-center w-full">
                  <div className="flex-1">
                    <div className="font-medium">{notification.title}</div>
                    <div className="text-sm text-muted-foreground">
                      {notification.message}
                    </div>
                  </div>
                  {!notification.is_read && (
                    <div className="h-2 w-2 rounded-full bg-primary"></div>
                  )}
                </div>

                {isMembershipRequest && !notification.is_read && (
                  <div className="flex gap-2 mt-2 w-full">
                    <Button
                      size="sm"
                      variant="default"
                      onClick={(e) =>
                        handleAcceptMembershipRequest(notification.id, e)
                      }
                      disabled={isProcessing}
                      className="flex-1"
                    >
                      <Check className="h-3 w-3 mr-1" />
                      Kabul Et
                    </Button>
                    <Button
                      size="sm"
                      variant="destructive"
                      onClick={(e) =>
                        handleRejectMembershipRequest(notification.id, e)
                      }
                      disabled={isProcessing}
                      className="flex-1"
                    >
                      <X className="h-3 w-3 mr-1" />
                      Reddet
                    </Button>
                  </div>
                )}

                <div className="text-xs text-muted-foreground mt-1">
                  {new Date(notification.created_at).toLocaleString("tr-TR")}
                </div>
              </DropdownMenuItem>
            );
          })
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

"use server"

import { revalidatePath } from "next/cache"
import { redirect } from "next/navigation"
import { createClient } from "@/utils/supabase/server"
import { getSupabaseAdmin } from "@/utils/supabase/admin"
import { z } from "zod"

const loginSchema = z.object({
  email: z.string().email("Geçerli bir e-posta adresi girin"),
  password: z.string().min(1, "Şifre gerekli"),
})

const registerSchema = z.object({
  name: z.string().min(2, "Ad en az 2 karakter olmalı"),
  surname: z.string().min(2, "Soyad en az 2 karakter olmalı"),
  email: z.string().email("Geçerli bir e-posta adresi girin"),
  password: z.string().min(6, "Şifre en az 6 karakter olmalı"),
})

export async function loginAction(prevState: any, formData: FormData) {
  const supabase = await createClient()

  try {
    const validatedFields = loginSchema.parse({
      email: formData.get("email"),
      password: formData.get("password"),
    })

    const { error } = await supabase.auth.signInWithPassword({
      email: validatedFields.email,
      password: validatedFields.password,
    })

    if (error) {
      return {
        error: "Geçersiz e-posta veya şifre",
        success: false,
      }
    }

    revalidatePath("/", "layout")
    redirect("/dashboard")
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        error: error.errors[0].message,
        success: false,
      }
    }

    return {
      error: "Bir hata oluştu",
      success: false,
    }
  }
}

export async function registerAction(prevState: any, formData: FormData) {
  const supabase = await createClient()

  try {
    const validatedFields = registerSchema.parse({
      name: formData.get("name"),
      surname: formData.get("surname"),
      email: formData.get("email"),
      password: formData.get("password"),
    })

    // Admin client oluştur
    const adminClient = getSupabaseAdmin()

    // Aynı email ile kullanıcı var mı kontrol et
    const { data: existingUser } = await adminClient
      .from("users")
      .select("id")
      .eq("email", validatedFields.email)
      .maybeSingle()

    if (existingUser) {
      return {
        error: "Bu e-posta adresi zaten kullanılıyor.",
        success: false,
      }
    }

    // Yeni kullanıcı oluştur
    const { data, error: signUpError } = await supabase.auth.signUp({
      email: validatedFields.email,
      password: validatedFields.password,
    })

    const userId = data.user?.id

    if (signUpError || !userId) {
      return {
        error: `Kayıt olurken hata: ${signUpError?.message ?? "Bilinmeyen hata"}`,
        success: false,
      }
    }

 

    // Profil kaydı
    const { error: profileError } = await supabase.from("users").insert({
      id: userId,
      email: validatedFields.email,
      name: validatedFields.name,
      surname: validatedFields.surname,
      created_at: new Date().toISOString(),
    })

    // Profil kaydı başarısızsa auth.users kaydını da sil
    if (profileError) {
      await adminClient.auth.admin.deleteUser(userId)
      return {
        error: `Profil oluşturulurken hata: ${profileError.message}`,
        success: false,
      }
    }

    // E-posta doğrulaması kapalıysa otomatik giriş yap
    if (data?.user && !data.user.email_confirmed_at) {
      // E-posta doğrulaması gerekiyor
      return {
        error: "Lütfen e-posta adresinizi doğrulayın. Gelen kutunuzu kontrol edin.",
        success: false,
      }
    }

    revalidatePath("/", "layout")
    redirect("/dashboard")
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        error: error.errors[0].message,
        success: false,
      }
    }

    if (error instanceof Error) {
      return {
        error: error.message,
        success: false,
      }
    }

    return {
      error: "Bir hata oluştu",
      success: false,
    }
  }
}

export async function logoutAction() {
  const supabase = await createClient()

  await supabase.auth.signOut()

  revalidatePath("/", "layout")
  redirect("/login")
}

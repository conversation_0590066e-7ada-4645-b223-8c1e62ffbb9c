import { redirect } from "next/navigation";
import { createClient } from "@/utils/supabase/server";
import { LoginForm } from "@/components/auth/login-form";
import { StructuredData } from "@/components/seo/structured-data";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Giriş Yap | Sportiva - Spor Salonu Yönetim Platformu",
  description:
    "Sportiva hesabınıza giriş yapın. Spor salonu üyeliğinizi yönetin, antrenman programlarınızı takip edin ve daha fazlası.",
  keywords: [
    "sportiva giriş",
    "spor salonu giriş",
    "üye girişi",
    "fitness giriş",
    "antrenman takip",
    "spor salonu üyelik",
  ],
  openGraph: {
    title: "Giriş Yap | Sportiva",
    description:
      "Sportiva hesabınıza giriş yapın ve spor salonu deneyiminizi dijitalleştirin.",
    type: "website",
    locale: "tr_TR",
    siteName: "Sportiva",
  },
  twitter: {
    card: "summary",
    title: "Giriş Yap | Sportiva",
    description:
      "Sportiva hesabınıza giriş yapın ve spor salonu deneyiminizi dijitalleştirin.",
  },
  robots: {
    index: true,
    follow: true,
  },
  alternates: {
    canonical: "/login",
  },
};

export default async function LoginPage() {
  const supabase = await createClient();

  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (user) {
    redirect("/dashboard");
  }

  return (
    <>
      <StructuredData type="LoginAction" />
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 via-white to-gray-100 dark:from-gray-900 dark:via-gray-800 dark:to-black">
        <div className="max-w-md w-full space-y-8 p-8">
          <LoginForm />
        </div>
      </div>
    </>
  );
}

"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/components/auth/auth-provider";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Building2,
  MapPin,
  Phone,
  Mail,
  Globe,
  FileText,
  CreditCard,
  Upload,
  Save,
  Edit,
  CheckCircle,
  Loader2,
  AlertTriangle,
} from "lucide-react";
import { cn } from "@/lib/utils";

interface CompanyProfile {
  companyName: string;
  taxNumber: string;
  taxOffice: string;
  mersisNumber: string;
  address: string;
  city: string;
  district: string;
  postalCode: string;
  phone: string;
  email: string;
  website: string;
  description: string;
  logo: string;
  bankName: string;
  bankAccountNumber: string;
  iban: string;
  accountHolder: string;
  contactPerson: string;
  contactPhone: string;
  contactEmail: string;
}

export default function CompanyPage() {
  const { authUser } = useAuth();
  const [companyProfile, setCompanyProfile] = useState<CompanyProfile>({
    companyName: "",
    taxNumber: "",
    taxOffice: "",
    mersisNumber: "",
    address: "",
    city: "",
    district: "",
    postalCode: "",
    phone: "",
    email: "",
    website: "",
    description: "",
    logo: "",
    bankName: "",
    bankAccountNumber: "",
    iban: "",
    accountHolder: "",
    contactPerson: "",
    contactPhone: "",
    contactEmail: "",
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  useEffect(() => {
    const loadCompanyProfile = async () => {
      if (!authUser) {
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        // Mock data - gerçek implementasyonda API'den gelecek
        const mockProfile: CompanyProfile = {
          companyName: "Sportiva Fitness Merkezi Ltd. Şti.",
          taxNumber: "**********",
          taxOffice: "Kadıköy",
          mersisNumber: "****************",
          address: "Atatürk Caddesi No: 123 Kat: 2",
          city: "İstanbul",
          district: "Kadıköy",
          postalCode: "34710",
          phone: "0216 123 4567",
          email: "<EMAIL>",
          website: "https://www.sportiva.com.tr",
          description: "Modern spor salonları işletmeciliği alanında faaliyet gösteren şirketimiz, kaliteli hizmet anlayışı ile müşteri memnuniyetini ön planda tutmaktadır.",
          logo: "",
          bankName: "Türkiye İş Bankası",
          bankAccountNumber: "**********",
          iban: "TR12 0006 4000 0011 2345 6789 01",
          accountHolder: "Sportiva Fitness Merkezi Ltd. Şti.",
          contactPerson: "Ahmet Yılmaz",
          contactPhone: "0532 123 4567",
          contactEmail: "<EMAIL>",
        };

        // Simulated API delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        setCompanyProfile(mockProfile);
      } catch (err) {
        console.error("Error loading company profile:", err);
        setError("Şirket profili yüklenirken bir hata oluştu.");
      } finally {
        setIsLoading(false);
      }
    };

    loadCompanyProfile();
  }, [authUser]);

  const handleInputChange = (field: keyof CompanyProfile, value: string) => {
    setCompanyProfile(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSave = async () => {
    try {
      setIsSaving(true);
      setError(null);
      setSuccessMessage(null);

      // Mock API call - gerçek implementasyonda API'ye gönderilecek
      await new Promise(resolve => setTimeout(resolve, 1500));

      setSuccessMessage("Şirket profili başarıyla güncellendi.");
      setIsEditing(false);

      // Success message'ı 3 saniye sonra temizle
      setTimeout(() => {
        setSuccessMessage(null);
      }, 3000);
    } catch (err) {
      console.error("Error saving company profile:", err);
      setError("Şirket profili kaydedilirken bir hata oluştu.");
    } finally {
      setIsSaving(false);
    }
  };

  const cities = [
    "İstanbul", "Ankara", "İzmir", "Bursa", "Antalya", "Adana", "Konya", "Gaziantep",
    "Şanlıurfa", "Kocaeli", "Mersin", "Diyarbakır", "Hatay", "Manisa", "Kayseri"
  ];

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="flex items-center gap-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Şirket profili yükleniyor...</span>
        </div>
      </div>
    );
  }

  if (!authUser) {
    return (
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          Şirket profilini görüntülemek için giriş yapmanız gerekiyor.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <Building2 className="h-8 w-8" />
            Şirket Profili
          </h1>
          <p className="text-muted-foreground">
            Şirket bilgilerinizi ve iletişim detaylarınızı yönetin
          </p>
        </div>
        <div className="flex gap-2">
          {!isEditing ? (
            <Button onClick={() => setIsEditing(true)} className="gap-2">
              <Edit className="h-4 w-4" />
              Düzenle
            </Button>
          ) : (
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => setIsEditing(false)}
                disabled={isSaving}
              >
                İptal
              </Button>
              <Button onClick={handleSave} disabled={isSaving} className="gap-2">
                {isSaving ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Save className="h-4 w-4" />
                )}
                Kaydet
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Success Message */}
      {successMessage && (
        <Alert className="border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800 dark:text-green-200">
            {successMessage}
          </AlertDescription>
        </Alert>
      )}

      {/* Error Message */}
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Company Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Şirket Bilgileri
          </CardTitle>
          <CardDescription>
            Temel şirket bilgileri ve yasal detaylar
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="companyName">Şirket Adı</Label>
              <Input
                id="companyName"
                value={companyProfile.companyName}
                onChange={(e) => handleInputChange("companyName", e.target.value)}
                disabled={!isEditing}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="taxNumber">Vergi Numarası</Label>
              <Input
                id="taxNumber"
                value={companyProfile.taxNumber}
                onChange={(e) => handleInputChange("taxNumber", e.target.value)}
                disabled={!isEditing}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="taxOffice">Vergi Dairesi</Label>
              <Input
                id="taxOffice"
                value={companyProfile.taxOffice}
                onChange={(e) => handleInputChange("taxOffice", e.target.value)}
                disabled={!isEditing}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="mersisNumber">MERSİS Numarası</Label>
              <Input
                id="mersisNumber"
                value={companyProfile.mersisNumber}
                onChange={(e) => handleInputChange("mersisNumber", e.target.value)}
                disabled={!isEditing}
              />
            </div>
          </div>
          <div className="space-y-2">
            <Label htmlFor="description">Şirket Açıklaması</Label>
            <Textarea
              id="description"
              value={companyProfile.description}
              onChange={(e) => handleInputChange("description", e.target.value)}
              disabled={!isEditing}
              rows={3}
            />
          </div>
        </CardContent>
      </Card>

      {/* Address Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MapPin className="h-5 w-5" />
            Adres Bilgileri
          </CardTitle>
          <CardDescription>
            Şirket adresi ve konum bilgileri
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="address">Adres</Label>
            <Textarea
              id="address"
              value={companyProfile.address}
              onChange={(e) => handleInputChange("address", e.target.value)}
              disabled={!isEditing}
              rows={2}
            />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="city">İl</Label>
              <Select
                value={companyProfile.city}
                onValueChange={(value) => handleInputChange("city", value)}
                disabled={!isEditing}
              >
                <SelectTrigger>
                  <SelectValue placeholder="İl seçin" />
                </SelectTrigger>
                <SelectContent>
                  {cities.map((city) => (
                    <SelectItem key={city} value={city}>
                      {city}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="district">İlçe</Label>
              <Input
                id="district"
                value={companyProfile.district}
                onChange={(e) => handleInputChange("district", e.target.value)}
                disabled={!isEditing}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="postalCode">Posta Kodu</Label>
              <Input
                id="postalCode"
                value={companyProfile.postalCode}
                onChange={(e) => handleInputChange("postalCode", e.target.value)}
                disabled={!isEditing}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Contact Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Phone className="h-5 w-5" />
            İletişim Bilgileri
          </CardTitle>
          <CardDescription>
            Telefon, e-posta ve web sitesi bilgileri
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="phone">Telefon</Label>
              <Input
                id="phone"
                value={companyProfile.phone}
                onChange={(e) => handleInputChange("phone", e.target.value)}
                disabled={!isEditing}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="email">E-posta</Label>
              <Input
                id="email"
                type="email"
                value={companyProfile.email}
                onChange={(e) => handleInputChange("email", e.target.value)}
                disabled={!isEditing}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="website">Web Sitesi</Label>
              <Input
                id="website"
                value={companyProfile.website}
                onChange={(e) => handleInputChange("website", e.target.value)}
                disabled={!isEditing}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Bank Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Banka Bilgileri
          </CardTitle>
          <CardDescription>
            Ödeme ve fatura bilgileri için banka hesap detayları
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="bankName">Banka Adı</Label>
              <Input
                id="bankName"
                value={companyProfile.bankName}
                onChange={(e) => handleInputChange("bankName", e.target.value)}
                disabled={!isEditing}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="accountHolder">Hesap Sahibi</Label>
              <Input
                id="accountHolder"
                value={companyProfile.accountHolder}
                onChange={(e) => handleInputChange("accountHolder", e.target.value)}
                disabled={!isEditing}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="bankAccountNumber">Hesap Numarası</Label>
              <Input
                id="bankAccountNumber"
                value={companyProfile.bankAccountNumber}
                onChange={(e) => handleInputChange("bankAccountNumber", e.target.value)}
                disabled={!isEditing}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="iban">IBAN</Label>
              <Input
                id="iban"
                value={companyProfile.iban}
                onChange={(e) => handleInputChange("iban", e.target.value)}
                disabled={!isEditing}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Contact Person */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            İletişim Sorumlusu
          </CardTitle>
          <CardDescription>
            Birincil iletişim sorumlusu bilgileri
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="contactPerson">Ad Soyad</Label>
              <Input
                id="contactPerson"
                value={companyProfile.contactPerson}
                onChange={(e) => handleInputChange("contactPerson", e.target.value)}
                disabled={!isEditing}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="contactPhone">Telefon</Label>
              <Input
                id="contactPhone"
                value={companyProfile.contactPhone}
                onChange={(e) => handleInputChange("contactPhone", e.target.value)}
                disabled={!isEditing}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="contactEmail">E-posta</Label>
              <Input
                id="contactEmail"
                type="email"
                value={companyProfile.contactEmail}
                onChange={(e) => handleInputChange("contactEmail", e.target.value)}
                disabled={!isEditing}
              />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

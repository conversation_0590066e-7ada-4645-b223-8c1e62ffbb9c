import { DashboardSidebar } from "@/components/dashboard/dashboard-sidebar";
import { redirect } from "next/navigation";
import { createClient } from "@/utils/supabase/server";
import { Users } from "@/lib/supabase/types";

export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const supabase = await createClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user) {
    redirect("/login");
  }
  const { data: profile } = await supabase
    .from("users")
    .select("*")
    .eq("id", user.id)
    .single();
  const userData = profile as Users;

  return (
    <>
      <DashboardSidebar {...userData} />
      <section className="w-full h-full transition-all duration-300 pl-24 pr-10 mt-20 mb-10">
        {children}
      </section>
    </>
  );
}

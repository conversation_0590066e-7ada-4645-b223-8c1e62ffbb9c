---
description: 
globs: 
alwaysApply: false
---
# Form Bileşen Kalıpları

Bu rehber, projede form bileşenlerinin nasıl yapılandırılması ve kullanılması gerektiğini açıklar.

## Form Bileşeni Yapısı

Form bileşenleri birkaç temel bölümden oluşur:

1. **State Tanımı**: useActionState ile form durumu yönetimi
2. **UI Bileşenleri**: Form alanları ve gönderim düğmesi
3. **Doğrulama Mantığı**: Hem istemci hem de sunucu tarafında doğrulama
4. **Durum Göstergesi**: Yükleme, hata ve başarı göstergeleri

## Örnek Yapı

[register-form.tsx](mdc:src/components/register-form.tsx) dosyası örnek form bileşen yapısını göstermektedir:

```tsx
// 1. Use client directive ve gerekli importlar
'use client'
import { useActionState } from "react"
import { someAction } from "@/app/actions/some-actions"

// 2. Doğrulama şeması (isteğe bağlı, genellikle Server Action'da bulunur)
const validationSchema = z.object({...})

// 3. Başlangıç durumu
const initialState = {
  success: false,
  error: undefined,
  message: undefined,
  data: undefined,
}

// 4. Alt bileşenler (isteğe bağlı)
function SubmitButton() {
  const { pending } = useFormStatus()
  return <Button disabled={pending}>...</Button>
}

// 5. Ana form bileşeni
export function SomeForm() {
  // State yönetimi
  const [state, action] = useActionState(someAction, initialState)

  // Yönlendirme veya başarı sonrası işlemler
  useEffect(() => {
    if (state?.success) {
      // Başarı işlemleri
    }
  }, [state, router])

  return (
    <form action={action} className="space-y-4">
      {/* Hata mesajı */}
      {state?.error && <Alert variant="destructive">{state.error}</Alert>}
      
      {/* Form alanları */}
      <div className="space-y-2">
        <Label htmlFor="fieldName">Alan Adı</Label>
        <Input 
          id="fieldName"
          name="fieldName"
          defaultValue={state.data?.fieldName || ""}
        />
      </div>
      
      {/* Gönder düğmesi */}
      <SubmitButton />
    </form>
  )
}
```

## Best Practices

1. **'use client' direktifi**: Form bileşenleri genellikle istemci tarafı etkileşime sahip olduğundan, 'use client' direktifi ile başlamalıdır.

2. **Form Durumu**:
   - `useActionState` ile form durumunu yönetin
   - `initialState` nesnesinde olası tüm durumları belirtin

3. **Erişilebilirlik**:
   - Her input için bir `<Label>` kullanın
   - Hata mesajları için `aria-describedby` kullanın
   - Yükleme durumunda düğmeleri `disabled` yapın

4. **UX İyileştirmeleri**:
   - Yükleme durumunda göstergeler kullanın
   - Hata ve başarı mesajları için uygun bildirimler gösterin
   - Form gönderiminden sonra uygun yönlendirmeler yapın

5. **Veri Kalıcılığı**:
   - Doğrulama hatalarında form değerlerini korumak için `defaultValue` kullanın
   - Başarılı gönderimlerde form sıfırlamasına izin verin

## Özel Bileşenler

Formlar için özel bileşenleri modüler ve yeniden kullanılabilir tutun:

- `useFormStatus()` kullanan gönderim düğmeleri
- Parola güç göstergeleri
- Özelleştirilmiş doğrulama hata mesajları

## Örnek: PasswordStrengthIndicator

```tsx
function PasswordStrengthIndicator({ password = "" }) {
  // Şifre güç kriteri kontrolü
  const hasMinLength = password.length >= 6
  const hasLowercase = /[a-z]/.test(password)
  const hasUppercase = /[A-Z]/.test(password)
  const hasNumber = /[0-9]/.test(password)

  // Şifre gücü hesaplama
  const strengthScore = [hasMinLength, hasLowercase, hasUppercase, hasNumber]
    .filter(Boolean).length

  return (
    <div className="mt-2">
      {/* Şifre güç göstergesi ve kriterleri */}
    </div>
  )
}
```

## Form İşleme Akışı

1. Kullanıcı formu doldurur ve gönderir
2. `useFormStatus` yükleme durumunu gösterir 
3. Form verileri Server Action'a gönderilir
4. Server Action veriyi doğrular ve işler
5. Dönüş değeri `useActionState` aracılığıyla form durumunu günceller
6. Başarı veya hata durumuna göre UI güncellenir


"use client";

import { useState, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  AlertCircle,
  CalendarDays,
  CreditCard,
  DollarSign,
  TrendingUp,
  Users,
} from "lucide-react";
import { MemberRequestsTable } from "@/components/dashboard/member-requests-table";
import { RecentTransactionsTable } from "@/components/dashboard/recent-transactions-table";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useAuth } from "@/components/auth/auth-provider";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { Skeleton } from "@/components/ui/skeleton";
import { getMyGyms } from "@/app/actions/gym-actions";
import { createClient } from "@/utils/supabase/client";

export function ManagerDashboard() {
  const { authUser } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState({
    totalMembers: 0,
    activeMembers: 0,
    totalRevenue: 0,
    revenueChange: 0,
    activePackages: 0,
    expiringPackages: 0,
    gyms: [] as any[],
    newMembersLastMonth: 0,
  });
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDashboardData = async () => {
      if (!authUser) return;

      try {
        setIsLoading(true);
        setError(null);
        const supabase = createClient();

        // Server Action kullanarak yöneticinin salonlarını al
        const myGymsResponse = await getMyGyms();
        
        if (!myGymsResponse.success || myGymsResponse.error) {
          throw new Error(myGymsResponse.error || "Salon bilgileri alınamadı");
        }
        
        const gyms = myGymsResponse.data || [];

        if (!gyms || gyms.length === 0) {
          setError("Henüz bir salon oluşturmadınız");
          setIsLoading(false);
          return;
        }

        const gymIds = gyms.map((gym) => gym.id);
        
        // Tarih hesaplamaları
        const now = new Date().toISOString();
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        const thirtyDaysLater = new Date();
        thirtyDaysLater.setDate(thirtyDaysLater.getDate() + 30);
        const lastMonth = new Date();
        lastMonth.setMonth(lastMonth.getMonth() - 1);
        const twoMonthsAgo = new Date();
        twoMonthsAgo.setMonth(twoMonthsAgo.getMonth() - 2);

        // Tüm sorguları paralel olarak çalıştır
        const [
          totalMembersResult,
          activeMembersResult,
          newMembersResult,
          subscriptionsResult,
          lastMonthSubscriptionsResult
        ] = await Promise.all([
          // Toplam üye sayısı
          supabase
            .from("memberships")
            .select("id", { count: "exact", head: true })
            .in("gym_id", gymIds)
            .or("status.eq.active,status.eq.approved_passive"),
          
          // Aktif üye sayısı
          supabase
            .from("memberships")
            .select("id", { count: "exact", head: true })
            .in("gym_id", gymIds)
            .eq("status", "active"),
          
          // Son 30 günde eklenen yeni üyeler
          supabase
            .from("memberships")
            .select("id", { count: "exact", head: true })
            .in("gym_id", gymIds)
            .gt("created_at", thirtyDaysAgo.toISOString()),
          
          // Abonelikler ve gelir
          supabase
            .from("subscriptions")
            .select(`
              id, purchase_price, start_date, end_date,
              membership:memberships(gym_id)
            `)
            .in("membership.gym_id", gymIds),
          
          // Son aydaki gelir hesaplama için önceki ay verileri
          supabase
            .from("subscriptions")
            .select(`
              id, purchase_price, start_date,
              membership:memberships(gym_id)
            `)
            .in("membership.gym_id", gymIds)
            .gt("start_date", twoMonthsAgo.toISOString())
            .lt("start_date", lastMonth.toISOString())
        ]);

        // Hata kontrolü
        if (totalMembersResult.error) throw new Error("Üye sayısı alınamadı: " + totalMembersResult.error.message);
        if (activeMembersResult.error) throw new Error("Aktif üye sayısı alınamadı: " + activeMembersResult.error.message);
        if (newMembersResult.error) throw new Error("Yeni üye sayısı alınamadı: " + newMembersResult.error.message);
        if (subscriptionsResult.error) throw new Error("Abonelik bilgileri alınamadı: " + subscriptionsResult.error.message);
        if (lastMonthSubscriptionsResult.error) throw new Error("Önceki ay verileri alınamadı: " + lastMonthSubscriptionsResult.error.message);
        
        const subscriptions = subscriptionsResult.data || [];
        const lastMonthSubscriptions = lastMonthSubscriptionsResult.data || [];

        // Toplam gelir hesaplama
        const totalRevenue = subscriptions.reduce(
          (sum, sub) => sum + (sub.purchase_price || 0),
          0
        ) || 0;

        // Aktif paketler
        const activePackages = subscriptions.filter(
          (sub) => (sub.end_date && sub.end_date > now) || !sub.end_date
        ).length || 0;

        // Yakında sona erecek paketler (30 gün içinde)
        const expiringPackages = subscriptions.filter(
          (sub) =>
            sub.end_date &&
            sub.end_date > now &&
            sub.end_date < thirtyDaysLater.toISOString()
        ).length || 0;

        // Önceki ay geliri
        const lastMonthRevenue = lastMonthSubscriptions.reduce(
          (sum, sub) => sum + (sub.purchase_price || 0),
          0
        ) || 0;

        const revenueChange =
          lastMonthRevenue > 0 ? totalRevenue - lastMonthRevenue : totalRevenue;

        setDashboardData({
          totalMembers: totalMembersResult.count || 0,
          activeMembers: activeMembersResult.count || 0,
          totalRevenue,
          revenueChange,
          activePackages,
          expiringPackages,
          gyms: gyms || [],
          newMembersLastMonth: newMembersResult.count || 0,
        });
      } catch (err) {
        console.error("Dashboard veri yükleme hatası:", err);
        setError(
          "Veriler yüklenirken bir hata oluştu. Lütfen sayfayı yenileyin."
        );
      } finally {
        setIsLoading(false);
      }
    };

    fetchDashboardData();
  }, [authUser]);

  if (isLoading) {
    return <ManagerDashboardSkeleton />;
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div>
            <h2 className="text-3xl font-bold tracking-tight">
              Yönetici Paneli
            </h2>
            <p className="text-muted-foreground">
              Salonunuzun genel durumunu ve son aktiviteleri görüntüleyin.
            </p>
          </div>
        </div>

        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Hata</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>

        {error === "Henüz bir salon oluşturmadınız" && (
          <div className="flex justify-center">
            <Button asChild>
              <Link href="/dashboard/manager/gyms/new">Salon Oluştur</Link>
            </Button>
          </div>
        )}
      </div>
    );
  }

  const {
    totalMembers,
    activeMembers,
    totalRevenue,
    revenueChange,
    activePackages,
    expiringPackages,
    newMembersLastMonth,
  } = dashboardData;
  const activePercentage =
    totalMembers > 0 ? Math.round((activeMembers / totalMembers) * 100) : 0;

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Genel Bakış</h2>
          <p className="text-muted-foreground">
            Salonunuzun genel durumunu ve son aktiviteleri görüntüleyin.
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button asChild variant="outline">
            <Link href="/dashboard/manager/gyms">Salonlarım</Link>
          </Button>
          <Button asChild>
            <Link href="/dashboard/manager/packages">Paketleri Yönet</Link>
          </Button>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Toplam Üye</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalMembers}</div>
            <p className="text-xs text-muted-foreground">
              +{newMembersLastMonth} yeni üye (son 30 gün)
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Aktif Üye</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activeMembers}</div>
            <p className="text-xs text-muted-foreground">
              %{activePercentage} aktif üye oranı
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Toplam Gelir</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ₺{totalRevenue.toLocaleString("tr-TR")}
            </div>
            <p className="text-xs text-muted-foreground">
              {revenueChange > 0 ? "+" : ""}₺
              {revenueChange.toLocaleString("tr-TR")} (bir önceki aya göre)
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Aktif Paketler
            </CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activePackages}</div>
            <p className="text-xs text-muted-foreground">
              {expiringPackages} paket yakında sona eriyor
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="requests">
        <TabsList>
          <TabsTrigger value="requests">Üyelik İstekleri</TabsTrigger>
          <TabsTrigger value="transactions">Son İşlemler</TabsTrigger>
        </TabsList>
        <TabsContent value="requests" className="space-y-4">
          <MemberRequestsTable />
        </TabsContent>
        <TabsContent value="transactions" className="space-y-4">
          <RecentTransactionsTable />
        </TabsContent>
      </Tabs>
    </div>
  );
}

function ManagerDashboardSkeleton() {
  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Genel Bakış</h2>
          <p className="text-muted-foreground">Veriler yükleniyor...</p>
        </div>
        <div className="flex items-center gap-2">
          <Skeleton className="h-10 w-32" />
          <Skeleton className="h-10 w-32" />
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {Array(4)
          .fill(0)
          .map((_, i) => (
            <Card key={i}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <Skeleton className="h-5 w-24" />
                <Skeleton className="h-4 w-4" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-16 mb-1" />
                <Skeleton className="h-4 w-32" />
              </CardContent>
            </Card>
          ))}
      </div>

      <div className="space-y-4">
        <Skeleton className="h-10 w-64 mb-4" />
        <Skeleton className="h-[300px] w-full" />
      </div>
    </div>
  );
}

"use client";
import { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Plus,
  Building,
  Loader2,
  MapPin,
  PenSquare,
  ExternalLink,
  ArrowLeft,
  Mail,
} from "lucide-react";
import Link from "next/link";
import { useAuth } from "@/components/auth/auth-provider";
import { useToast } from "@/components/ui/use-toast";
import { Gyms } from "@/lib/supabase/types";
import { getMyGyms } from "@/app/actions/gym-actions";

export default function GymsPage() {
  const [gyms, setGyms] = useState<Gyms[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const { authUser } = useAuth();
  const { toast } = useToast();

  const fetchGyms = async () => {
    if (!authUser?.id) {
      console.log("No authUser.id, skipping fetchGyms");
      return;
    }

    try {
      setIsLoading(true);
      const response = await getMyGyms();

      if (response.success && response.data) {
        setGyms(response.data);
      } else {
        console.error("Failed to fetch gyms:", response.error);
        toast({
          title: "Hata",
          description: response.error || "Salonlar yüklenirken bir hata oluştu",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error fetching gyms:", error);
      toast({
        title: "Hata",
        description: "Salonlar yüklenirken bir hata oluştu",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (authUser?.id) {
      fetchGyms();
    }
  }, [authUser?.id]);

  // Son salon seçme veya dashboarda geri dönme mantığı
  const renderDashboardButton = () => {
    if (gyms.length > 0) {
      const lastGym = gyms[0]; // En son eklenen salon (fetchGyms'de created_at DESC sıralaması var)
      return (
        <Button variant="outline" asChild size="sm" className="flex gap-1.5">
          <Link href={`/dashboard/manager/gym/${lastGym.slug}`}>
            <ArrowLeft className="h-4 w-4" />
            Dashboard'a Dön
          </Link>
        </Button>
      );
    }
    return null;
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-32">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-3">
          <h2 className="text-lg font-medium">Salonlarım</h2>
          {renderDashboardButton()}
        </div>
        <Button asChild size="sm">
          <Link href="/gym-setup">
            <Plus className="h-4 w-4 mr-1.5" />
            Yeni Salon
          </Link>
        </Button>
      </div>

      {gyms.length === 0 ? (
        <Card className="border-dashed">
          <CardContent className="flex flex-col items-center justify-center py-8">
            <Building className="h-10 w-10 text-muted-foreground/80 mb-3" />
            <p className="text-base font-medium mb-1">Henüz salon eklenmemiş</p>
            <p className="text-sm text-muted-foreground mb-4 text-center">
              İlk salonunuzu ekleyin ve yönetmeye başlayın
            </p>
            <Button asChild size="sm">
              <Link href="/gym-setup">
                <Plus className="h-4 w-4 mr-1.5" />
                Yeni Salon
              </Link>
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {gyms.map((gym) => (
            <Card
              key={gym.id}
              className="group hover:shadow-lg transition-all duration-200 cursor-pointer relative overflow-hidden"
              onClick={() => window.location.href = `/dashboard/manager/gym/${gym.slug}`}
            >
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="space-y-1 flex-1 min-w-0">
                    <CardTitle className="text-lg leading-tight truncate">
                      {gym.name}
                    </CardTitle>
                    <div className="flex items-center gap-1 text-xs text-muted-foreground">
                      <span className="px-2 py-1 bg-primary/10 text-primary rounded-full font-medium">
                        {gym.gym_type || "Genel"}
                      </span>
                      <span className="text-muted-foreground/60">•</span>
                      <span className="capitalize">
                        {gym.status === "active" ? "Aktif" : "Pasif"}
                      </span>
                    </div>
                  </div>
                </div>

                {gym.address && (
                  <CardDescription className="flex items-start gap-2 text-sm">
                    <MapPin className="h-4 w-4 shrink-0 mt-0.5 text-muted-foreground/70" />
                    <span className="truncate">
                      {gym.address}
                      {gym.district && `, ${gym.district}`}
                      {gym.city && `, ${gym.city}`}
                    </span>
                  </CardDescription>
                )}
                {gym.email && (
                  <CardDescription className="flex items-start gap-2 text-sm">
                    <Mail className="h-4 w-4 shrink-0 mt-0.5 text-muted-foreground/70" />
                    <span className="truncate">{gym.email}</span>
                  </CardDescription>
                )}
              </CardHeader>

              <CardFooter className="pt-0 space-y-3 relative z-20">
                {/* Ana işlemler */}
                <div className="grid grid-cols-2 gap-2 w-full">
                  <Button
                    variant="default"
                    size="sm"
                    asChild
                    className="hover:bg-primary/90"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <Link href={`/dashboard/manager/gym/${gym.slug}`}>
                      <ExternalLink className="h-4 w-4 mr-2" />
                      Salon Seç
                    </Link>
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    asChild
                    className="hover:border-primary/50 hover:bg-primary/5"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <Link href={`/dashboard/manager/gym/${gym.slug}/settings`}>
                      <PenSquare className="h-4 w-4 mr-2" />
                      Ayarlar
                    </Link>
                  </Button>
                </div>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}

---
description:
globs:
alwaysApply: false
---
# Next.js ve Supabase Korumalı Rotaları Uygulama

Bu kural, Next.js ve Supabase Auth kullanarak korumalı rotaları ve erişim kontrolünü nasıl uygulayacağınızı açıklar.

## Middleware ile Rota Koruması

Next.js Middleware, tüm istekleri kontrol edip yönlendirmeler yaparak korumalı sayfaları uygulamanızı sağlar. Middleware, tüm rota istekleri işlenmeden önce çalışır.

```typescript
// src/middleware.ts
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { createSupabaseMiddlewareClient } from '@/lib/supabase/middleware'

export async function middleware(req: NextRequest) {
  // Supabase middleware client'ını oluştur
  const { supabase, response } = await createSupabaseMiddlewareClient(
    req, 
    NextResponse.next()
  )
  
  // Mevcut oturumu al
  const { data: { user } } = await supabase.auth.getUser()
  
  const { pathname } = req.nextUrl

  // Korumalı rotalar
  const protectedRoutes = ['/dashboard', '/profile', '/settings', '/admin']
  
  // Yetkilendirme sayfaları
  const authRoutes = ['/auth/login', '/auth/signup', '/auth/forgot-password']
  
  // Admin sayfaları
  const adminRoutes = ['/admin']

  // Rol tabanlı kontrol için kullanıcı rolünü al
  const userRole = session?.user?.user_metadata?.role || 'user'
  
  // 1. Korumalı sayfa kontrolü
  if (protectedRoutes.some(route => pathname.startsWith(route)) && !session) {
    // Kullanıcı giriş yapmamış ve korumalı sayfaya erişmeye çalışıyor
    const loginUrl = new URL('/auth/login', req.url)
    // Giriş sonrası geri dönüş için URL'i parametre olarak sakla
    loginUrl.searchParams.set('redirectedFrom', pathname)
    return NextResponse.redirect(loginUrl)
  }

  // 2. Admin sayfası kontrolü
  if (adminRoutes.some(route => pathname.startsWith(route)) && userRole !== 'admin') {
    // Kullanıcı admin değil ama admin sayfasına erişmeye çalışıyor
    return NextResponse.redirect(new URL('/unauthorized', req.url))
  }

  // 3. Giriş yapmış kullanıcıların auth sayfalarına erişim kontrolü
  if (session && authRoutes.some(route => pathname.startsWith(route))) {
    // Kullanıcı zaten giriş yapmış ve auth sayfalarına gitmeye çalışıyor
    return NextResponse.redirect(new URL('/dashboard', req.url))
  }

  // İstekle devam et
  return response
}

// Middleware'in hangi yollar için aktif olacağını belirle
export const config = {
  matcher: [
    // API rotaları, statik dosyalar ve resimler dışındaki her şeyi eşleştir
    '/((?!api|_next/static|_next/image|favicon.ico|.*\\.[^.]+$).*)',
  ],
}
```

## Server Component'lerde Koruma

Server Component'lerde doğrudan kullanıcı oturumunu kontrol ederek bir koruma katmanı daha ekleyebilirsiniz:

```tsx
// src/app/dashboard/page.tsx
import { createSupabaseServerClient } from '@/lib/supabase/server'
import { redirect } from 'next/navigation'

export default async function DashboardPage() {
  const supabase = createSupabaseServerClient()
  const { data: { user } } = await supabase.auth.getUser()

  // Middleware normalde bu kontrolü yapmalı, bu bir ek güvenlik katmanıdır
  if (!session) {
    redirect('/auth/login')
  }

  // Kullanıcı giriş yapmış, içeriği göster
  return (
    <div>
      <h1>Hoşgeldin, {session.user.email}</h1>
      <p>Bu senin kişisel dashboard sayfan.</p>
      {/* Dashboard içeriği */}
    </div>
  )
}
```

## Rol Tabanlı Koruma Bileşeni

Server Component'lerde belirli rol seviyelerine erişimi kısıtlamak için bir bileşen oluşturabilirsiniz:

```tsx
// src/components/RoleProtected.tsx
import { createSupabaseServerClient } from '@/lib/supabase/server'
import { redirect } from 'next/navigation'
import { ReactNode } from 'react'

interface RoleProtectedProps {
  children: ReactNode
  requiredRole: 'admin' | 'moderator' | 'user'
}

export default async function RoleProtected({
  children,
  requiredRole
}: RoleProtectedProps) {
  const supabase = createSupabaseServerClient()
  const { data: { user } } = await supabase.auth.getUser()

  // Kullanıcı giriş yapmamış
  if (!session) {
    redirect('/auth/login')
  }

  // Kullanıcı rolünü kontrol et (JWT veya user_metadata'dan)
  const userRole = session.user.user_metadata?.role || 'user'
  
  // Roldeki hiyerarşiyi kontrol et
  const roles = {
    'admin': ['admin'],
    'moderator': ['admin', 'moderator'],
    'user': ['admin', 'moderator', 'user']
  }
  
  const hasRequiredRole = roles[requiredRole].includes(userRole)

  // Kullanıcının rolü yeterli değil
  if (!hasRequiredRole) {
    redirect('/unauthorized')
  }

  // Kullanıcı gerekli role sahip, içeriği göster
  return <>{children}</>
}
```

Kullanımı:

```tsx
// src/app/admin/page.tsx
import RoleProtected from '@/components/RoleProtected'

export default function AdminPage() {
  return (
    <RoleProtected requiredRole='admin'>
      <div>
        <h1>Admin Paneli</h1>
        <p>Bu sayfayı sadece admin rolüne sahip kullanıcılar görebilir.</p>
        {/* Admin paneli içeriği */}
      </div>
    </RoleProtected>
  )
}
```

## Yetkisiz Erişim Sayfası

Yetkisiz erişimler için özel bir sayfa oluşturun:

```tsx
// src/app/unauthorized/page.tsx
import Link from 'next/link'

export default function UnauthorizedPage() {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen">
      <h1 className="text-3xl font-bold">Erişim Reddedildi</h1>
      <p className="mt-4 text-lg">Bu sayfaya erişmek için yeterli yetkiniz bulunmuyor.</p>
      <div className="mt-6 flex gap-4">
        <Link href="/" className="px-4 py-2 bg-gray-200 rounded hover:bg-gray-300">
          Ana Sayfaya Dön
        </Link>
        <Link href="/dashboard" className="px-4 py-2 bg-gray-200 rounded hover:bg-gray-300">
          Dashboard'a Git
        </Link>
      </div>
    </div>
  )
}
```

## Client Component'lerde Oturum Kontrolü

Client Component'lerde oturum durumunu izlemek ve otomatik olarak koruma uygulamak için bir hook oluşturabilirsiniz:

```tsx
// src/hooks/useAuthProtection.ts
'use client'

import { createSupabaseBrowserClient } from '@/lib/supabase/client'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'

type UseAuthProtectionOptions = {
  redirectTo?: string
  requiredRole?: 'admin' | 'moderator' | 'user'
}

export function useAuthProtection(options: UseAuthProtectionOptions = {}) {
  const { redirectTo = '/auth/login', requiredRole } = options
  const router = useRouter()
  const supabase = createSupabaseBrowserClient()
  const [isLoading, setIsLoading] = useState(true)
  const [isAuthorized, setIsAuthorized] = useState(false)
  const [user, setUser] = useState<any>(null)

  useEffect(() => {
    const checkAuth = async () => {
      setIsLoading(true)
      
      // Oturumu kontrol et
      const { data: { user } } = await supabase.auth.getUser()
      
      if (!session) {
        // Kullanıcı giriş yapmamış, login sayfasına yönlendir
        router.push(redirectTo)
        return
      }

      // Rol kontrolü isteniyorsa yap
      if (requiredRole) {
        const userRole = session.user.user_metadata?.role || 'user'
        
        // Rol hiyerarşisi
        const roles = {
          'admin': ['admin'],
          'moderator': ['admin', 'moderator'],
          'user': ['admin', 'moderator', 'user']
        }
        
        const hasRequiredRole = roles[requiredRole].includes(userRole)
        
        if (!hasRequiredRole) {
          // Kullanıcının rolü yeterli değil
          router.push('/unauthorized')
          return
        }
      }

      // Kullanıcı yetkili
      setUser(session.user)
      setIsAuthorized(true)
      setIsLoading(false)
    }

    // İlk kontrol
    checkAuth()
    
    // Auth state değişikliklerini dinle
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event) => {
      if (event === 'SIGNED_OUT') {
        setIsAuthorized(false)
        router.push(redirectTo)
      } else if (event === 'SIGNED_IN') {
        checkAuth()
      }
    })

    return () => {
      subscription.unsubscribe()
    }
  }, [router, supabase, redirectTo, requiredRole])

  return { isLoading, isAuthorized, user }
}
```

Client Component'te kullanımı:

```tsx
// src/components/AdminDashboard.tsx
'use client'

import { useAuthProtection } from '@/hooks/useAuthProtection'

export default function AdminDashboard() {
  const { isLoading, isAuthorized, user } = useAuthProtection({
    requiredRole: 'admin',
  })

  if (isLoading) {
    return <p>Yükleniyor...</p>
  }

  if (!isAuthorized) {
    return <p>Bu içeriği görüntülemek için yetkiniz yok.</p>
  }

  return (
    <div>
      <h1>Admin Dashboard</h1>
      <p>Hoşgeldin, {user.email} (Admin)</p>
      {/* Admin UI içeriği */}
    </div>
  )
}
```

## Yönlendirme ile Giriş Sonrası Gelinen Sayfaya Dönme

Middleware ile yakaladığımız `redirectedFrom` parametresini giriş sayfasında kullanarak, kullanıcıyı giriş sonrası doğru sayfaya yönlendirebilirsiniz:

```tsx
// src/app/auth/login/page.tsx
'use client'

import { createSupabaseBrowserClient } from '@/lib/supabase/client'
import { useRouter, useSearchParams } from 'next/navigation'
import { useState } from 'react'

export default function LoginPage() {
  const supabase = createSupabaseBrowserClient()
  const router = useRouter()
  const searchParams = useSearchParams()
  const redirectedFrom = searchParams.get('redirectedFrom')
  
  // ... giriş formu state ve form yönetimi ...

  const handleLogin = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    // ... giriş işlemi ...

    const { error: signInError } = await supabase.auth.signInWithPassword({
      email,
      password,
    })
    
    if (signInError) {
      // ... hata yönetimi ...
    } else {
      // Eğer redirectedFrom varsa oraya, yoksa dashboard'a yönlendir
      router.push(redirectedFrom || '/dashboard')
    }
  }

  // ... form JSX ...
}
```

Bu örnekler, Next.js ve Supabase kullanarak kapsamlı bir rota koruma ve yetkilendirme sistemi oluşturmanıza yardımcı olur. Middleware, Server Component'ler ve Client Component'lerde koruma mekanizmaları ile uygulamanızın erişimini etkin bir şekilde kontrol edebilirsiniz.

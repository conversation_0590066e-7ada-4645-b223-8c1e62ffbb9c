import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Check } from "lucide-react"

export function Pricing() {
  return (
    <section id="pricing" className="w-full flex flex-col items-center justify-center py-12 md:py-24 lg:py-32 bg-gray-50 dark:bg-gray-900">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <div className="space-y-2">
            <div className="inline-block rounded-lg bg-primary px-3 py-1 text-sm text-primary-foreground">
              Fiyatlandırma
            </div>
            <h2 className="text-3xl font-bold tracking-tighter md:text-4xl/tight">Salon Yöneticileri İçin Planlar</h2>
            <p className="max-w-[900px] text-gray-500 md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed dark:text-gray-400">
              İşletmenizin ihtiyaçlarına uygun planı seçin
            </p>
          </div>
        </div>
        <div className="mx-auto grid max-w-5xl grid-cols-1 gap-6 py-12 lg:grid-cols-2">
          <Card className="flex flex-col">
            <CardHeader>
              <CardTitle className="text-2xl">Aylık Plan</CardTitle>
              <CardDescription>Salon yöneticileri için aylık abonelik</CardDescription>
              <div className="mt-4 flex items-baseline text-gray-900 dark:text-gray-50">
                <span className="text-4xl font-extrabold tracking-tight">₺299</span>
                <span className="ml-1 text-xl font-semibold">/ay</span>
              </div>
            </CardHeader>
            <CardContent className="flex-1">
              <ul className="space-y-3">
                <li className="flex items-center">
                  <Check className="mr-2 h-5 w-5 text-primary" />
                  <span>Tek salon yönetimi</span>
                </li>
                <li className="flex items-center">
                  <Check className="mr-2 h-5 w-5 text-primary" />
                  <span>Sınırsız üye yönetimi</span>
                </li>
                <li className="flex items-center">
                  <Check className="mr-2 h-5 w-5 text-primary" />
                  <span>Paket yönetimi</span>
                </li>
                <li className="flex items-center">
                  <Check className="mr-2 h-5 w-5 text-primary" />
                  <span>Temel raporlar</span>
                </li>
                <li className="flex items-center">
                  <Check className="mr-2 h-5 w-5 text-primary" />
                  <span>E-posta desteği</span>
                </li>
              </ul>
            </CardContent>
            <CardFooter>
              <Link href="/register" className="w-full">
                <Button className="w-full">Şimdi Başla</Button>
              </Link>
            </CardFooter>
          </Card>
          <Card className="flex flex-col border-primary">
            <CardHeader>
              <div className="rounded-full w-fit bg-primary px-3 py-1 text-xs text-primary-foreground mb-2">
                POPÜLER
              </div>
              <CardTitle className="text-2xl">Yıllık Plan</CardTitle>
              <CardDescription>Salon yöneticileri için yıllık abonelik</CardDescription>
              <div className="mt-4 flex items-baseline text-gray-900 dark:text-gray-50">
                <span className="text-4xl font-extrabold tracking-tight">₺2,499</span>
                <span className="ml-1 text-xl font-semibold">/yıl</span>
              </div>
              <p className="text-sm text-muted-foreground mt-1">₺208/ay olarak hesaplanır. %30 tasarruf!</p>
            </CardHeader>
            <CardContent className="flex-1">
              <ul className="space-y-3">
                <li className="flex items-center">
                  <Check className="mr-2 h-5 w-5 text-primary" />
                  <span>Tek salon yönetimi</span>
                </li>
                <li className="flex items-center">
                  <Check className="mr-2 h-5 w-5 text-primary" />
                  <span>Sınırsız üye yönetimi</span>
                </li>
                <li className="flex items-center">
                  <Check className="mr-2 h-5 w-5 text-primary" />
                  <span>Paket yönetimi</span>
                </li>
                <li className="flex items-center">
                  <Check className="mr-2 h-5 w-5 text-primary" />
                  <span>Gelişmiş raporlar ve analizler</span>
                </li>
                <li className="flex items-center">
                  <Check className="mr-2 h-5 w-5 text-primary" />
                  <span>Öncelikli destek</span>
                </li>
                <li className="flex items-center">
                  <Check className="mr-2 h-5 w-5 text-primary" />
                  <span>Öne çıkarma özellikleri</span>
                </li>
              </ul>
            </CardContent>
            <CardFooter>
              <Link href="/register" className="w-full">
                <Button className="w-full">Şimdi Başla</Button>
              </Link>
            </CardFooter>
          </Card>
        </div>
      </div>
    </section>
  )
}

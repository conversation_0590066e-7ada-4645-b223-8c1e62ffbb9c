import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { CalendarD<PERSON>, Clock, Dumbbell } from "lucide-react"

export function MemberDashboard() {
  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Üye Paneli</h2>
          <p className="text-muted-foreground">Üyeliklerinizi ve aktivitelerinizi takip edin.</p>
        </div>
        <div className="flex items-center gap-2">
          <Link href="/settings/profile">
            <Button variant="outline">Profil <PERSON></Button>
          </Link>
          <Link href="/findGym">
            <Button>Yeni Salon Bul</Button>
          </Link>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Aktif Üyelikler</CardTitle>
            <Dumbbell className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{}</div>
            <p className="text-xs text-muted-foreground"> toplam salon üyeliği</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Yaklaşan Antrenman</CardTitle>
            <CalendarDays className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{"Planlanmadı"}</div>
            <p className="text-xs text-muted-foreground">
              "Yaklaşan antrenman yok"
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Bu Ayki Ziyaretler</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">monthlyVisits</div>
            <p className="text-xs text-muted-foreground">
           "İyi gidiyorsun!"
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Bekleyen İstekler</CardTitle>
            <Dumbbell className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">pendingRequests</div>
            <p className="text-xs text-muted-foreground">
              "Onay bekleyen istekler var"
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="memberships">
        <TabsList>
          <TabsTrigger value="memberships">Üyeliklerim</TabsTrigger>
          <TabsTrigger value="activities">Son Aktiviteler</TabsTrigger>
        </TabsList>
        <TabsContent value="memberships" className="space-y-4">
         
        </TabsContent>
        <TabsContent value="activities" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Son Aktiviteler</CardTitle>
              <CardDescription>Son ziyaretleriniz, satın almalarınız ve antrenmanlarınız</CardDescription>
            </CardHeader>
            <CardContent>
             
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

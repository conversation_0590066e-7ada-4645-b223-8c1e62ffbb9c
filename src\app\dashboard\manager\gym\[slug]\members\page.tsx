import { notFound } from "next/navigation";
import { getGymBySlugSecure } from "@/app/actions/gym-actions";
import { getMembershipsByGymId } from "@/app/actions/membership-actions";
import { MembersPageClient } from "@/components/dashboard/members/members-page-client";

export default async function MembersPage({
  params,
}: {
  params: Promise<{ slug: string }>;
}) {
  const { slug } = await params;

  try {
    // Server-side'da gym bilgisini çek
    const gymResponse = await getGymBySlugSecure(slug);

    if (!gymResponse.success || !gymResponse.data) {
      notFound();
    }

    const gym = gymResponse.data;

    // Server-side'da initial members'ı çek
    const membersResponse = await getMembershipsByGymId(gym.id);

    const initialMembers = membersResponse.success ? membersResponse.data : [];

    return <MembersPageClient gym={gym} initialMembers={initialMembers} />;
  } catch (error) {
    console.error("Members page error:", error);
    notFound();
  }
}

import { <PERSON><PERSON><PERSON>, Users, CreditCard, BarChart4, Calendar, MapPin } from "lucide-react"

export function Features() {
  return (
    <section id="features" className="w-full flex flex-col items-center justify-center py-12 md:py-24 lg:py-32">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <div className="space-y-2">
            <div className="inline-block rounded-lg bg-primary px-3 py-1 text-sm text-primary-foreground">
              Özellikler
            </div>
            <h2 className="text-3xl font-bold tracking-tighter md:text-4xl/tight">
              Sportiva ile Neler Yapabilirsiniz?
            </h2>
            <p className="max-w-[900px] text-gray-500 md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed dark:text-gray-400">
              Spor salonu yöneticileri ve üyeler için tasarlanm<PERSON><PERSON> kapsamlı çözümler
            </p>
          </div>
        </div>
        <div className="mx-auto grid max-w-5xl grid-cols-1 gap-6 py-12 md:grid-cols-2 lg:grid-cols-3">
          <div className="flex flex-col items-center space-y-2 rounded-lg border p-6 shadow-sm">
            <div className="rounded-full bg-primary/10 p-3">
              <Dumbbell className="h-6 w-6 text-primary" />
            </div>
            <h3 className="text-xl font-bold">Salon Yönetimi</h3>
            <p className="text-center text-gray-500 dark:text-gray-400">
              Salonunuzu dijital ortamda yönetin, üye ve paket takibini kolayca yapın.
            </p>
          </div>
          <div className="flex flex-col items-center space-y-2 rounded-lg border p-6 shadow-sm">
            <div className="rounded-full bg-primary/10 p-3">
              <Users className="h-6 w-6 text-primary" />
            </div>
            <h3 className="text-xl font-bold">Üye Yönetimi</h3>
            <p className="text-center text-gray-500 dark:text-gray-400">
              Üyelerinizi takip edin, katılım isteklerini onaylayın ve iletişimde kalın.
            </p>
          </div>
          <div className="flex flex-col items-center space-y-2 rounded-lg border p-6 shadow-sm">
            <div className="rounded-full bg-primary/10 p-3">
              <CreditCard className="h-6 w-6 text-primary" />
            </div>
            <h3 className="text-xl font-bold">Paket Yönetimi</h3>
            <p className="text-center text-gray-500 dark:text-gray-400">
              Özel üyelik paketleri oluşturun, satışları ve gelirleri takip edin.
            </p>
          </div>
          <div className="flex flex-col items-center space-y-2 rounded-lg border p-6 shadow-sm">
            <div className="rounded-full bg-primary/10 p-3">
              <MapPin className="h-6 w-6 text-primary" />
            </div>
            <h3 className="text-xl font-bold">Salon Keşfi</h3>
            <p className="text-center text-gray-500 dark:text-gray-400">
              Size en yakın ve en uygun spor salonlarını harita üzerinde keşfedin.
            </p>
          </div>
          <div className="flex flex-col items-center space-y-2 rounded-lg border p-6 shadow-sm">
            <div className="rounded-full bg-primary/10 p-3">
              <Calendar className="h-6 w-6 text-primary" />
            </div>
            <h3 className="text-xl font-bold">Üyelik Takibi</h3>
            <p className="text-center text-gray-500 dark:text-gray-400">
              Üyeliklerinizi ve paketlerinizi kolayca yönetin, süreleri takip edin.
            </p>
          </div>
          <div className="flex flex-col items-center space-y-2 rounded-lg border p-6 shadow-sm">
            <div className="rounded-full bg-primary/10 p-3">
              <BarChart4 className="h-6 w-6 text-primary" />
            </div>
            <h3 className="text-xl font-bold">Analitik Raporlar</h3>
            <p className="text-center text-gray-500 dark:text-gray-400">
              Detaylı raporlar ve analizlerle işletmenizin performansını ölçün.
            </p>
          </div>
        </div>
      </div>
    </section>
  )
}

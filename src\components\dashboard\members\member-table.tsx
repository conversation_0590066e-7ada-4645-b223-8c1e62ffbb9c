"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Loader2, RefreshCw } from "lucide-react";
import { format } from "date-fns";
import { tr } from "date-fns/locale";
import { MemberWithDetails } from "./members-page-client";

interface MemberTableProps {
  members: MemberWithDetails[];
  isLoading: boolean;
  onRefresh: () => void;
}

export function MemberTable({
  members,
  isLoading,
  onRefresh,
}: MemberTableProps) {
  const [selectedMember, setSelectedMember] =
    useState<MemberWithDetails | null>(null);

  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return "-";
    return format(new Date(dateString), "d MMMM yyyy", {
      locale: tr,
    });
  };

  const getUserInitials = (user: any) => {
    if (!user?.name) return "??";
    const nameParts = `${user.name} ${user.surname || ""}`.trim().split(/\s+/);
    return nameParts
      .map((p) => p[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  const getUserFullName = (user: any) => {
    if (!user) return "";
    return `${user.name || ""} ${user.surname || ""}`.trim() || user.email;
  };

  const getMembershipBadge = (status: string) => {
    switch (status) {
      case "active":
        return <Badge className="bg-green-500 hover:bg-green-600">Aktif</Badge>;
      case "passive":
        return <Badge variant="secondary">Pasif</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Üye Listesi</CardTitle>
          <p className="text-sm text-muted-foreground">
            {members.length} üye gösteriliyor
          </p>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={onRefresh}
          disabled={isLoading}
        >
          {isLoading ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <RefreshCw className="h-4 w-4" />
          )}
        </Button>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Üye</TableHead>
              <TableHead>Durum</TableHead>
              <TableHead>Kayıt Tarihi</TableHead>
              <TableHead className="text-right">İşlemler</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={4} className="h-24 text-center">
                  <div className="flex justify-center">
                    <Loader2 className="h-5 w-5 animate-spin text-muted-foreground" />
                  </div>
                </TableCell>
              </TableRow>
            ) : members.length > 0 ? (
              members.map((member) => (
                <TableRow key={member.membership.id}>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <Avatar>
                        <AvatarImage
                          src={member.user?.profile_picture_url || ""}
                          alt={getUserFullName(member.user)}
                        />
                        <AvatarFallback>
                          {getUserInitials(member.user)}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-medium">
                          {getUserFullName(member.user)}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {member.user?.email}
                        </div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    {getMembershipBadge(member.membership.status)}
                  </TableCell>
                  <TableCell>
                    {formatDate(member.membership.created_at)}
                  </TableCell>
                  <TableCell className="text-right">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setSelectedMember(member)}
                    >
                      Detaylar
                    </Button>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={4} className="h-24 text-center">
                  <div className="text-muted-foreground">
                    Henüz üye bulunmuyor
                  </div>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}

import { MetadataRoute } from 'next'

export default function robots(): MetadataRoute.Robots {
  return {
    rules: [
      {
        userAgent: '*',
        allow: '/',
        disallow: [
          '/dashboard/',
          '/settings/',
          '/api/',
          '/auth/',
          '/_next/',
          '/admin/',
        ],
      },
      {
        userAgent: 'Googlebot',
        allow: [
          '/',
          '/login',
          '/register',
          '/about',
          '/findGym',
        ],
        disallow: [
          '/dashboard/',
          '/settings/',
          '/api/',
          '/auth/',
          '/_next/',
          '/admin/',
        ],
      },
    ],
    sitemap: 'https://sportiva.com/sitemap.xml',
  }
}

"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { AlertCircle, Upload } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { updateUserProfile, updateProfilePicture } from "@/app/actions/user-actions";
import { Users } from "@/lib/supabase/types";
import { toast } from "sonner";

interface ProfileFormProps {
  userId: string;
  initialData: Users | undefined;
}

export function ProfileForm({ userId, initialData }: ProfileFormProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    name: initialData?.name || "",
    surname: initialData?.surname || "",
    age: initialData?.age || null,
    height_cm: initialData?.height_cm?.toString() || "",
    weight_kg: initialData?.weight_kg?.toString() || "",
    gender: initialData?.gender || "",
    fitness_goal: initialData?.fitness_goal || "",
    profilePicture: null as File | null,
  });
  const [previewUrl, setPreviewUrl] = useState<string | null>(
    initialData?.profile_picture_url || ""
  );

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Dosya boyutu kontrolü (5MB)
    if (file.size > 5 * 1024 * 1024) {
      setError("Dosya boyutu 5MB'dan küçük olmalıdır.");
      return;
    }

    // Sadece resim dosyalarını kabul et
    if (!file.type.startsWith("image/")) {
      setError("Sadece resim dosyaları yükleyebilirsiniz.");
      return;
    }

    setFormData((prev) => ({ ...prev, profilePicture: file }));
    setPreviewUrl(URL.createObjectURL(file));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setSuccess(null);

    // Basic validation
    if (!formData.name || !formData.surname) {
      setError("Ad ve soyad alanları zorunludur.");
      return;
    }

    setIsLoading(true);

    try {
      let profilePictureUrl = initialData?.profile_picture_url || "";

      // Upload profile picture if provided
      if (formData.profilePicture) {
        try {
          // Profil resmi yükleme için FormData oluştur
          const pictureFormData = new FormData();
          pictureFormData.append("userId", userId);
          pictureFormData.append("profilePicture", formData.profilePicture);
          
          // Server action ile profil resmini yükle
          const pictureResult = await updateProfilePicture(pictureFormData);
          
          if (pictureResult.error) {
            throw new Error(pictureResult.error);
          }
          
          if (pictureResult.success && pictureResult.data) {
            profilePictureUrl = pictureResult.data.profile_picture_url || "";
          }
        } catch (err: any) {
          console.error("Profil resmi yükleme hatası:", err);
          toast.error("Profil resmi yüklenirken bir hata oluştu");
        }
      }

      // Update user profile using server action
      const result = await updateUserProfile(userId, {
        name: formData.name,
        surname: formData.surname,
        profile_picture_url: profilePictureUrl,
        age: formData.age || null,
        height_cm: formData.height_cm ? Number(formData.height_cm) : null,
        weight_kg: formData.weight_kg ? Number(formData.weight_kg) : null,
        gender: formData.gender || null,
        fitness_goal: formData.fitness_goal || null,
      });

      if (result.error) {
        throw new Error(result.error);
      }

      setSuccess("Profil bilgileriniz başarıyla güncellendi.");
      toast.success("Profil bilgileriniz güncellendi");
      router.refresh();
    } catch (err: any) {
      console.error("Profile update error:", err);
      setError(
        err.message ||
          "Profil bilgileriniz kaydedilirken bir hata oluştu. Lütfen tekrar deneyin."
      );
      toast.error("Profil güncelleme hatası");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert
          variant="default"
          className="bg-green-50 text-green-800 border-green-200"
        >
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}

      <div className="flex justify-center mb-6">
        <div className="relative">
          <div className="h-24 w-24 rounded-full overflow-hidden bg-gray-100 flex items-center justify-center">
            {previewUrl ? (
              <img
                src={previewUrl}
                alt="Profile preview"
                className="h-full w-full object-cover"
              />
            ) : (
              <Upload className="h-8 w-8 text-gray-400" />
            )}
          </div>
          <Label
            htmlFor="profilePicture"
            className="absolute bottom-0 right-0 h-8 w-8 rounded-full bg-primary text-primary-foreground flex items-center justify-center cursor-pointer"
          >
            +
          </Label>
          <Input
            id="profilePicture"
            name="profilePicture"
            type="file"
            accept="image/*"
            onChange={handleFileChange}
            className="hidden"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="name">Ad</Label>
          <Input
            id="name"
            name="name"
            value={formData.name}
            onChange={handleChange}
            required
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="surname">Soyad</Label>
          <Input
            id="surname"
            name="surname"
            value={formData.surname}
            onChange={handleChange}
            required
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="space-y-2">
          <Label htmlFor="age">Yaş</Label>
          <Input
            id="age"
            name="age"
            type="number"
            value={formData.age || ""}  
            onChange={handleChange}
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="height_cm">Boy (cm)</Label>
          <Input
            id="height_cm"
            name="height_cm"
            type="number"
            value={formData.height_cm || ""}
            onChange={handleChange}
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="weight_kg">Kilo (kg)</Label>
          <Input
            id="weight_kg"
            name="weight_kg"
            type="number"
            step="0.1"
            value={formData.weight_kg || ""}
            onChange={handleChange}
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="gender">Cinsiyet</Label>
        <Select
          value={formData.gender}
          onValueChange={(value) => handleSelectChange("gender", value)}
        >
          <SelectTrigger id="gender">
            <SelectValue placeholder="Seçiniz" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="male">Erkek</SelectItem>
            <SelectItem value="female">Kadın</SelectItem>
            <SelectItem value="other">Diğer</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="fitness_goal">Fitness Hedefi</Label>
        <Textarea
          id="fitness_goal"
          name="fitness_goal"
          value={formData.fitness_goal || ""}
          onChange={handleChange}
          placeholder="Fitness hedeflerinizi kısaca açıklayın"
          rows={3}
        />
      </div>

      <Button type="submit" disabled={isLoading}>
        {isLoading ? "Kaydediliyor..." : "Değişiklikleri Kaydet"}
      </Button>
    </form>
  );
}

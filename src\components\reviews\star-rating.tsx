"use client"

import { useState } from "react"
import { Star } from "lucide-react"
import { cn } from "@/lib/utils"

interface StarRatingProps {
  rating: number
  onRatingChange?: (rating: number) => void
  readonly?: boolean
  size?: number
  className?: string
}

export function StarRating({ rating, onRatingChange, readonly = false, size = 20, className }: StarRatingProps) {
  const [hoverRating, setHoverRating] = useState(0)

  const handleMouseEnter = (index: number) => {
    if (readonly) return
    setHoverRating(index)
  }

  const handleMouseLeave = () => {
    if (readonly) return
    setHoverRating(0)
  }

  const handleClick = (index: number) => {
    if (readonly || !onRatingChange) return
    onRatingChange(index)
  }

  return (
    <div className={cn("flex items-center", className)} onMouseLeave={handleMouseLeave}>
      {[1, 2, 3, 4, 5].map((index) => {
        const isActive = (hoverRating || rating) >= index

        return (
          <Star
            key={index}
            className={cn(
              "transition-colors",
              isActive ? "fill-primary text-primary" : "text-muted-foreground",
              !readonly && "cursor-pointer",
            )}
            size={size}
            onClick={() => handleClick(index)}
            onMouseEnter={() => handleMouseEnter(index)}
          />
        )
      })}
    </div>
  )
}

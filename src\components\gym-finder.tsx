"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  <PERSON>Footer,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

import { Badge } from "@/components/ui/badge";
import { MapPin, Search, Star, Loader2, Package, UserPlus } from "lucide-react";
import {
  getAllActiveGyms,
  getUniqueCities,
  getUniqueFeatures,
  getGymStatsForListing,
} from "@/app/actions/gym-actions";
import { sendMembershipRequest } from "@/app/actions/gym-invitation-actions";
import { Tables } from "@/lib/supabase/types";
import { useToast } from "@/components/ui/use-toast";
import { createClient } from "@/utils/supabase/client";

// Salon verisi için tip tanımı
type GymWithStats = Tables<"gyms"> & {
  packageCount: number;
  averageRating: number;
  reviewCount: number;
};

export function GymFinder() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCity, setSelectedCity] = useState("");
  const [selectedFeatures, setSelectedFeatures] = useState<string[]>([]);
  const [gyms, setGyms] = useState<GymWithStats[]>([]);
  const [filteredGyms, setFilteredGyms] = useState<GymWithStats[]>([]);
  const [cities, setCities] = useState<string[]>([]);
  const [features, setFeatures] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [sortBy, setSortBy] = useState<"name" | "rating" | "newest">("newest");
  const [sendingRequest, setSendingRequest] = useState<string | null>(null);
  const [user, setUser] = useState<any>(null);
  const { toast } = useToast();

  // Verileri yükle
  useEffect(() => {
    loadInitialData();
    checkUser();
  }, []);

  // Filtreleme ve sıralama
  useEffect(() => {
    filterAndSortGyms();
  }, [gyms, searchTerm, selectedCity, selectedFeatures, sortBy]);

  // Kullanıcı kontrolü
  const checkUser = async () => {
    try {
      const supabase = createClient();
      const { data: userData } = await supabase.auth.getUser();
      setUser(userData?.user || null);
    } catch (error) {
      console.error("Kullanıcı kontrolü sırasında hata:", error);
    }
  };

  const loadInitialData = async () => {
    try {
      setLoading(true);

      // Paralel olarak tüm verileri yükle
      const [gymsResponse, citiesResponse, featuresResponse] =
        await Promise.all([
          getAllActiveGyms(),
          getUniqueCities(),
          getUniqueFeatures(),
        ]);

      if (gymsResponse.success && gymsResponse.data) {
        // Her salon için istatistikleri yükle
        const gymsWithStats = await Promise.all(
          gymsResponse.data.map(async (gym) => {
            const statsResponse = await getGymStatsForListing(gym.id);
            return {
              ...gym,
              packageCount:
                statsResponse.success && statsResponse.data
                  ? statsResponse.data.packageCount
                  : 0,
              averageRating:
                statsResponse.success && statsResponse.data
                  ? statsResponse.data.averageRating
                  : 0,
              reviewCount:
                statsResponse.success && statsResponse.data
                  ? statsResponse.data.reviewCount
                  : 0,
            };
          })
        );
        setGyms(gymsWithStats);
      }

      if (citiesResponse.success && citiesResponse.data) {
        setCities(citiesResponse.data);
      }

      if (featuresResponse.success && featuresResponse.data) {
        setFeatures(featuresResponse.data);
      }
    } catch (error) {
      console.error("Veriler yüklenirken hata:", error);
      toast({
        title: "Hata",
        description: "Salon verileri yüklenirken bir hata oluştu.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const filterAndSortGyms = () => {
    let filtered = gyms.filter((gym) => {
      // Arama terimi filtresi
      if (searchTerm.trim()) {
        const searchLower = searchTerm.toLowerCase();
        const matchesSearch =
          gym.name.toLowerCase().includes(searchLower) ||
          gym.description?.toLowerCase().includes(searchLower) ||
          gym.address?.toLowerCase().includes(searchLower) ||
          gym.city?.toLowerCase().includes(searchLower) ||
          gym.district?.toLowerCase().includes(searchLower);

        if (!matchesSearch) return false;
      }

      // Şehir filtresi
      if (selectedCity && gym.city !== selectedCity) {
        return false;
      }

      // Özellik filtresi
      if (selectedFeatures.length > 0) {
        const hasAllFeatures = selectedFeatures.every((feature) =>
          gym.features?.includes(feature)
        );
        if (!hasAllFeatures) return false;
      }

      return true;
    });

    // Sıralama
    filtered.sort((a, b) => {
      switch (sortBy) {
        case "name":
          return a.name.localeCompare(b.name);
        case "rating":
          return b.averageRating - a.averageRating;
        case "newest":
        default:
          return (
            new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
          );
      }
    });

    setFilteredGyms(filtered);
  };

  const toggleFeature = (feature: string) => {
    if (selectedFeatures.includes(feature)) {
      setSelectedFeatures(selectedFeatures.filter((f) => f !== feature));
    } else {
      setSelectedFeatures([...selectedFeatures, feature]);
    }
  };

  const clearFilters = () => {
    setSearchTerm("");
    setSelectedCity("");
    setSelectedFeatures([]);
  };

  // Katılım isteği gönderme
  const handleSendMembershipRequest = async (gymId: string) => {
    if (!user) {
      toast({
        title: "Giriş Gerekli",
        description: "Katılım isteği göndermek için giriş yapmanız gerekiyor.",
        variant: "destructive",
      });
      return;
    }

    setSendingRequest(gymId);
    try {
      const result = await sendMembershipRequest(gymId);

      if (result.success) {
        toast({
          title: "Başarılı",
          description: result.message,
        });
      } else {
        toast({
          title: "Hata",
          description: result.error,
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Katılım isteği gönderilirken hata:", error);
      toast({
        title: "Hata",
        description: "Katılım isteği gönderilirken bir hata oluştu.",
        variant: "destructive",
      });
    } finally {
      setSendingRequest(null);
    }
  };

  return (
    <div className="container mx-auto py-8">
      <div className="mb-8 space-y-2">
        <h1 className="text-3xl font-bold">Salon Bul</h1>
        <p className="text-muted-foreground">
          Size en yakın ve en uygun spor salonlarını keşfedin
        </p>
      </div>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Filtreler</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <div className="font-medium">Arama</div>
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="Salon adı veya konum ara..."
                    className="pl-8"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <div className="font-medium">Şehir</div>
                <select
                  value={selectedCity}
                  onChange={(e) => setSelectedCity(e.target.value)}
                  className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                >
                  <option value="">Tüm şehirler</option>
                  {cities.map((city) => (
                    <option key={city} value={city}>
                      {city}
                    </option>
                  ))}
                </select>
              </div>

              <div className="space-y-2">
                <div className="font-medium">Özellikler</div>
                <div className="flex flex-wrap gap-2">
                  {features.map((feature) => (
                    <Badge
                      key={feature}
                      variant={
                        selectedFeatures.includes(feature)
                          ? "default"
                          : "outline"
                      }
                      className="cursor-pointer"
                      onClick={() => toggleFeature(feature)}
                    >
                      {feature}
                    </Badge>
                  ))}
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button
                variant="outline"
                className="w-full"
                onClick={clearFilters}
              >
                Filtreleri Temizle
              </Button>
            </CardFooter>
          </Card>

          <div className="h-[400px] rounded-lg border bg-muted flex items-center justify-center">
            <div className="text-center">
              <MapPin className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
              <p className="text-sm text-muted-foreground">
                Harita görünümü burada olacak
              </p>
              <p className="text-xs text-muted-foreground mt-1">
                (Google Maps API entegrasyonu)
              </p>
            </div>
          </div>
        </div>

        <div className="lg:col-span-2 space-y-6">
          <div className="flex items-center justify-between">
            <div className="text-sm text-muted-foreground">
              {filteredGyms.length} salon bulundu
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant={sortBy === "name" ? "default" : "outline"}
                size="sm"
                onClick={() => setSortBy("name")}
              >
                İsme Göre
              </Button>
              <Button
                variant={sortBy === "rating" ? "default" : "outline"}
                size="sm"
                onClick={() => setSortBy("rating")}
              >
                Puana Göre
              </Button>
              <Button
                variant={sortBy === "newest" ? "default" : "outline"}
                size="sm"
                onClick={() => setSortBy("newest")}
              >
                En Yeni
              </Button>
            </div>
          </div>

          {loading ? (
            <div className="flex h-[400px] items-center justify-center rounded-lg border">
              <div className="text-center">
                <Loader2 className="h-8 w-8 mx-auto mb-2 animate-spin text-muted-foreground" />
                <p className="text-muted-foreground">Salonlar yükleniyor...</p>
              </div>
            </div>
          ) : filteredGyms.length > 0 ? (
            <div className="space-y-4">
              {filteredGyms.map((gym) => (
                <Card key={gym.id} className="overflow-hidden">
                  <div className="md:flex">
                    <div className="md:w-1/3">
                      <img
                        src={
                          gym.cover_image_url ||
                          gym.logo_url ||
                          "/placeholder.svg"
                        }
                        alt={gym.name}
                        className="h-48 w-full object-cover md:h-full"
                      />
                    </div>
                    <div className="p-6 md:w-2/3">
                      <div className="flex items-start justify-between">
                        <div>
                          <h3 className="text-xl font-bold">{gym.name}</h3>
                          <div className="flex items-center gap-1 mt-1 text-sm text-muted-foreground">
                            <MapPin className="h-4 w-4" />
                            {gym.address && `${gym.address}, `}
                            {gym.district && `${gym.district}, `}
                            {gym.city}
                          </div>
                          {gym.description && (
                            <p className="mt-2 text-sm text-muted-foreground line-clamp-2">
                              {gym.description}
                            </p>
                          )}
                        </div>
                        <div className="flex items-center gap-1">
                          <Star className="h-4 w-4 fill-primary text-primary" />
                          <span className="font-medium">
                            {gym.averageRating > 0
                              ? gym.averageRating.toFixed(1)
                              : "0.0"}
                          </span>
                          <span className="text-sm text-muted-foreground">
                            ({gym.reviewCount})
                          </span>
                        </div>
                      </div>

                      {gym.features && gym.features.length > 0 && (
                        <div className="mt-4 flex flex-wrap gap-2">
                          {gym.features.slice(0, 4).map((feature) => (
                            <Badge key={feature} variant="secondary">
                              {feature}
                            </Badge>
                          ))}
                          {gym.features.length > 4 && (
                            <Badge variant="outline">
                              +{gym.features.length - 4} daha
                            </Badge>
                          )}
                        </div>
                      )}

                      <div className="mt-6 flex items-center justify-between">
                        <div className="flex items-center gap-4 text-sm text-muted-foreground">
                          <div className="flex items-center gap-1">
                            <Package className="h-4 w-4" />
                            <span className="font-medium">
                              {gym.packageCount}
                            </span>{" "}
                            paket
                          </div>
                          {gym.reviewCount > 0 && (
                            <div className="flex items-center gap-1">
                              <Star className="h-4 w-4" />
                              <span className="font-medium">
                                {gym.reviewCount}
                              </span>{" "}
                              değerlendirme
                            </div>
                          )}
                        </div>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="outline"
                            onClick={() => handleSendMembershipRequest(gym.id)}
                            disabled={sendingRequest === gym.id}
                          >
                            {sendingRequest === gym.id ? (
                              <Loader2 className="h-4 w-4 animate-spin mr-2" />
                            ) : (
                              <UserPlus className="h-4 w-4 mr-2" />
                            )}
                            {sendingRequest === gym.id
                              ? "Gönderiliyor..."
                              : "Katılım İsteği"}
                          </Button>
                          <Link href={`/gyms/${gym.slug}`}>
                            <Button>Detayları Gör</Button>
                          </Link>
                        </div>
                      </div>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          ) : (
            <div className="flex h-[400px] items-center justify-center rounded-lg border">
              <div className="text-center">
                <p className="text-muted-foreground">
                  Arama kriterlerinize uygun salon bulunamadı.
                </p>
                <p className="text-sm text-muted-foreground mt-1">
                  Lütfen filtrelerinizi değiştirin.
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

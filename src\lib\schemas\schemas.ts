import { z } from "zod";

/**
 * Yeni spor salonu oluşturmak için validasyon şeması
 * Server actions'da ve client formlarında ortak kullanılır
 */
export const gymCreateSchema = z.object({
  userId: z.string().uuid("Geçerli bir kullanıcı ID'si gereklidir"),
  name: z
    .string()
    .min(2, "Salon adı en az 2 karakter olmalıdır.")
    .max(50, "Salon adı en fazla 50 karakter olabilir."),
  city: z
    .string()
    .min(2, "Şehir adı en az 2 karakter olmalıdır.")
    .max(50, "Şehir adı en fazla 50 karakter olabilir."),
  district: z
    .string()
    .max(50, "İlçe adı en fazla 50 karakter olabilir.")
    .optional()
    .or(z.literal("")),
  address: z
    .string()
    .min(5, "Adres en az 5 karakter olmalıdır.")
    .max(255, "Adres en fazla 255 karakter olabilir."),
  phone: z
    .string()
    .min(10, "Telefon numarası en az 10 karakter olmalıdır.")
    .max(20, "Telefon numarası en fazla 20 karakter olabilir."),
  email: z.string().email("Geçerli bir e-posta adresi girmelisiniz."),
  description: z
    .string()
    .min(1, "Açıklama gereklidir.")
    .max(500, "Açıklama en fazla 500 karakter olabilir."),
  gym_type: z
    .string()
    .min(1, "Salon türü gereklidir.")
    .max(50, "Salon tipi en fazla 50 karakter olabilir."),
});

/**
 * Spor salonu bilgilerini güncellemek için validasyon şeması
 * Server actions'da ve client formlarında ortak kullanılır
 */
export const gymUpdateSchema = z.object({
  gymId: z.string().uuid("Geçerli bir salon ID'si gereklidir"),
  name: z
    .string()
    .min(2, "Salon adı en az 2 karakter olmalıdır.")
    .max(50, "Salon adı en fazla 50 karakter olabilir.")
    .optional(),
  city: z
    .string()
    .min(2, "Şehir adı en az 2 karakter olmalıdır.")
    .max(50, "Şehir adı en fazla 50 karakter olabilir.")
    .optional(),
  district: z
    .string()
    .max(50, "İlçe adı en fazla 50 karakter olabilir.")
    .optional()
    .or(z.literal("")),
  address: z
    .string()
    .min(5, "Adres en az 5 karakter olmalıdır.")
    .max(255, "Adres en fazla 255 karakter olabilir.")
    .optional(),
  phone: z
    .string()
    .min(10, "Telefon numarası en az 10 karakter olmalıdır.")
    .max(20, "Telefon numarası en fazla 20 karakter olabilir.")
    .optional()
    .or(z.literal("")),
  email: z
    .string()
    .email("Geçerli bir e-posta adresi olmalıdır.")
    .optional()
    .or(z.literal("")),
  description: z
    .string()
    .max(500, "Açıklama en fazla 500 karakter olabilir.")
    .optional()
    .or(z.literal("")),
  gym_type: z
    .string()
    .max(50, "Salon tipi en fazla 50 karakter olabilir.")
    .optional()
    .or(z.literal("")),
  logo_url: z
    .string()
    .url("Geçerli bir URL olmalıdır.")
    .optional()
    .or(z.literal("")),
  cover_image_url: z
    .string()
    .url("Geçerli bir URL olmalıdır.")
    .optional()
    .or(z.literal("")),
  status: z
    .enum(["active", "inactive", "pending"])
    .optional(),
  features: z.array(z.string()).optional()
});

/**
 * Spor salonu arama işlemleri için validasyon şeması
 */
export const gymSearchSchema = z.object({
  query: z.string().optional(),
  city: z.string().optional(),
  features: z.array(z.string()).optional(),
  maxDistance: z.number().optional()
});

// Client tarafı form için kullandığımız formSchema 
// Artık gym update şemasının userId hariç versiyonu olacak
export const gymFormSchema = gymUpdateSchema.omit({ gymId: true });

// Type for Zod schema inference
export type GymFormValues = z.infer<typeof gymFormSchema>;
export type GymCreateValues = z.infer<typeof gymCreateSchema>;
export type GymUpdateValues = z.infer<typeof gymUpdateSchema>;
export type GymSearchValues = z.infer<typeof gymSearchSchema>;

// Default form values
export const defaultGymFormValues: GymFormValues = {
  name: "",
  city: "",
  district: "",
  address: "",
  phone: "",
  email: "",
  description: "",
  gym_type: "",
  logo_url: "",
  cover_image_url: "",
  status: "active",
}; 


// Public user schemas
/**
 * Üye detayları validation şeması
 */
export const PublicUserSchema = z.object({
  age: z.coerce.number().min(16, "Yaş en az 16 olmalıdır").max(100, "Yaş en fazla 100 olabilir"),
  height_cm: z.coerce.number().min(100, "Boy en az 100 cm olmalıdır").max(250, "Boy en fazla 250 cm olabilir"),
  weight_kg: z.coerce.number().min(30, "Kilo en az 30 kg olmalıdır").max(250, "Kilo en fazla 250 kg olabilir"),
  gender: z.enum(["male", "female", "other"], {
    required_error: "Cinsiyet seçimi gereklidir",
    invalid_type_error: "Geçerli bir cinsiyet seçiniz",
  }),
});

export type UserDetails = z.infer<typeof PublicUserSchema>;


// Validasyon şemaları
export const userUpdateSchema = z.object({
    userId: z.string().uuid("Geçerli bir kullanıcı ID'si gereklidir"),
    name: z.string().min(2, "Ad en az 2 karakter olmalıdır"),
    surname: z.string().min(2, "Soyad en az 2 karakter olmalıdır"),
    phone: z.string().optional(),
    age: z.number().optional(),
    gender: z.enum(["male", "female", "other", ""]).optional(),
    fitness_goal: z.string().optional(),
    profile_picture_url: z.string().optional(),
    height_cm: z.number().optional(),
    weight_kg: z.number().optional(),
    is_manager: z.boolean().optional(),
    email: z.string().email("Geçerli bir e-posta adresi giriniz").optional(),
  })
  
export const passwordResetSchema = z.object({
    email: z.string().email("Geçerli bir e-posta adresi giriniz"),
  })
  
export const passwordUpdateSchema = z.object({
    userId: z.string().uuid("Geçerli bir kullanıcı ID'si gereklidir"),
    currentPassword: z.string().min(6, "Mevcut şifre en az 6 karakter olmalıdır"),
    newPassword: z.string().min(8, "Yeni şifre en az 8 karakter olmalıdır")
      .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, "Şifre en az bir büyük harf, bir küçük harf ve bir rakam içermelidir"),
  })
  
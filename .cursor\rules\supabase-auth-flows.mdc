---
description:
globs:
alwaysApply: false
---
# Next.js ve Supabase Auth Akışları

Bu kural, Next.js ve Supabase kullanarak temel kimlik doğrulama akışlarını nasıl uygulayacağınızı açıklar.

## Kay<PERSON><PERSON> (Sign Up)

Yeni bir kullanıcı hesabı oluşturmak için email/password yöntemi:

```tsx
// Client Component
'use client'

import { useState } from 'react'
import { createSupabaseBrowserClient } from '@/lib/supabase/client'

export default function SignUpForm() {
  const supabase = createSupabaseBrowserClient()
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [error, setError] = useState<string | null>(null)
  const [message, setMessage] = useState<string | null>(null)
  const [loading, setLoading] = useState(false)

  const handleSignUp = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setError(null)
    setMessage(null)
    setLoading(true)
    
    // Email/Password ile kayıt
    const { error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        emailRedirectTo: `${window.location.origin}/auth/callback`,
      },
    })
    
    setLoading(false)
    
    if (error) {
      setError(error.message)
    } else {
      setMessage('Kayıt başarılı! Lütfen e-postanızı kontrol edin.')
      setEmail('')
      setPassword('')
    }
  }

  return (
    <form onSubmit={handleSignUp}>
      {/* Form içeriği */}
      <input 
        type="email" 
        value={email}
        onChange={(e) => setEmail(e.target.value)}
        required
      />
      <input 
        type="password" 
        value={password}
        onChange={(e) => setPassword(e.target.value)}
        required
      />
      {error && <p style={{ color: 'red' }}>{error}</p>}
      {message && <p style={{ color: 'green' }}>{message}</p>}
      <button type="submit" disabled={loading}>
        {loading ? 'Kaydediliyor...' : 'Kayıt Ol'}
      </button>
    </form>
  )
}
```

### Server Action ile Kayıt

```tsx
// src/app/auth/actions.ts (Server Action)
'use server'

import { createSupabaseServerClient } from '@/lib/supabase/server'
import { headers } from 'next/headers'
import { z } from 'zod'

const SignUpSchema = z.object({
  email: z.string().email({ message: 'Geçerli bir e-posta adresi giriniz.' }),
  password: z.string().min(6, { message: 'Şifre en az 6 karakter olmalıdır.' }),
})

export async function signUpAction(prevState: any, formData: FormData) {
  const email = formData.get('email') as string
  const password = formData.get('password') as string

  const validation = SignUpSchema.safeParse({ email, password })
  if (!validation.success) {
    return {
      message: 'Geçersiz form verileri.',
      errors: validation.error.flatten().fieldErrors,
      fieldValues: { email, password: '' }
    }
  }

  const supabase = createSupabaseServerClient()
  const origin = headers().get('origin')

  const { error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      emailRedirectTo: `${origin}/auth/callback`,
    },
  })

  if (error) {
    return {
      message: 'Kayıt sırasında bir hata oluştu: ' + error.message,
      errors: null,
      fieldValues: { email, password: '' }
    }
  }

  return {
    message: 'Kayıt başarılı! Lütfen e-posta adresinize gönderilen onay bağlantısını kontrol edin.',
    errors: null,
    fieldValues: { email: '', password: '' }
  }
}

// Form bileşeninde kullanımı:
// 'use client'
// import { useActionState } from 'react'
// import { signUpAction } from '../actions'
//
// const initialState = { message: null, errors: null, fieldValues: { email: '', password: '' } }
// const [state, formAction, pending] = useActionState(signUpAction, initialState)
//
// return <form action={formAction}>...</form>
```

## Giriş Yapma (Sign In)

E-posta ve şifre ile giriş:

```tsx
// Client Component
'use client'

import { useState } from 'react'
import { createSupabaseBrowserClient } from '@/lib/supabase/client'
import { useRouter } from 'next/navigation'

export default function LoginForm() {
  const supabase = createSupabaseBrowserClient()
  const router = useRouter()
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [error, setError] = useState<string | null>(null)
  const [loading, setLoading] = useState(false)

  const handleLogin = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setError(null)
    setLoading(true)
    
    const { error: signInError } = await supabase.auth.signInWithPassword({
      email,
      password,
    })
    
    setLoading(false)
    
    if (signInError) {
      setError(signInError.message)
    } else {
      // Giriş başarılı, kullanıcıyı yönlendir
      router.push('/dashboard')
      router.refresh() // Oturum değişikliğini tüm uygulamaya yansıt
    }
  }

  return (
    <form onSubmit={handleLogin}>
      {/* Form içeriği */}
      <input 
        type="email" 
        value={email}
        onChange={(e) => setEmail(e.target.value)}
        required
      />
      <input 
        type="password" 
        value={password}
        onChange={(e) => setPassword(e.target.value)}
        required
      />
      {error && <p style={{ color: 'red' }}>{error}</p>}
      <button type="submit" disabled={loading}>
        {loading ? 'Giriş Yapılıyor...' : 'Giriş Yap'}
      </button>
    </form>
  )
}
```

### Server Action ile Giriş

```tsx
// src/app/auth/actions.ts (devamı)
'use server'

// ... Önceki kodlar

import { redirect } from 'next/navigation'

const LoginSchema = z.object({
  email: z.string().email({ message: 'Geçerli bir e-posta adresi giriniz.' }),
  password: z.string().min(1, { message: 'Şifre boş olamaz.' }),
})

export async function signInAction(prevState: any, formData: FormData) {
  const email = formData.get('email') as string
  const password = formData.get('password') as string

  const validation = LoginSchema.safeParse({ email, password })
  if (!validation.success) {
    return {
      message: 'Geçersiz form verileri.',
      errors: validation.error.flatten().fieldErrors,
      fieldValues: { email, password: '' }
    }
  }

  const supabase = createSupabaseServerClient()
  const { error } = await supabase.auth.signInWithPassword({
    email,
    password,
  })

  if (error) {
    let userMessage = 'Giriş sırasında bir hata oluştu.';
    if (error.message === 'Invalid login credentials') {
      userMessage = 'Geçersiz e-posta veya şifre.';
    } else if (error.message === 'Email not confirmed') {
      userMessage = 'E-posta adresiniz henüz onaylanmamış. Lütfen e-postanızı kontrol edin.';
    }
    return {
      message: userMessage,
      errors: null,
      fieldValues: { email, password: '' }
    }
  }

  // Başarılı giriş sonrası dashboard'a yönlendir
  redirect('/dashboard')  
}
```

## Çıkış Yapma (Sign Out)

```tsx
// Client Component
'use client'

import { useState } from 'react'
import { createSupabaseBrowserClient } from '@/lib/supabase/client'
import { useRouter } from 'next/navigation'

export default function LogoutButton() {
  const supabase = createSupabaseBrowserClient()
  const router = useRouter()
  const [loading, setLoading] = useState(false)

  const handleLogout = async () => {
    setLoading(true)
    
    const { error } = await supabase.auth.signOut()
    
    setLoading(false)
    
    if (!error) {
      router.push('/auth/login')
      router.refresh() // Oturum değişikliğini tüm uygulamaya yansıt
    }
  }

  return (
    <button 
      onClick={handleLogout}
      disabled={loading}
    >
      {loading ? 'Çıkış Yapılıyor...' : 'Çıkış Yap'}
    </button>
  )
}
```

### Server Action ile Çıkış

```tsx
// src/app/auth/actions.ts (devamı)
'use server'

// ... Önceki kodlar

export async function signOutAction() {
  const supabase = createSupabaseServerClient()
  await supabase.auth.signOut()
  redirect('/auth/login')
}

// Client Component'te kullanımı:
// import { signOutAction } from '@/app/auth/actions'
// <form action={signOutAction}>
//   <button type="submit">Çıkış Yap</button>
// </form>
```

## Şifremi Unuttum / Şifre Sıfırlama

### Şifre Sıfırlama İsteği

```tsx
// src/app/auth/forgot-password/page.tsx
'use client'

import { useState } from 'react'
import { createSupabaseBrowserClient } from '@/lib/supabase/client'

export default function ForgotPasswordPage() {
  const supabase = createSupabaseBrowserClient()
  const [email, setEmail] = useState('')
  const [message, setMessage] = useState('')
  const [error, setError] = useState('')
  const [loading, setLoading] = useState(false)

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setError('')
    setMessage('')
    setLoading(true)
    
    const { error: resetError } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/auth/update-password`,
    })
    
    setLoading(false)
    
    if (resetError) {
      setError(resetError.message)
    } else {
      setMessage('Şifre sıfırlama talimatları e-posta adresinize gönderildi.')
    }
  }

  return (
    <form onSubmit={handleSubmit}>
      {/* Form içeriği */}
      <input
        type="email"
        value={email}
        onChange={(e) => setEmail(e.target.value)}
        required
      />
      {message && <p style={{ color: 'green' }}>{message}</p>}
      {error && <p style={{ color: 'red' }}>{error}</p>}
      <button type="submit" disabled={loading}>
        {loading ? 'Gönderiliyor...' : 'Sıfırlama Bağlantısı Gönder'}
      </button>
    </form>
  )
}
```

### Yeni Şifre Belirleme

```tsx
// src/app/auth/update-password/page.tsx
'use client'

import { useState, useEffect } from 'react'
import { createSupabaseBrowserClient } from '@/lib/supabase/client'
import { useRouter } from 'next/navigation'

export default function UpdatePasswordPage() {
  const supabase = createSupabaseBrowserClient()
  const router = useRouter()
  const [password, setPassword] = useState('')
  const [error, setError] = useState<string | null>(null)
  const [message, setMessage] = useState<string | null>(null)
  const [loading, setLoading] = useState(false)
  const [isValidToken, setIsValidToken] = useState<boolean | null>(null)

  useEffect(() => {
    // Supabase auth state change'i dinle ve şifre sıfırlama token'ını kontrol et
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (event === 'PASSWORD_RECOVERY') {
          setIsValidToken(true)
        }
      }
    )

    return () => {
      subscription.unsubscribe()
    }
  }, [supabase])

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setError(null)
    setMessage(null)
    setLoading(true)

    const { error: updateError } = await supabase.auth.updateUser({
      password: password,
    })

    setLoading(false)

    if (updateError) {
      setError(updateError.message)
    } else {
      setMessage('Şifreniz başarıyla güncellendi! Yönlendiriliyorsunuz...')
      // Kısa bir süre sonra giriş sayfasına yönlendir
      setTimeout(() => router.push('/auth/login'), 2000)
    }
  }

  if (isValidToken === false) {
    return <p>Geçersiz veya süresi dolmuş şifre sıfırlama bağlantısı.</p>
  }

  return (
    <form onSubmit={handleSubmit}>
      <h2>Yeni Şifre Oluştur</h2>
      <input
        type="password"
        value={password}
        onChange={(e) => setPassword(e.target.value)}
        required
      />
      {message && <p style={{ color: 'green' }}>{message}</p>}
      {error && <p style={{ color: 'red' }}>{error}</p>}
      <button type="submit" disabled={loading || isValidToken !== true}>
        {loading ? 'Güncelleniyor...' : 'Şifreyi Güncelle'}
      </button>
    </form>
  )
}
```

## Magic Link ile Giriş (Parolasız)

```tsx
'use client'

import { useState } from 'react'
import { createSupabaseBrowserClient } from '@/lib/supabase/client'

export default function MagicLoginPage() {
  const supabase = createSupabaseBrowserClient()
  const [email, setEmail] = useState('')
  const [message, setMessage] = useState('')
  const [error, setError] = useState('')
  const [loading, setLoading] = useState(false)

  const handleMagicLogin = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setError('')
    setMessage('')
    setLoading(true)

    const { error: signInError } = await supabase.auth.signInWithOtp({
      email: email,
      options: {
        emailRedirectTo: `${window.location.origin}/auth/callback`,
      },
    })
    
    setLoading(false)

    if (signInError) {
      setError(signInError.message)
    } else {
      setMessage('Giriş bağlantısı e-posta adresinize gönderildi. Lütfen kontrol edin.')
    }
  }

  return (
    <form onSubmit={handleMagicLogin}>
      <input
        type="email"
        value={email}
        onChange={(e) => setEmail(e.target.value)}
        required
      />
      {message && <p style={{ color: 'green' }}>{message}</p>}
      {error && <p style={{ color: 'red' }}>{error}</p>}
      <button type="submit" disabled={loading}>
        {loading ? 'Gönderiliyor...' : 'Giriş Bağlantısı Gönder'}
      </button>
    </form>
  )
}
```

Bu temel auth akışları, bir Next.js ve Supabase projesinde kullanıcı kimlik doğrulaması için sağlam bir temel oluşturur. Client Component'ler ve Server Actions olmak üzere iki farklı yaklaşım sunulmuştur.

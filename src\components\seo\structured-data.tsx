interface StructuredDataProps {
  type: 'Organization' | 'WebSite' | 'WebPage' | 'LoginAction' | 'RegisterAction';
  data?: any;
}

export function StructuredData({ type, data }: StructuredDataProps) {
  let jsonLd: any = {};

  switch (type) {
    case 'Organization':
      jsonLd = {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "Sportiva",
        "description": "Spor salonu yönetim platformu",
        "url": "https://sportiva.com",
        "logo": "https://sportiva.com/logo.png",
        "contactPoint": {
          "@type": "ContactPoint",
          "contactType": "customer service",
          "availableLanguage": "Turkish"
        },
        "sameAs": [
          "https://twitter.com/sportiva",
          "https://facebook.com/sportiva",
          "https://instagram.com/sportiva"
        ]
      };
      break;

    case 'WebSite':
      jsonLd = {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "name": "Sportiva",
        "url": "https://sportiva.com",
        "description": "Spor salonu yöneticileri ve üyeleri için dijital platform",
        "potentialAction": {
          "@type": "SearchAction",
          "target": "https://sportiva.com/search?q={search_term_string}",
          "query-input": "required name=search_term_string"
        }
      };
      break;

    case 'WebPage':
      jsonLd = {
        "@context": "https://schema.org",
        "@type": "WebPage",
        "name": data?.title || "Sportiva",
        "description": data?.description || "Spor salonu yönetim platformu",
        "url": data?.url || "https://sportiva.com",
        "isPartOf": {
          "@type": "WebSite",
          "name": "Sportiva",
          "url": "https://sportiva.com"
        }
      };
      break;

    case 'LoginAction':
      jsonLd = {
        "@context": "https://schema.org",
        "@type": "WebPage",
        "name": "Giriş Yap - Sportiva",
        "description": "Sportiva spor salonu yönetim platformuna giriş yapın",
        "url": "https://sportiva.com/login",
        "isPartOf": {
          "@type": "WebSite",
          "name": "Sportiva",
          "url": "https://sportiva.com"
        },
        "potentialAction": {
          "@type": "LoginAction",
          "target": "https://sportiva.com/login",
          "object": {
            "@type": "EntryPoint",
            "urlTemplate": "https://sportiva.com/login",
            "actionPlatform": [
              "http://schema.org/DesktopWebPlatform",
              "http://schema.org/MobileWebPlatform"
            ]
          }
        }
      };
      break;

    case 'RegisterAction':
      jsonLd = {
        "@context": "https://schema.org",
        "@type": "WebPage",
        "name": "Kayıt Ol - Sportiva",
        "description": "Sportiva spor salonu yönetim platformuna ücretsiz kayıt olun",
        "url": "https://sportiva.com/register",
        "isPartOf": {
          "@type": "WebSite",
          "name": "Sportiva",
          "url": "https://sportiva.com"
        },
        "potentialAction": {
          "@type": "RegisterAction",
          "target": "https://sportiva.com/register",
          "object": {
            "@type": "EntryPoint",
            "urlTemplate": "https://sportiva.com/register",
            "actionPlatform": [
              "http://schema.org/DesktopWebPlatform",
              "http://schema.org/MobileWebPlatform"
            ]
          }
        }
      };
      break;
  }

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
    />
  );
}

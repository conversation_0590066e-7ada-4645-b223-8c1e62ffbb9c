"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import {
  Check,
  Crown,
  Zap,
  Star,
  ArrowRight,
  Loader2,
  CheckCircle,
  ArrowUp,
  ArrowDown,
  Calendar,
  Users,
  Building,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useAuth } from "@/components/auth/auth-provider";
import {
  getAllPlatformPackages,
  getCurrentManagerSubscription,
} from "@/app/actions/subscription-actions";
import { Tables } from "@/lib/supabase/types";

type SubscriptionPlan = "monthly" | "quarterly" | "yearly";
type SubscriptionTier = "starter" | "professional" | "enterprise";
type PlatformPackage = Tables<"platform_packages">;

interface CurrentSubscription {
  tier: string;
  duration: string;
  packageName: string;
  maxGyms: number | null;
  maxMembers: number | null;
  features: Record<string, any>;
  subscriptionEndDate: string;
  subscriptionStartDate: string;
  status: string;
  priceAmount: number;
}

export function PricingClient() {
  const router = useRouter();
  const { authUser } = useAuth();

  const [billingPeriod, setBillingPeriod] =
    useState<SubscriptionPlan>("monthly");
  const [isYearly, setIsYearly] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [packages, setPackages] = useState<PlatformPackage[]>([]);
  const [currentSubscription, setCurrentSubscription] =
    useState<CurrentSubscription | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Load packages and current subscription on mount
  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Load all platform packages
        const packagesResponse = await getAllPlatformPackages();
        if (packagesResponse.success && packagesResponse.data) {
          setPackages(packagesResponse.data);
        } else {
          setError(packagesResponse.error || "Paketler yüklenemedi");
        }

        // Load current subscription if user is logged in
        if (authUser) {
          const subscriptionResponse = await getCurrentManagerSubscription(
            authUser.id
          );
          if (subscriptionResponse.success && subscriptionResponse.data) {
            setCurrentSubscription(subscriptionResponse.data);
          }
        }
      } catch (err: any) {
        setError(err.message || "Veriler yüklenirken bir hata oluştu");
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [authUser]);

  const handlePeriodChange = (yearly: boolean) => {
    setIsYearly(yearly);
    setBillingPeriod(yearly ? "yearly" : "monthly");
  };

  const getTierIcon = (tier: SubscriptionTier) => {
    switch (tier) {
      case "starter":
        return <Zap className="h-8 w-8" />;
      case "professional":
        return <Star className="h-8 w-8" />;
      case "enterprise":
        return <Crown className="h-8 w-8" />;
    }
  };

  const getTierColor = (tier: SubscriptionTier) => {
    switch (tier) {
      case "starter":
        return "text-blue-600";
      case "professional":
        return "text-green-600";
      case "enterprise":
        return "text-purple-600";
    }
  };

  const getTierBadgeColor = (tier: SubscriptionTier) => {
    switch (tier) {
      case "starter":
        return "bg-blue-100 text-blue-800";
      case "professional":
        return "bg-green-100 text-green-800";
      case "enterprise":
        return "bg-purple-100 text-purple-800";
    }
  };

  // Helper functions
  const getPackagesByTierAndDuration = (
    tier: SubscriptionTier,
    duration: SubscriptionPlan
  ) => {
    return packages.find(
      (pkg) => pkg.tier === tier && pkg.duration === duration
    );
  };

  const getDiscountPercentage = (
    tier: SubscriptionTier,
    plan: SubscriptionPlan
  ) => {
    const monthlyPackage = getPackagesByTierAndDuration(tier, "monthly");
    const planPackage = getPackagesByTierAndDuration(tier, plan);

    if (!monthlyPackage || !planPackage) return 0;

    const monthly = monthlyPackage.price_amount;
    const planPrice = planPackage.price_amount;

    if (plan === "quarterly") {
      const quarterlyMonthly = planPrice / 3;
      return Math.round(((monthly - quarterlyMonthly) / monthly) * 100);
    } else if (plan === "yearly") {
      const yearlyMonthly = planPrice / 12;
      return Math.round(((monthly - yearlyMonthly) / monthly) * 100);
    }
    return 0;
  };

  const isCurrentPackage = (
    tier: SubscriptionTier,
    duration: SubscriptionPlan
  ) => {
    return (
      currentSubscription?.tier === tier &&
      currentSubscription?.duration === duration
    );
  };

  const isUpgrade = (tier: SubscriptionTier) => {
    if (!currentSubscription) return false;
    const tierOrder = { starter: 1, professional: 2, enterprise: 3 };
    return (
      tierOrder[tier as keyof typeof tierOrder] >
      tierOrder[currentSubscription.tier as keyof typeof tierOrder]
    );
  };

  const isDowngrade = (tier: SubscriptionTier) => {
    if (!currentSubscription) return false;
    const tierOrder = { starter: 1, professional: 2, enterprise: 3 };
    return (
      tierOrder[tier as keyof typeof tierOrder] <
      tierOrder[currentSubscription.tier as keyof typeof tierOrder]
    );
  };

  const handleSelectPlan = (tier: SubscriptionTier) => {
    if (isCurrentPackage(tier, billingPeriod)) {
      return; // Already on this package
    }
    // URL'ye tier ve plan bilgilerini ekleyerek payment sayfasına yönlendir
    router.push(`/payment?tier=${tier}&plan=${billingPeriod}`);
  };

  const getButtonText = (tier: SubscriptionTier) => {
    if (isCurrentPackage(tier, billingPeriod)) {
      return "Mevcut Paketiniz";
    } else if (isUpgrade(tier)) {
      return "Yükselt";
    } else if (isDowngrade(tier)) {
      return "Düşür";
    } else {
      return "Başlayın";
    }
  };

  const getButtonIcon = (tier: SubscriptionTier) => {
    if (isCurrentPackage(tier, billingPeriod)) {
      return <CheckCircle className="ml-2 h-4 w-4" />;
    } else if (isUpgrade(tier)) {
      return <ArrowUp className="ml-2 h-4 w-4" />;
    } else if (isDowngrade(tier)) {
      return <ArrowDown className="ml-2 h-4 w-4" />;
    } else {
      return <ArrowRight className="ml-2 h-4 w-4" />;
    }
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 flex items-center justify-center">
        <Card className="w-full max-w-md bg-white dark:bg-slate-800 border-slate-200 dark:border-slate-700">
          <CardContent className="flex flex-col items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
            <p className="text-muted-foreground">Paketler yükleniyor...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 flex items-center justify-center">
        <Card className="w-full max-w-md bg-white dark:bg-slate-800 border-slate-200 dark:border-slate-700">
          <CardContent className="flex flex-col items-center justify-center py-8">
            <div className="h-12 w-12 rounded-full bg-red-100 dark:bg-red-900/30 flex items-center justify-center mb-4">
              <span className="text-red-600 dark:text-red-400 text-xl">⚠️</span>
            </div>
            <p className="text-red-600 dark:text-red-400 text-center">
              {error}
            </p>
            <Button
              variant="outline"
              onClick={() => window.location.reload()}
              className="mt-4"
            >
              Tekrar Dene
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
      {/* Current Subscription Banner */}
      {currentSubscription && (
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
          <div className="container mx-auto px-4 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="h-10 w-10 rounded-full bg-white/20 flex items-center justify-center">
                  {getTierIcon(currentSubscription.tier as SubscriptionTier)}
                </div>
                <div>
                  <p className="font-semibold">
                    Mevcut Paketiniz: {currentSubscription.packageName}
                  </p>
                  <p className="text-sm opacity-90">
                    {currentSubscription.maxGyms
                      ? `${currentSubscription.maxGyms} salon`
                      : "Sınırsız salon"}{" "}
                    •
                    {currentSubscription.maxMembers
                      ? ` ${currentSubscription.maxMembers} üye`
                      : " Sınırsız üye"}
                  </p>
                </div>
              </div>
              <div className="text-right">
                <div className="flex items-center gap-2 text-sm opacity-90">
                  <Calendar className="h-4 w-4" />
                  <span>
                    {new Date(
                      currentSubscription.subscriptionEndDate
                    ).toLocaleDateString("tr-TR")}{" "}
                    tarihine kadar
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Header */}
      <div className="bg-white dark:bg-slate-900 border-b border-slate-200 dark:border-slate-700">
        <div className="container mx-auto px-4 py-6">
          <div className="text-center space-y-4">
            <h1 className="text-4xl font-bold text-gray-900 dark:text-white">
              {currentSubscription
                ? "Paketinizi Yükseltin veya Değiştirin"
                : "Size Uygun Paketi Seçin"}
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              {currentSubscription
                ? "Mevcut paketinizi yükseltebilir veya farklı bir ödeme döngüsü seçebilirsiniz."
                : "Salonunuzun büyüklüğüne ve ihtiyaçlarına göre tasarlanmış paketlerimizle işletmenizi dijitalleştirin."}
            </p>
          </div>
        </div>
      </div>

      {/* Billing Period Toggle */}
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center space-x-4 mb-12">
          <Label
            htmlFor="billing-toggle"
            className="text-sm font-medium text-gray-700 dark:text-gray-300"
          >
            Aylık
          </Label>
          <Switch
            id="billing-toggle"
            checked={isYearly}
            onCheckedChange={handlePeriodChange}
          />
          <Label
            htmlFor="billing-toggle"
            className="text-sm font-medium text-gray-700 dark:text-gray-300"
          >
            Yıllık
          </Label>
          {isYearly && (
            <Badge
              variant="secondary"
              className="ml-2 bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
            >
              2 aya kadar tasarruf edin!
            </Badge>
          )}
        </div>

        {/* Dynamic Pricing Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-7xl mx-auto">
          {(
            ["starter", "professional", "enterprise"] as SubscriptionTier[]
          ).map((tier) => {
            const packageData = getPackagesByTierAndDuration(
              tier,
              billingPeriod
            );
            const isCurrentPkg = isCurrentPackage(tier, billingPeriod);
            const isUpgradePkg = isUpgrade(tier);
            const isDowngradePkg = isDowngrade(tier);

            if (!packageData) return null;

            return (
              <Card
                key={`${tier}-${billingPeriod}`}
                className={cn(
                  "relative hover:shadow-lg transition-all duration-200 bg-white dark:bg-slate-800",
                  isCurrentPkg && "ring-2 ring-blue-500 dark:ring-blue-400",
                  tier === "professional" &&
                    !isCurrentPkg &&
                    "border-2 border-green-500 dark:border-green-400 scale-105",
                  isDowngradePkg && "opacity-75"
                )}
              >
                {/* Current Package Badge */}
                {isCurrentPkg && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-blue-500 dark:bg-blue-600 text-white px-4 py-1">
                      Mevcut Paketiniz
                    </Badge>
                  </div>
                )}

                {/* Recommended Badge */}
                {tier === "professional" && !isCurrentPkg && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-green-500 dark:bg-green-600 text-white px-4 py-1">
                      Önerilen
                    </Badge>
                  </div>
                )}

                <CardHeader className="text-center pb-2">
                  <div className={cn("mx-auto mb-4", getTierColor(tier))}>
                    {getTierIcon(tier)}
                  </div>
                  <CardTitle className="text-2xl text-gray-900 dark:text-white capitalize">
                    {packageData.name}
                  </CardTitle>
                  <CardDescription className="text-gray-600 dark:text-gray-300">
                    {tier === "starter" && "Yeni başlayan salonlar için"}
                    {tier === "professional" && "Büyüyen salonlar için"}
                    {tier === "enterprise" && "Büyük işletmeler için"}
                  </CardDescription>

                  {/* Upgrade/Downgrade Indicators */}
                  {isUpgradePkg && (
                    <Badge className="w-fit mx-auto mt-2 bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300">
                      <ArrowUp className="h-3 w-3 mr-1" />
                      Yükseltme
                    </Badge>
                  )}
                  {isDowngradePkg && (
                    <Badge className="w-fit mx-auto mt-2 bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300">
                      <ArrowDown className="h-3 w-3 mr-1" />
                      Düşürme
                    </Badge>
                  )}
                </CardHeader>

                <CardContent className="text-center">
                  <div className="mb-6">
                    <div className="text-4xl font-bold text-gray-900 dark:text-white">
                      ₺{packageData.price_amount}
                    </div>
                    <div className="text-sm text-muted-foreground dark:text-gray-400">
                      {billingPeriod === "monthly" && "/ay"}
                      {billingPeriod === "quarterly" && "/3 ay"}
                      {billingPeriod === "yearly" && "/yıl"}
                    </div>
                    {billingPeriod !== "monthly" && (
                      <div className="text-sm text-green-600 dark:text-green-400 font-medium">
                        %{getDiscountPercentage(tier, billingPeriod)} tasarruf
                      </div>
                    )}
                  </div>

                  {/* Package Features */}
                  <ul className="space-y-3 text-left">
                    <li className="flex items-center text-sm text-gray-700 dark:text-gray-300">
                      <Building className="h-4 w-4 mr-3 text-green-500 dark:text-green-400 flex-shrink-0" />
                      <span>
                        {packageData.max_gyms
                          ? `${packageData.max_gyms} salon`
                          : "Sınırsız salon"}
                      </span>
                    </li>
                    <li className="flex items-center text-sm text-gray-700 dark:text-gray-300">
                      <Users className="h-4 w-4 mr-3 text-green-500 dark:text-green-400 flex-shrink-0" />
                      <span>
                        {packageData.max_members
                          ? `${packageData.max_members} üyeye kadar`
                          : "Sınırsız üye"}
                      </span>
                    </li>
                    {packageData.features &&
                      Array.isArray(packageData.features) &&
                      packageData.features.map(
                        (feature: string, index: number) => (
                          <li
                            key={index}
                            className="flex items-center text-sm text-gray-700 dark:text-gray-300"
                          >
                            <Check className="h-4 w-4 mr-3 text-green-500 dark:text-green-400 flex-shrink-0" />
                            <span>{feature}</span>
                          </li>
                        )
                      )}
                  </ul>
                </CardContent>

                <CardFooter>
                  <Button
                    className={cn(
                      "w-full",
                      isCurrentPkg && "bg-blue-600 hover:bg-blue-700",
                      tier === "professional" &&
                        !isCurrentPkg &&
                        "bg-green-600 hover:bg-green-700",
                      tier === "enterprise" &&
                        !isCurrentPkg &&
                        "bg-purple-600 hover:bg-purple-700",
                      isDowngradePkg && "bg-orange-600 hover:bg-orange-700"
                    )}
                    onClick={() => handleSelectPlan(tier)}
                    disabled={isCurrentPkg}
                  >
                    {getButtonText(tier)}
                    {getButtonIcon(tier)}
                  </Button>
                </CardFooter>
              </Card>
            );
          })}
        </div>
      </div>
    </div>
  );
}

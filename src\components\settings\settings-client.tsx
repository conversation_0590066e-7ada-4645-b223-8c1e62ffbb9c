"use client";
import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import { ProfileForm } from "@/components/settings/profile-form";
import { SecurityForm } from "@/components/settings/security-form";
import { SubscriptionStatus } from "@/components/settings/subscription-status";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useRouter } from "next/navigation";
import { Users, Gyms } from "@/lib/supabase/types";
import {
  <PERSON>,
  User,
  Lock,
  CreditCard,
  Settings as SettingsIcon,
  ChevronLeft,
  MapPin,
  Calendar,
  ExternalLink,
  UserCircle2,
  Shield,
  Loader2,
} from "lucide-react";
import Link from "next/link";
import { format } from "date-fns";
import { tr } from "date-fns/locale";

interface SettingsClientProps {
  initialProfile: Users | null;
  initialOwnedGyms: Gyms[];
  initialMemberships: any[];
  authUser: any;
  serverError: string | null;
}

export default function SettingsClient({
  initialProfile,
  initialOwnedGyms,
  initialMemberships,
  authUser,
  serverError,
}: SettingsClientProps) {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("profile");

  const [profile, setProfile] = useState<Users | null>(initialProfile);
  const [ownedGyms, setOwnedGyms] = useState<Gyms[]>(initialOwnedGyms);
  const [memberships, setMemberships] = useState<any[]>(initialMemberships);

  const [isLoading, setIsLoading] = useState(!initialProfile && !serverError);
  const [error, setError] = useState<string | null>(serverError);

  useEffect(() => {
    setProfile(initialProfile);
  }, [initialProfile]);

  useEffect(() => {
    setOwnedGyms(initialOwnedGyms);
  }, [initialOwnedGyms]);

  useEffect(() => {
    setMemberships(initialMemberships);
  }, [initialMemberships]);

  useEffect(() => {
    setError(serverError);
  }, [serverError]);

  useEffect(() => {
    if (initialProfile || serverError) {
      setIsLoading(false);
    }
  }, [initialProfile, serverError]);

  if (isLoading) {
    return (
      <div className="container max-w-6xl py-12 mx-auto min-h-[70vh] flex flex-col items-center justify-center">
        <Loader2 className="h-12 w-12 animate-spin text-primary mb-4" />
        <p className="text-muted-foreground">Ayarlar yükleniyor...</p>
      </div>
    );
  }

  if (error && !initialProfile) {
    return (
      <div className="container py-12 mx-auto max-w-3xl">
        <div className="flex justify-center mb-6">
          <Button
            variant="outline"
            size="lg"
            className="flex items-center gap-2 shadow-sm"
            onClick={() => router.push("/dashboard")}
          >
            <ChevronLeft className="h-4 w-4" />
            Dashboard'a Dön
          </Button>
        </div>

        <Card className="border-destructive/20 shadow-md overflow-hidden">
          <div className="bg-destructive/10 p-2"></div>
          <CardHeader>
            <div className="flex items-center gap-2">
              <Shield className="h-5 w-5 text-destructive" />
              <CardTitle>Bir Hata Oluştu</CardTitle>
            </div>
            <CardDescription>
              Ayarlarınız yüklenirken bir sorun meydana geldi.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="bg-muted/50 rounded-lg p-4 border text-sm text-destructive">
              {error}
            </div>
          </CardContent>
          <CardFooter className="bg-muted/30 flex justify-end gap-2 border-t">
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.push("/dashboard")}
            >
              Dashboard'a Dön
            </Button>
            <Button
              variant="default"
              size="sm"
              onClick={() => window.location.reload()}
            >
              Tekrar Dene
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  if (!authUser || !profile) {
    return (
      <div className="container max-w-6xl py-12 mx-auto min-h-[70vh] flex flex-col items-center justify-center">
        <UserCircle2 className="h-16 w-16 text-muted-foreground mb-4" />
        <p className="text-xl font-medium">Erişim Sorunu</p>
        <p className="text-muted-foreground mt-2 text-center">
          Profil bilgilerinize ulaşılamadı veya oturumunuz geçerli değil.
          <br />
          Lütfen tekrar giriş yapmayı deneyin.
        </p>
        <Button onClick={() => router.push("/auth/login")} className="mt-6">
          Giriş Sayfasına Git
        </Button>
      </div>
    );
  }

  const isManager = !!profile?.is_manager;

  return (
    <div className="min-h-screen bg-muted/20">
      {/* Header */}
      <div className="bg-background border-b sticky top-0 z-10 shadow-sm">
        <div className="container mx-auto py-4">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 md:gap-6">
            <div className="flex items-center gap-5">
              <Button
                variant="ghost"
                size="icon"
                className="md:hidden rounded-full bg-background border hover:bg-muted/50 shadow-sm"
                onClick={() =>
                  router.push(
                    isManager ? "/dashboard/manager" : "/dashboard/member"
                  )
                }
              >
                <ChevronLeft className="h-5 w-5" />
              </Button>

              <Avatar className="h-16 w-16 border-2 border-primary/10 shadow-sm">
                <AvatarImage src={profile?.profile_picture_url || undefined} />
                <AvatarFallback className="text-xl font-semibold bg-primary/10">
                  {profile?.name?.[0]?.toUpperCase()}
                  {profile?.surname?.[0]?.toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <div className="space-y-0.5">
                <h1 className="text-2xl font-bold tracking-tight">
                  {profile?.name} {profile?.surname}
                </h1>
                <div className="flex items-center flex-wrap gap-2 text-muted-foreground">
                  <span className="text-sm">{profile?.email}</span>
                  {isManager && (
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Badge variant="secondary" className="font-medium">
                            <Building className="h-3 w-3 mr-1" />
                            Salon Yöneticisi
                          </Badge>
                        </TooltipTrigger>
                        <TooltipContent side="bottom">
                          <p className="text-xs">
                            Salon yönetme yetkisine sahipsiniz
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  )}
                </div>
              </div>
            </div>

            <div className="self-end md:self-auto w-full md:w-auto">
              <Button
                variant="outline"
                size="sm"
                className="hidden md:flex items-center shadow-sm gap-1.5 hover:bg-muted/50"
                onClick={() =>
                  router.push(
                    isManager ? "/dashboard/manager" : "/dashboard/member"
                  )
                }
              >
                <ChevronLeft className="h-4 w-4" />
                Dashboard'a Dön
              </Button>

              <Tabs
                value={activeTab}
                onValueChange={setActiveTab}
                className="w-full md:hidden"
              >
                <TabsList className="w-full h-auto grid grid-cols-3 gap-x-2 p-1 bg-muted/50">
                  <TabsTrigger value="profile" className="text-xs">
                    <User className="h-3 w-3 mr-1" />
                    Profil
                  </TabsTrigger>
                  <TabsTrigger value="security" className="text-xs">
                    <Lock className="h-3 w-3 mr-1" />
                    Güvenlik
                  </TabsTrigger>
                  <TabsTrigger value="memberships" className="text-xs">
                    <UserCircle2 className="h-3 w-3 mr-1" />
                    Üyelikler
                  </TabsTrigger>
                </TabsList>
              </Tabs>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto py-8 px-4 md:px-6">
        <div className="grid grid-cols-1 md:grid-cols-4 lg:grid-cols-5 gap-6">
          {/* Sidebar */}
          <div className="hidden md:block md:col-span-1 lg:col-span-1">
            <div className="bg-card rounded-lg border shadow-sm overflow-hidden sticky top-28">
              <div className="p-4 bg-muted/30 border-b">
                <h3 className="font-medium">Ayarlar</h3>
              </div>
              <ScrollArea className="h-[calc(100vh-11rem)]">
                <div className="p-1">
                  <Tabs
                    value={activeTab}
                    orientation="vertical"
                    onValueChange={setActiveTab}
                    className="w-full"
                  >
                    <TabsList className="flex flex-col justify-start h-auto gap-0.5 bg-transparent p-0">
                      <TabsTrigger
                        value="profile"
                        className="justify-start px-3 py-2.5 rounded-md data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                      >
                        <User className="h-4 w-4 mr-2" />
                        Profil
                      </TabsTrigger>
                      <TabsTrigger
                        value="security"
                        className="justify-start px-3 py-2.5 rounded-md data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                      >
                        <Lock className="h-4 w-4 mr-2" />
                        Güvenlik
                      </TabsTrigger>
                      <TabsTrigger
                        value="memberships"
                        className="justify-start px-3 py-2.5 rounded-md data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                      >
                        <UserCircle2 className="h-4 w-4 mr-2" />
                        Üyeliklerim
                      </TabsTrigger>
                      {isManager && (
                        <TabsTrigger
                          value="gyms"
                          className="justify-start px-3 py-2.5 rounded-md data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                        >
                          <Building className="h-4 w-4 mr-2" />
                          Salonlarım
                        </TabsTrigger>
                      )}
                      <TabsTrigger
                        value="subscription"
                        className="justify-start px-3 py-2.5 rounded-md data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                      >
                        <CreditCard className="h-4 w-4 mr-2" />
                        Abonelik
                      </TabsTrigger>
                      {isManager && (
                        <TabsTrigger
                          value="manager"
                          className="justify-start px-3 py-2.5 rounded-md data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                        >
                          <SettingsIcon className="h-4 w-4 mr-2" />
                          Yönetici Ayarları
                        </TabsTrigger>
                      )}
                    </TabsList>
                  </Tabs>
                </div>
              </ScrollArea>
            </div>
          </div>

          {/* Content Area */}
          <div className="md:col-span-3 lg:col-span-4 space-y-6">
            <Tabs
              value={activeTab}
              onValueChange={setActiveTab}
              className="md:hidden mb-6"
            >
              <TabsList className="w-full h-auto grid grid-cols-3 gap-x-2">
                <TabsTrigger value="profile">
                  <User className="h-4 w-4 mr-2" />
                  Profil
                </TabsTrigger>
                <TabsTrigger value="security">
                  <Lock className="h-4 w-4 mr-2" />
                  Güvenlik
                </TabsTrigger>
                <TabsTrigger value="memberships">
                  <UserCircle2 className="h-4 w-4 mr-2" />
                  Üyeliklerim
                </TabsTrigger>
              </TabsList>
            </Tabs>

            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsContent value="profile" className="m-0 space-y-4">
                <Card className="shadow-sm border overflow-hidden">
                  <div className="bg-gradient-to-r from-primary/10 to-primary/5 h-2" />
                  <CardHeader>
                    <div className="flex items-center gap-2">
                      <User className="h-5 w-5 text-primary" />
                      <CardTitle className="text-xl">
                        Profil Bilgileri
                      </CardTitle>
                    </div>
                    <CardDescription>
                      Kişisel bilgilerinizi güncelleyin. Bu bilgiler
                      profilinizde görünecektir.
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ProfileForm
                      userId={authUser?.id || ""}
                      initialData={profile || undefined}
                    />
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="security" className="m-0 space-y-4">
                <Card className="shadow-sm border overflow-hidden">
                  <div className="bg-gradient-to-r from-primary/10 to-primary/5 h-2" />
                  <CardHeader>
                    <div className="flex items-center gap-2">
                      <Lock className="h-5 w-5 text-primary" />
                      <CardTitle className="text-xl">
                        Güvenlik Ayarları
                      </CardTitle>
                    </div>
                    <CardDescription>
                      Şifrenizi değiştirin ve hesap güvenliğinizi yönetin.
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <SecurityForm
                      userId={authUser?.id || ""}
                      email={authUser?.email || ""}
                    />
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="memberships" className="m-0 space-y-4">
                <Card className="shadow-sm border overflow-hidden">
                  <div className="bg-gradient-to-r from-primary/10 to-primary/5 h-2" />
                  <CardHeader>
                    <div className="flex items-center gap-2">
                      <UserCircle2 className="h-5 w-5 text-primary" />
                      <CardTitle className="text-xl">Üyeliklerim</CardTitle>
                    </div>
                    <CardDescription>
                      Üye olduğunuz salonlar ve üyelik durumlarınız.
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="px-0">
                    {memberships.length === 0 ? (
                      <div className="text-center py-12 px-4">
                        <div className="bg-muted/50 rounded-full p-4 w-20 h-20 mx-auto mb-5">
                          <UserCircle2 className="h-12 w-12 mx-auto text-primary/60" />
                        </div>
                        <h3 className="text-lg font-medium mb-2">
                          Henüz üyeliğiniz bulunmuyor
                        </h3>
                        <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                          Salonlara üye olarak antrenman programlarınızı takip
                          edebilir ve spor aktivitelerinizi yönetebilirsiniz.
                        </p>
                        <Button asChild className="shadow-sm">
                          <Link href="/findGym">
                            <Building className="h-4 w-4 mr-2" />
                            Salon Bul
                          </Link>
                        </Button>
                      </div>
                    ) : (
                      <div className="divide-y">
                        {memberships.map((membership) => (
                          <div
                            key={membership.id}
                            className="p-5 hover:bg-muted/30 transition-colors"
                          >
                            <div className="flex items-start justify-between flex-col sm:flex-row gap-4">
                              <div className="flex items-start gap-4">
                                <Avatar className="h-14 w-14 border shadow-sm">
                                  <AvatarImage
                                    src={membership.gym?.logo_url || undefined}
                                  />
                                  <AvatarFallback className="bg-primary/10 font-semibold">
                                    {membership.gym?.name
                                      ?.charAt(0)
                                      ?.toUpperCase()}
                                  </AvatarFallback>
                                </Avatar>
                                <div>
                                  <h3 className="font-semibold text-base">
                                    {membership.gym?.name}
                                  </h3>
                                  {membership.gym?.address && (
                                    <div className="flex items-start gap-1 text-xs text-muted-foreground mt-1">
                                      <MapPin className="h-3 w-3 mt-0.5 flex-shrink-0" />
                                      <span>
                                        {membership.gym?.address},{" "}
                                        {membership.gym?.city?.name}
                                      </span>
                                    </div>
                                  )}
                                  <div className="flex items-center flex-wrap gap-2 mt-2">
                                    <Badge
                                      variant={
                                        membership.status === "active"
                                          ? "default"
                                          : membership.status ===
                                            "approved_passive"
                                          ? "secondary"
                                          : "outline"
                                      }
                                      className="text-xs font-medium"
                                    >
                                      {membership.status === "active"
                                        ? "Aktif Üyelik"
                                        : membership.status ===
                                          "approved_passive"
                                        ? "Pasif Üyelik"
                                        : "Onay Bekliyor"}
                                    </Badge>
                                    {membership.created_at && (
                                      <span className="text-xs text-muted-foreground flex items-center">
                                        <Calendar className="h-3 w-3 mr-1" />
                                        {format(
                                          new Date(membership.created_at),
                                          "d MMM yyyy",
                                          { locale: tr }
                                        )}
                                      </span>
                                    )}
                                  </div>
                                </div>
                              </div>
                              <Button
                                variant="outline"
                                size="sm"
                                asChild
                                className="shadow-sm"
                              >
                                <Link href={`/gyms/${membership.gym?.id}`}>
                                  <ExternalLink className="h-3.5 w-3.5 mr-1.5" />
                                  Salona Git
                                </Link>
                              </Button>
                            </div>

                            {membership.subscriptions?.length > 0 && (
                              <div className="mt-4 ml-0 sm:ml-[72px]">
                                <h4 className="text-sm font-medium mb-2">
                                  Aktif Paketler
                                </h4>
                                <div className="space-y-2">
                                  {membership.subscriptions.map((sub: any) => (
                                    <div
                                      key={sub.id}
                                      className="text-xs bg-muted/50 p-3 rounded-md border shadow-sm"
                                    >
                                      <div className="font-medium text-sm">
                                        {sub.gym_package.name}
                                      </div>
                                      <div className="text-muted-foreground mt-1.5 flex flex-wrap items-center gap-2">
                                        <span className="bg-background px-2 py-1 rounded-full text-[10px] flex items-center">
                                          <Calendar className="h-2.5 w-2.5 mr-1" />
                                          Bitiş:{" "}
                                          {format(
                                            new Date(sub.end_date),
                                            "d MMM yyyy",
                                            { locale: tr }
                                          )}
                                        </span>
                                        <span className="bg-background px-2 py-1 rounded-full text-[10px] flex items-center">
                                          <CreditCard className="h-2.5 w-2.5 mr-1" />
                                          Fiyat: ₺{sub.gym_package.price}
                                        </span>
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              {isManager && (
                <TabsContent value="gyms" className="m-0 space-y-4">
                  <Card className="shadow-sm border overflow-hidden">
                    <div className="bg-gradient-to-r from-primary/10 to-primary/5 h-2" />
                    <CardHeader>
                      <div className="flex items-center gap-2">
                        <Building className="h-5 w-5 text-primary" />
                        <CardTitle className="text-xl">Salonlarım</CardTitle>
                      </div>
                      <CardDescription>
                        Yönettiğiniz salonlar ve durumları.
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="px-0">
                      {ownedGyms.length === 0 ? (
                        <div className="text-center py-12 px-4">
                          <div className="bg-muted/50 rounded-full p-4 w-20 h-20 mx-auto mb-5">
                            <Building className="h-12 w-12 mx-auto text-primary/60" />
                          </div>
                          <h3 className="text-lg font-medium mb-2">
                            Henüz salon eklenmemiş
                          </h3>
                          <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                            İlk salonunuzu ekleyin ve yönetmeye başlayın.
                          </p>
                          <Button asChild className="shadow-sm">
                            <Link href="/dashboard/manager/gyms/new">
                              <Building className="h-4 w-4 mr-2" />
                              Salon Ekle
                            </Link>
                          </Button>
                        </div>
                      ) : (
                        <div className="divide-y">
                          {ownedGyms.map((gym) => (
                            <div
                              key={gym.id}
                              className="p-5 hover:bg-muted/30 transition-colors"
                            >
                              <div className="flex items-start justify-between flex-col sm:flex-row gap-4">
                                <div className="flex items-start gap-4">
                                  <Avatar className="h-14 w-14 border shadow-sm">
                                    <AvatarImage
                                      src={gym.logo_url || undefined}
                                    />
                                    <AvatarFallback className="bg-primary/10 font-semibold">
                                      {gym.name?.charAt(0)?.toUpperCase()}
                                    </AvatarFallback>
                                  </Avatar>
                                  <div>
                                    <h3 className="font-semibold text-base">
                                      {gym.name}
                                    </h3>
                                    {gym.address && (
                                      <div className="flex items-start gap-1 text-xs text-muted-foreground mt-1">
                                        <MapPin className="h-3 w-3 mt-0.5 flex-shrink-0" />
                                        <span>
                                          {gym.address}, {gym.city}
                                        </span>
                                      </div>
                                    )}
                                    <div className="flex items-center flex-wrap gap-2 mt-2">
                                      <Badge
                                        variant={
                                          gym.status === "active"
                                            ? "default"
                                            : "outline"
                                        }
                                        className="text-xs font-medium"
                                      >
                                        {gym.status === "active"
                                          ? "Aktif"
                                          : "İnaktif"}
                                      </Badge>
                                      {gym.created_at && (
                                        <span className="text-xs text-muted-foreground flex items-center">
                                          <Calendar className="h-3 w-3 mr-1" />
                                          {format(
                                            new Date(gym.created_at),
                                            "d MMM yyyy",
                                            { locale: tr }
                                          )}
                                        </span>
                                      )}
                                    </div>
                                  </div>
                                </div>
                                <div className="flex gap-2 w-full sm:w-auto">
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    asChild
                                    className="flex-1 sm:flex-none shadow-sm"
                                  >
                                    <Link
                                      href={`/dashboard/manager/gyms/${gym.id}`}
                                    >
                                      <SettingsIcon className="h-3.5 w-3.5 mr-1.5" />
                                      Düzenle
                                    </Link>
                                  </Button>
                                  <Button
                                    variant="default"
                                    size="sm"
                                    asChild
                                    className="flex-1 sm:flex-none shadow-sm"
                                  >
                                    <Link href={`/dashboard/manager/${gym.id}`}>
                                      <ExternalLink className="h-3.5 w-3.5 mr-1.5" />
                                      Dashboard
                                    </Link>
                                  </Button>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>
              )}

              <TabsContent value="subscription" className="m-0 space-y-4">
                <Card className="shadow-sm border overflow-hidden">
                  <div className="bg-gradient-to-r from-primary/10 to-primary/5 h-2" />
                  <CardHeader>
                    <div className="flex items-center gap-2">
                      <CreditCard className="h-5 w-5 text-primary" />
                      <CardTitle className="text-xl">
                        Abonelik Ayarları
                      </CardTitle>
                    </div>
                    <CardDescription>
                      Sportiva uygulaması abonelik bilgileriniz ve
                      faturalarınız.
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <SubscriptionStatus
                      userId={authUser?.id || ""}
                      isManager={isManager}
                    />
                  </CardContent>
                </Card>
              </TabsContent>

              {isManager && (
                <TabsContent value="manager" className="m-0 space-y-4">
                  <Card className="shadow-sm border overflow-hidden">
                    <div className="bg-gradient-to-r from-primary/10 to-primary/5 h-2" />
                    <CardHeader>
                      <div className="flex items-center gap-2">
                        <SettingsIcon className="h-5 w-5 text-primary" />
                        <CardTitle className="text-xl">Bildirimler</CardTitle>
                      </div>
                      <CardDescription>
                        Bildirim ayarlarınızı yönetin.
                      </CardDescription>
                    </CardHeader>
                  </Card>
                </TabsContent>
              )}
            </Tabs>
          </div>
        </div>
      </div>
    </div>
  );
}

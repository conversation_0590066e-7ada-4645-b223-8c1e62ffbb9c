"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Search, UserPlus } from "lucide-react";
import { MemberWithDetails } from "./members-page-client";

interface MemberFiltersProps {
  searchTerm: string;
  onSearchChange: (value: string) => void;
  selectedTab: string;
  onTabChange: (value: string) => void;
  membersByStatus: {
    all: MemberWithDetails[];
    active: MemberWithDetails[];
    passive: MemberWithDetails[];
  };
  onAddMember: () => void;
}

export function MemberFilters({
  searchTerm,
  onSearchChange,
  selectedTab,
  onTabChange,
  membersByStatus,
  onAddMember,
}: MemberFiltersProps) {
  return (
    <div className="space-y-4">
      {/* Add Member Button */}
      <div className="flex justify-end">
        <Button onClick={onAddMember}>
          <UserPlus className="mr-2 h-4 w-4" />
          <PERSON>ye <PERSON>kle
        </Button>
      </div>

      {/* Tabs */}
      <Tabs value={selectedTab} onValueChange={onTabChange}>
        <TabsList className="flex flex-wrap">
          <TabsTrigger value="all">
            Tümü
            {membersByStatus.all.length > 0 && (
              <Badge variant="outline" className="ml-2">
                {membersByStatus.all.length}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="active">
            Aktif Üyeler
            {membersByStatus.active.length > 0 && (
              <Badge variant="outline" className="ml-2">
                {membersByStatus.active.length}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="passive">
            Pasif Üyeler
            {membersByStatus.passive.length > 0 && (
              <Badge variant="outline" className="ml-2">
                {membersByStatus.passive.length}
              </Badge>
            )}
          </TabsTrigger>
        </TabsList>
      </Tabs>

      {/* Search */}
      <div className="flex items-center gap-2">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Üye ara..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => onSearchChange(e.target.value)}
          />
        </div>
        <Button variant="outline">Filtrele</Button>
      </div>
    </div>
  );
}
